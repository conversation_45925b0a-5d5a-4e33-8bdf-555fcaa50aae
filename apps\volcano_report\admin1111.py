from django.contrib.admin import AdminSite, ModelAdmin
from django.contrib import admin

from django.contrib.auth.models import User, Group
from import_export import resources
from import_export.admin import ImportExportModelAdmin
from .models import UDTGetQueryCondition, UDTGetQueryConditiondetail, UdtSMTScrapRecord

print("ModelAdmin type:", type(ModelAdmin))
print("ModelAdmin value:", ModelAdmin)


class CustomAdminSite(AdminSite):
    def has_permission(self, request):
        return request.user.is_active and request.user.is_staff

custom_admin_site = CustomAdminSite(name='customadmin')

@custom_admin_site.register(UDTGetQueryCondition)
class UDTGetQueryConditionAdmin(ModelAdmin):
    list_display = ('ID', 'Reason', 'UDP')
    list_filter = ('Reason',)
    search_fields = ('Reason', 'UDP')
    ordering = ('-ID',)

@custom_admin_site.register(UDTGetQueryConditiondetail)
class UDTGetQueryConditiondetailAdmin(ModelAdmin):
    list_display = ('ID', 'Reason', 'UDP', 'SNTextarea',
                    'PartnumberTextarea', 'Partfamily', 'StationType',
                    'LineName', 'TimeInput', 'MPN', 'LotCode', 'DateCode',
                    'Supplier', 'CreateTime', 'Remark')
    list_filter = ('Reason', 'UDP')
    search_fields = ('Reason',)
    ordering = ('-ID',)

class UdtSMTScrapRecordResource(resources.ModelResource):
    def before_import_row(self, row, **kwargs):
        row['creator'] = kwargs['user'].username

    class Meta:
        model = UdtSMTScrapRecord
        fields = ('date', 'serialnumber', 'Partnumber', 'reporting_unit_code', 'PONumber', 'reporting_reason', 'recorder')
        export_order = fields
        import_id_fields = ('serialnumber',)
        exclude = ('id',)

@custom_admin_site.register(UdtSMTScrapRecord)
class UdtSMTScrapRecordAdmin(ImportExportModelAdmin):
    resource_class = UdtSMTScrapRecordResource

    def get_import_resource_kwargs(self, request, *args, **kwargs):
        resource_kwargs = super().get_import_resource_kwargs(request, *args, **kwargs)
        resource_kwargs['user'] = request.user
        return resource_kwargs

    list_display = ['ID', 'date', 'serialnumber', 'Partnumber', 'PONumber', 'reporting_unit_code', 'reporting_reason', 'recorder', 'createtime', 'creator']
    search_fields = ['serialnumber', 'Partnumber', 'PONumber']
    list_filter = ['date', 'reporting_unit_code']
    exclude = ('creator',)

    def get_form(self, request, obj=None, **kwargs):
        form = super().get_form(request, obj, **kwargs)
        form.current_user = request.user
        return form

    def save_model(self, request, obj, form, change):
        if not change:  # 如果是新创建的对象
            obj.creator = request.user.username
        super().save_model(request, obj, form, change)

    class Media:
        js = ('admin/js/jquery.init.js', 'js/admin_form.js',)

# 如果需要，也可以注册 User 和 Group 模型
custom_admin_site.register(User)
custom_admin_site.register(Group)

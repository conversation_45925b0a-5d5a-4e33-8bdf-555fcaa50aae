{% extends 'base1.html' %}
{% load static %}

{% block head %}
<!-- <link rel="stylesheet" href="{% static 'css/your-styles.css' %}"> -->
<!-- <link href="https://cdn.datatables.net/rowgroup/1.3.1/css/rowGroup.dataTables.min.css" rel="stylesheet">
<link href="https://cdn.bootcdn.net/ajax/libs/layer/3.5.1/theme/default/layer.css" rel="stylesheet">
<script src="https://cdn.bootcdn.net/ajax/libs/layer/3.5.1/layer.min.js"></script> -->
{% endblock %}

{% block content %}
<div class="app-container">
    <h1 class="page-title">MTG&HP产出确认</h1>

    <div class="form-section">
        <form method="post" id="queryForm">
            {% csrf_token %}
            <div class="form-layout">
                <div class="form-group">
                    <label for="project_type">项目类型</label>
                    <select class="form-control" id="project_type" name="project_type" required>
                        <option value="MTG">MTG</option>
                        <option value="HP">HP</option>
                    </select>
                </div>
                <div class="form-group">
                    <label for="start_time">开始时间</label>
                    <input type="datetime-local" class="form-control" id="start_time" name="start_time" required>
                </div>
                <div class="form-group">
                    <label for="end_time">结束时间</label>
                    <input type="datetime-local" class="form-control" id="end_time" name="end_time" required>
                </div>
            </div>

            <div class="action-buttons">
                <button type="submit" class="btn btn-primary">查询</button>
                <button type="button" class="btn btn-info" id="exportServerBtn">导出Excel</button>
            </div>
        </form>
    </div>

    <!-- 在表格上方添加类型过滤按钮组 -->
    <div class="type-filter-section mt-3 mb-3">
        <div class="btn-group type-filter-buttons" role="group">
            <button type="button" class="btn btn-outline-primary active" data-type="all">全部</button>
            <!-- 按钮将通过 JavaScript 动态添加 -->
        </div>
        
        <!-- 添加线别筛选下拉框 -->
        <div class="line-filter mt-3">
            <select class="form-control" id="lineFilter">
                <option value="">所有线别</option>
                <!-- 选项将通过 JavaScript 动态添加 -->
            </select>
        </div>
    </div>

    <!-- 数据列表 -->
    <div class="table-section mt-4">
        <table class="table table-striped" id="outputTable">
            <thead>
                <tr>
                    <th>Type</th>
                    <th>Line</th>
                    <th>PartNumber</th>
                    <th>Station</th>
                    <th>Date</th>
                    <th>Shift</th>
                    <th>Output</th>
                    <th>PlanQty</th>
                    <th>Gap</th>
                    <th>确认</th>
                    <th>停线详情</th>
                    <th>StopTime</th>
                    <th>StopCount</th>
                </tr>
            </thead>
        </table>
    </div>
</div>

<style>
.app-container {
    padding: 20px;
    position: relative;
    max-width: 100%;
    overflow-x: auto;
}

.form-section {
    background: #fff;
    padding: 30px;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    max-width: 800px;
    margin: 20px auto;
}

.form-layout {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 20px;
    align-items: end;
}

.form-group {
    margin: 0;
}

.form-group label {
    display: block;
    margin-bottom: 8px;
    font-weight: 500;
    color: #333;
    font-size: 15px;
}

.form-control {
    width: 100%;
    height: 38px;
    border: 1px solid #ddd;
    border-radius: 4px;
    padding: 8px 12px;
}

.action-buttons {
    margin-top: 30px;
    display: flex;
    gap: 15px;
    justify-content: center;
}

.btn {
    min-width: 120px;
    height: 38px;
    font-size: 15px;
}

.table-section {
    background: #fff;
    padding: 20px;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    margin-top: 20px;
    overflow-x: auto;
    width: 100%;
}

.gap-negative {
    color: red;
}

.gap-positive {
    color: green;
}

.incident-details {
    max-height: none;
    background-color: #fff;
    border-radius: 4px;
    padding: 8px;
    overflow-x: auto;
}

.incident-item {
    padding: 8px;
    border: 1px solid #eee;
    margin-bottom: 8px;
    border-radius: 4px;
}

.incident-header {
    display: flex;
    justify-content: space-between;
    border-bottom: 1px solid #eee;
    padding-bottom: 4px;
    margin-bottom: 8px;
}

.incident-time {
    color: #666;
    font-weight: bold;
}

.incident-stoptime {
    color: #e74c3c;
    font-weight: bold;
}

.incident-content {
    padding: 4px 0;
}

.incident-desc {
    color: #333;
    display: block;
    margin-bottom: 8px;
}

.incident-footer {
    display: flex;
    justify-content: flex-end;
    color: #666;
    font-size: 0.9em;
}

.incident-person {
    font-style: italic;
}

.total-duration {
    background-color: #f8f9fa;
    padding: 8px;
    margin-bottom: 8px;
    border-radius: 4px;
    font-weight: bold;
    color: #e74c3c;
}

.has-incidents {
    background-color: #fff3cd80 !important;
}

.incident-details {
    position: relative;
}

.incident-details:hover {
    z-index: 1000;
}

.incident-table {
    width: 100%;
    border-collapse: collapse;
    font-size: 0.9em;
    margin-bottom: 0;
}

.incident-table th,
.incident-table td {
    padding: 6px;
    border: 1px solid #ddd;
    text-align: left;
    white-space: normal;
    word-break: break-word;
}

.incident-table th {
    background-color: #f8f9fa;
    font-weight: bold;
    position: sticky;
    top: 0;
    z-index: 1;
}

.incident-table .stoptime {
    color: #e74c3c;
    font-weight: bold;
    text-align: center;
}

.incident-table .total-row {
    background-color: #f8f9fa;
    font-weight: bold;
}

.incident-table .total-row td {
    border-top: 2px solid #dee2e6;
}

.incident-popup {
    position: fixed !important;
    top: 50% !important;
    left: 50% !important;
    transform: translate(-50%, -50%) !important;
    background: white !important;
    padding: 0;
    border-radius: 8px;
    box-shadow: 0 0 20px rgba(0,0,0,0.15);
    z-index: 1100;
    width: 80%;
    max-width: 1000px;
    min-width: 600px;
}

.popup-header {
    background: #f8f9fa;
    padding: 15px 20px;
    border-bottom: 1px solid #dee2e6;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.popup-header h5 {
    margin: 0;
    font-size: 16px;
    font-weight: 500;
}

.popup-content {
    padding: 20px;
    max-height: 60vh;
    overflow-y: auto;
}

.incident-table {
    width: 100%;
    border-collapse: collapse;
    background: white;
}

.incident-table th {
    background: #f8f9fa;
    padding: 12px;
    border: 1px solid #dee2e6;
    font-weight: 500;
    text-align: center;
}

.incident-table td {
    padding: 10px;
    border: 1px solid #dee2e6;
    vertical-align: middle;
}

.incident-table th:nth-child(1) { width: 20%; }  /* 时间段 */
.incident-table th:nth-child(2) { width: 15%; }  /* 时长 */
.incident-table th:nth-child(3) { width: 50%; }  /* 问题描述 */
.incident-table th:nth-child(4) { width: 15%; }  /* 责任人 */

.close-btn {
    font-size: 20px;
    color: #666;
    cursor: pointer;
    padding: 5px;
}

.close-btn:hover {
    color: #333;
}

/* 移除遮罩层 */
.incident-overlay {
    display: none !important;
}

/* 美化滚动条 */
.popup-content::-webkit-scrollbar {
    width: 8px;
}

.popup-content::-webkit-scrollbar-track {
    background: #f1f1f1;
}

.popup-content::-webkit-scrollbar-thumb {
    background: #888;
    border-radius: 4px;
}

.popup-content::-webkit-scrollbar-thumb:hover {
    background: #555;
}

/* 表格内容样式 */
.incident-table td.text-center {
    text-align: center;
}

/* 确保表格自应容器宽度 */
.incident-table {
    table-layout: fixed;
}

.incident-table td {
    word-break: break-word;
}

.close-btn {
    position: absolute;
    right: 15px;
    top: 15px;
    cursor: pointer;
    font-size: 24px;
    width: 30px;
    height: 30px;
    line-height: 30px;
    text-align: center;
    border-radius: 50%;
    background-color: #f8f9fa;
    color: #666;
    transition: all 0.3s;
}

.close-btn:hover {
    background-color: #e9ecef;
    color: #333;
}

.incident-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(255, 240, 240, 0.3);
    z-index: 1099;
}

/* 合并单元格的样式 */
.table td.text-nowrap {
    white-space: nowrap;
    vertical-align: middle;
    background-color: #f8f9fa;  /* 给合并的单元格添加背景色 */
}

/* 并单元格的边框样式 */
.table td[rowspan] {
    border-right: 2px solid #dee2e6;
    border-left: 2px solid #dee2e6;
}

/* 第一个合并单元格的上边框 */
.table td[rowspan]:first-child {
    border-top: 2px solid #dee2e6;
}

/* 最后一个合并单元格的下边框 */
.table tr:last-child td[rowspan] {
    border-bottom: 2px solid #dee2e6;
}

/* 加载动画样式 */
.loading-text {
    padding: 20px;
    color: #666;
    font-size: 14px;
    text-align: center;
}

/* 自定义layer样式 */
.layui-layer-loading .layui-layer-content {
    padding: 20px;
    background-color: rgba(255, 255, 255, 0.9);
    border-radius: 4px;
}

/* 确认按钮样式 */
.confirm-btn {
    min-width: 80px;
    margin-right: 10px;
    position: relative;
    z-index: 2;
}

/* 确认弹窗样式 */
#confirmModal .modal-body {
    padding: 20px;
}

#confirmModal .form-group {
    margin-bottom: 15px;
}

#confirmModal label {
    font-weight: bold;
    margin-right: 10px;
}

.info-group {
    margin-bottom: 10px;
}
.info-group label {
    font-weight: bold;
    margin-right: 10px;
    min-width: 80px;
    display: inline-block;
}
.modal-lg {
    max-width: 800px;
}

/* 修改弹窗样式 */
.modal {
    z-index: 1050 !important;
}

.incident-overlay {
    z-index: 1000;
}

.incident-popup {
    z-index: 1100;
}

.table-section {
    position: relative;
    z-index: 1;
}

.modal-backdrop {
    z-index: 1040 !important;
}

/* 已确认按钮样式 */
.btn-success:disabled {
    opacity: 1;
    position: relative;
    z-index: 2;
}

/* DataTables 相关样式 */
.dataTables_wrapper {
    position: relative;
    z-index: 1;
    padding-bottom: 60px;
}

.dataTables_length, 
.dataTables_filter {
    margin-bottom: 15px;
}

.dataTables_info, 
.dataTables_paginate {
    position: absolute;
    bottom: 0;
}

.dataTables_info {
    left: 0;
}

.dataTables_paginate {
    right: 0;
}

/* 确保分页控件在最上层 */
.dataTables_paginate .paginate_button {
    position: relative;
    z-index: 10;
}

/* 确保搜索框在最上层 */
.dataTables_filter {
    position: relative;
    z-index: 10;
}

/* 确保每页显示条数选择器在最上层 */
.dataTables_length {
    position: relative;
    z-index: 10;
}

/* 表格容器样式 */
.dataTables_scroll {
    margin-bottom: 15px;
    overflow: visible;
}

/* 表格样式 */
.table tbody tr {
    position: relative;
    z-index: 1;
}

/* 确保表格内容不会被遮挡 */
.table tbody tr:hover {
    z-index: 5;
}

/* 调整最后两列的和位置 */
.dataTables_wrapper table.dataTable td:nth-last-child(1),
.dataTables_wrapper table.dataTable td:nth-last-child(2) {
    min-width: 100px;
    position: relative;
    z-index: 2;
}

/* 确保按钮容器有足够空间 */
.dataTables_wrapper table.dataTable td:nth-last-child(1) {
    padding-right: 20px;
}

/* 查看详情按钮样式 */
.view-incidents {
    min-width: 80px;
    margin-right: 10px;
}

/* 修改表格容器样式 */
.table-section {
    background: #fff;
    padding: 20px;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    margin-top: 20px;
    overflow: visible;
    position: relative;
}

/* 调整表格样式确保右侧有足够空间 */
#outputTable {
    width: calc(100% - 60px) !important;
    margin-right: 60px;
}

/* 调整最后列的样式 */
#outputTable td:nth-last-child(1),
#outputTable td:nth-last-child(2) {
    position: relative;
    z-index: 10;
    background: #fff;
    min-width: 100px;
}

/* 调整按钮样式 */
.view-incidents.btn,
.confirm-btn,
.btn-success[disabled] {
    position: relative;
    z-index: 11;
    min-width: 80px;
    white-space: nowrap;
    margin: 2px;
}

/* 确保表格行在hover时不会被遮挡 */
.table tbody tr:hover {
    z-index: 9;
}

/* 移除之前可能冲突的样式 */
.dataTables_wrapper table.dataTable td:nth-last-child(1),
.dataTables_wrapper table.dataTable td:nth-last-child(2) {
    min-width: auto;
}

/* 调整表格样式以适应更大的弹窗 */
.incident-table {
    width: 100%;
    border-collapse: collapse;
    font-size: 0.9em;
    margin-bottom: 0;
    background-color: white;
}

/* 调整表格列宽度 */
.incident-table th[style*="min-width:150px"] {
    min-width: 200px;
}

.incident-table th[style*="min-width:300px"] {
    min-width: 500px;
}

/* 确保弹窗在滚动时表头固定 */
.incident-table thead {
    position: sticky;
    top: 0;
    background: white;
    z-index: 2;
}

/* 调整表格列宽 */
#outputTable {
    width: 100%;
    min-width: 1500px;  /* 确保表有最小宽度 */
}

#outputTable th,
#outputTable td {
    white-space: nowrap;  /* 防止文本换行 */
    padding: 8px 12px;    /* 增加单元格内边距 */
}

/* 设置各列的最小宽度 */
#outputTable th:nth-child(1) { min-width: 80px; }   /* Type */
#outputTable th:nth-child(2) { min-width: 100px; }  /* Line */
#outputTable th:nth-child(3) { min-width: 120px; }  /* PartNumber */
#outputTable th:nth-child(4) { min-width: 120px; }  /* Station */
#outputTable th:nth-child(5) { min-width: 80px; }   /* Date */
#outputTable th:nth-child(6) { min-width: 60px; }   /* Shift */
#outputTable th:nth-child(7) { min-width: 60px; }   /* Output */
#outputTable th:nth-child(8) { min-width: 60px; }   /* PlanQty */
#outputTable th:nth-child(9) { min-width: 60px; }   /* Gap */
#outputTable th:nth-child(10) { min-width: 70px; }  /* 确认 */
#outputTable th:nth-child(11) { min-width: 80px; }  /* 停线详情 */
#outputTable th:nth-child(12) { min-width: 80px; }  /* StopTime */
#outputTable th:nth-child(13) { min-width: 80px; }  /* StopCount */

/* 调整表格高度 */
.dataTables_scrollBody {
    max-height: 70vh !important;  /* 设置为视窗高度的70% */
    overflow-y: auto !important;
}

/* 确保表定 */
.dataTables_scrollHead {
    position: sticky;
    top: 0;
    z-index: 1;
    background: white;
}

/* 美化滚动条 */
.dataTables_scrollBody::-webkit-scrollbar {
    width: 8px;
    height: 8px;
}

.dataTables_scrollBody::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 4px;
}

.dataTables_scrollBody::-webkit-scrollbar-thumb {
    background: #888;
    border-radius: 4px;
}

.dataTables_scrollBody::-webkit-scrollbar-thumb:hover {
    background: #555;
}

/* 页面标题样式 */
.page-title {
    text-align: center;
    margin-bottom: 30px;
    font-size: 24px;
    color: #333;
    font-weight: 500;
}

/* 表格部分样式调整 */
.table-section {
    margin-top: 30px;
    padding: 25px;
}

/* 调整表格上方的间距 */
.mt-4 {
    margin-top: 2rem !important;
}

/* 调整表格整体样式 */
#outputTable {
    font-size: 12px;  /* 减小字体大小 */
    width: 100% !important;  /* 确保表格占满容器 */
    margin-right: 0;  /* 移除右侧边距 */
}

/* 调整表格单元格样式 */
#outputTable th,
#outputTable td {
    padding: 4px 6px;  /* 减小单元格内边距 */
    white-space: nowrap;
    vertical-align: middle;
}

/* 设置各列的最小宽度，整为更的值 */
#outputTable th:nth-child(1) { min-width: 80px; }   /* Type */
#outputTable th:nth-child(2) { min-width: 100px; }  /* Line */
#outputTable th:nth-child(3) { min-width: 120px; }  /* PartNumber */
#outputTable th:nth-child(4) { min-width: 120px; }  /* Station */
#outputTable th:nth-child(5) { min-width: 80px; }   /* Date */
#outputTable th:nth-child(6) { min-width: 60px; }   /* Shift */
#outputTable th:nth-child(7) { min-width: 60px; }   /* Output */
#outputTable th:nth-child(8) { min-width: 60px; }   /* PlanQty */
#outputTable th:nth-child(9) { min-width: 60px; }   /* Gap */
#outputTable th:nth-child(10) { min-width: 70px; }  /* 确认 */
#outputTable th:nth-child(11) { min-width: 80px; }  /* 停线详情 */
#outputTable th:nth-child(12) { min-width: 80px; }  /* StopTime */
#outputTable th:nth-child(13) { min-width: 80px; }  /* StopCount */

/* 调整按钮样式 */
.view-incidents.btn,
.confirm-btn,
.btn-success[disabled] {
    padding: 2px 8px;  /* 减小按钮内边距 */
    font-size: 12px;   /* 减小按钮字体 */
    min-width: 60px;   /* 减小按钮最小宽度 */
    margin: 1px;       /* 减小按钮间距 */
}

/* 调整表格容器样式 */
.table-section {
    padding: 15px;    /* 减小内边距 */
    margin-top: 15px;
    max-height: none; /* 移除最大高度限制 */
    overflow: visible; /* 改为可见溢出 */
}

/* 调整表格样式 */
#outputTable {
    font-size: 11px;  /* 进一步减小字体 */
    width: 100% !important;
    margin: 0;
    border-collapse: collapse;
}

/* 调整单元格内边距 */
#outputTable th,
#outputTable td {
    padding: 3px 4px;  /* 减小单元格内边距 */
    height: 24px;      /* 限制行高 */
    line-height: 1.2;  /* 减小行高 */
}

/* 修改 DataTables 初始化配置... */
.dataTables_scrollBody {
    max-height: none !important;  /* 移除最大高度限制 */
    overflow: visible !important; /* 改为可见溢出 */
}

/* 保持表头样式 */
.dataTables_scrollHead {
    position: sticky;
    top: 0;
    z-index: 1;
    background: white;
}

/* 添加过滤按钮组样式 */
.type-filter-section {
    text-align: left;
    padding: 10px 0;
}

.type-filter-buttons .btn {
    margin: 0 5px 5px 0;
    font-size: 12px;
    padding: 4px 12px;
}

.type-filter-buttons .btn.active {
    background-color: #007bff;
    color: white;
}

/* 修改过滤按钮组样式 */
.type-filter-section {
    text-align: left;
    padding: 10px 0;
    margin-bottom: 15px;
}

.type-filter-buttons {
    display: grid;
    grid-template-rows: repeat(2, auto);  /* 固定两行 */
    grid-auto-flow: column;  /* 按列排序 */
    grid-auto-columns: max-content;  /* 列宽度自适应内容 */
    gap: 8px;  /* 按钮之间的间距 */
    max-width: 100%;  /* 确保不会超出容器宽度 */
    overflow-x: auto;  /* 允许水平滚动 */
    padding-bottom: 8px;  /* 为滚动条留出空间 */
}

/* 修改按钮样式 */
.type-filter-buttons .btn {
    margin: 0;  /* 移除原有的margin */
    font-size: 12px;
    padding: 4px 12px;
    white-space: nowrap;  /* 防止按钮文字换行 */
    min-width: max-content;  /* 确保按钮宽度适应内容 */
}

/* 美化水平滚动条 */
.type-filter-buttons::-webkit-scrollbar {
    height: 6px;  /* 滚动条高度 */
}

.type-filter-buttons::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 3px;
}

.type-filter-buttons::-webkit-scrollbar-thumb {
    background: #888;
    border-radius: 3px;
}

.type-filter-buttons::-webkit-scrollbar-thumb:hover {
    background: #555;
}

/* 修改过滤按钮组容器样式 */
.type-filter-section {
    text-align: left;
    padding: 10px 0;
    margin-bottom: 15px;
    max-width: 100%;
    overflow: hidden;  /* 防止内容溢出 */
}

/* 线别筛选样式 */
.line-filter {
    max-width: 200px;
    margin-top: 15px;
}

.line-filter select {
    width: 100%;
    padding: 6px 12px;
    font-size: 14px;
    border: 1px solid #ddd;
    border-radius: 4px;
    background-color: #fff;
}

.line-filter select:focus {
    border-color: #80bdff;
    outline: 0;
    box-shadow: 0 0 0 0.2rem rgba(0,123,255,.25);
}
</style>

<!-- 添加确认窗 -->
<div class="modal fade" id="confirmModal" tabindex="-1" role="dialog">
    <div class="modal-dialog modal-lg" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">确认产出信息</h5>
                <button type="button" class="close" data-dismiss="modal">
                    <span>&times;</span>
                </button>
            </div>
            <div class="modal-body">
                <div class="row">
                    <div class="col-md-6">
                        <div class="info-group">
                            <label>线别：</label>
                            <span id="confirmLine" class="font-weight-bold"></span>
                        </div>
                        <div class="info-group">
                            <label>日期：</label>
                            <span id="confirmDate" class="font-weight-bold"></span>
                        </div>
                        <div class="info-group">
                            <label>班别：</label>
                            <span id="confirmShift" class="font-weight-bold"></span>
                        </div>
                        <div class="info-group">
                            <label>类型：</label>
                            <span id="confirmType" class="font-weight-bold"></span>
                        </div>
                        <div class="info-group">
                            <label>料号：</label>
                            <span id="confirmPartNumber" class="font-weight-bold"></span>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="info-group">
                            <label>站别：</label>
                            <span id="confirmStation" class="font-weight-bold"></span>
                        </div>
                        <div class="info-group">
                            <label>产出：</label>
                            <span id="confirmOutput" class="font-weight-bold"></span>
                        </div>
                        <div class="info-group">
                            <label>计划数：</label>
                            <input type="number" class="form-control form-control-sm d-inline-block w-50" 
                                   id="confirmPlanQty" min="0">
                        </div>
                        <div class="info-group">
                            <label>差异：</label>
                            <span id="confirmGap" class="font-weight-bold"></span>
                        </div>
                        <div class="info-group">
                            <label>停线时长：</label>
                            <span id="confirmStopTime" class="font-weight-bold"></span>
                        </div>
                        <div class="info-group">
                            <label>停线次数：</label>
                            <span id="confirmStopCount" class="font-weight-bold"></span>
                        </div>
                    </div>
                </div>
                
                <!-- 停线详情部分 -->
                <div class="mt-3" id="confirmIncidentsSection">
                    <h6>停线详情：</h6>
                    <div id="confirmIncidents" class="border p-2" style="max-height: 200px; overflow-y: auto;">
                        <!-- 停线详情表格将通过 JavaScript 动态插入 -->
                    </div>
                </div>

                <!-- 备注输入框 -->
                <div class="form-group mt-3">
                    <label for="confirmNote">备注：<span class="text-danger">*</span></label>
                    <textarea class="form-control" id="confirmNote" rows="3" required></textarea>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-dismiss="modal">取消</button>
                <button type="button" class="btn btn-primary" id="saveConfirm">确认</button>
            </div>
        </div>
    </div>
</div>

<!-- 在页面底部添加遮罩层 -->
<div class="incident-overlay"></div>
{% endblock %}

{% block extra_js %}
<script>
// 将 formatDateTime 函数移到全局作用域
function formatDateTime(dateTimeStr) {
    var dt = new Date(dateTimeStr);
    return dt.getFullYear() + '-' + 
        String(dt.getMonth() + 1).padStart(2, '0') + '-' + 
        String(dt.getDate()).padStart(2, '0') + ' ' + 
        String(dt.getHours()).padStart(2, '0') + ':' + 
        String(dt.getMinutes()).padStart(2, '0') + ':00';
}

$(document).ready(function() {
    // 设置默认时间范围（今天8点到明天8点）
    function setDefaultTimeRange() {
        var today = new Date();
        var tomorrow = new Date(today);
        tomorrow.setDate(today.getDate() + 1);
        
        // 设置时间 08:00
        var startDate = new Date(today.getFullYear(), today.getMonth(), today.getDate(), 8, 0);
        var endDate = new Date(tomorrow.getFullYear(), tomorrow.getMonth(), tomorrow.getDate(), 8, 0);
        
        // 格式化日期时间符串
        var startStr = startDate.getFullYear() + '-' + 
                      String(startDate.getMonth() + 1).padStart(2, '0') + '-' + 
                      String(startDate.getDate()).padStart(2, '0') + 'T08:00';
        
        var endStr = endDate.getFullYear() + '-' + 
                    String(endDate.getMonth() + 1).padStart(2, '0') + '-' + 
                    String(endDate.getDate()).padStart(2, '0') + 'T08:00';
        
        $('#start_time').val(startStr);
        $('#end_time').val(endStr);
    }
    
    setDefaultTimeRange();

    // 修改 DataTable 初始化配置
    var table = $('#outputTable').DataTable({
        processing: true,
        serverSide: false,
        searching: true,
        ordering: true,
        paging: false,
        info: false,
        dom: 't',
        columns: [
            { data: 'Type' },
            { data: 'Line' },
            { data: 'PartNumber' },
            { data: 'Station' },
            { data: 'Date' },
            { data: 'shift' },
            { data: 'Output' },
            { data: 'PlanQty' },
            { 
                data: 'Gap',
                render: function(data, type, row) {
                    if (type === 'display') {
                        var actualGap = parseInt(row.Output) - parseInt(row.PlanQty);
                        var className = actualGap < 0 ? 'gap-negative' : 'gap-positive';
                        return '<span class="' + className + '">' + actualGap + '</span>';
                    }
                    return parseInt(row.Output) - parseInt(row.PlanQty);
                }
            },
            {
                data: null,
                render: function(data, type, row) {
                    var isConfirmed = row.isConfirmed;
                    if (isConfirmed) {
                        return '<button class="btn btn-sm btn-success" disabled>已确认</button>';
                    } else {
                        return '<button class="btn btn-sm btn-primary confirm-btn">确认</button>';
                    }
                }
            },
            {
                data: 'incidents',
                render: function(data, type, row) {
                    if (!data || data.length === 0) return '';
                    return `<a href="#" class="view-incidents btn btn-sm btn-info">查看详情</a>
                        <div class="incident-popup" style="display:none;">
                            <div class="popup-header">
                                <h5>停线详情</h5>
                                <span class="close-btn">&times;</span>
                            </div>
                            <div class="popup-content">
                                <table class="incident-table">
                                    <thead>
                                        <tr>
                                            <th>时间段</th>
                                            <th>时长(分钟)</th>
                                            <th>问题描述</th>
                                            <th>责任人</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        ${data.map(incident => `
                                            <tr>
                                                <td>${incident.StartTime.substr(11, 5)} - ${incident.EndTime.substr(11, 5)}</td>
                                                <td class="text-center">${incident.StopTime}</td>
                                                <td>${incident.IssueDescription}</td>
                                                <td class="text-center">${incident.ResponsiblePerson}</td>
                                            </tr>
                                        `).join('')}
                                    </tbody>
                                </table>
                            </div>
                        </div>`;
                }
            },
            {
                data: 'incidents',
                render: function(data, type, row) {
                    if (!data || data.length === 0) return '0分钟';
                    var totalStopTime = 0;
                    data.forEach(function(incident) {
                        totalStopTime += parseInt(incident.StopTime) || 0;
                    });
                    return `<span class="stoptime">${totalStopTime}分钟</span>`;
                }
            },
            {
                data: 'incidents',
                render: function(data, type, row) {
                    return data ? data.length : 0;
                }
            }
        ],
        scrollY: false,
        scrollX: true,
        scrollCollapse: false,
        order: [
            [1, 'asc'],
            [4, 'asc'],
            [5, 'asc']
        ],
    });

    // 表单提交
    $('#queryForm').on('submit', function(e) {
        e.preventDefault();
        
        var loadingIndex = layer.load(1, {
            shade: [0.3, '#fff'],
            content: '<div class="loading-text">正在查询数据，请稍候...</div>'
        });
        
        var startTime = formatDateTime($('#start_time').val());
        var endTime = formatDateTime($('#end_time').val());
        var projectType = $('#project_type').val();
        
        $.ajax({
            url: '{% url "mtghpreport:line_output_data" %}',
            type: 'POST',
            data: {
                'start_time': startTime,
                'end_time': endTime,
                'project_type': projectType,
                'csrfmiddlewaretoken': '{{ csrf_token }}'
            },
            success: function(response) {
                layer.close(loadingIndex);
                if(response.data) {
                    // 清空表格并添加新数据
                    table.clear().rows.add(response.data).draw();
                    
                    // 初始化类型过滤按钮
                    initTypeFilters(response.data);
                    
                    // 初始化线别筛选
                    initLineFilter(response.data);
                    
                    // 重置过滤状态，显示全部数据
                    table.column(0).search('').draw();
                    table.column(1).search('').draw(); // 重置线别筛选
                    
                    // 重置按钮和下拉框状态
                    $('.type-filter-buttons button').removeClass('active');
                    $('.type-filter-buttons button[data-type="all"]').addClass('active');
                    $('#lineFilter').val('');
                    
                    $('#exportServerBtn').data('cache-key', response.cache_key);
                    layer.msg('查询成功', {icon: 1});
                } else {
                    layer.msg('查询失败：' + response.error, {icon: 2});
                }
            },
            error: function() {
                layer.close(loadingIndex);
                layer.msg('系统错误，请稍后重试', {icon: 2});
            }
        });
    });

    // 修改出Excel钮的处理逻辑
    $('#exportBtn').on('click', function() {
        if (typeof XLSX === 'undefined') {
            layer.msg('导出组件未加载，请刷新页面重试');
            return;
        }
        
        // 获当前DataTable中的所有数据
        var data = table.data().toArray();
        
        if (data.length === 0) {
            layer.msg('没有数据可供导出');
            return;
        }
        
        // 创建一个工作簿
        var wb = XLSX.utils.book_new();
        
        // 将数据转换工作表
        var ws = XLSX.utils.json_to_sheet(data);
        
        // 将工作表加到工作簿
        XLSX.utils.book_append_sheet(wb, ws, "Sheet1");
        
        // 生成文件名
        var fileName = 'LineOutput_' + moment().format('YYYYMMDD_HHmmss') + '.xlsx';
        
        // 导出Excel文件
        XLSX.writeFile(wb, fileName);
    });

    // 服务器端导出钮处理
    $('#exportServerBtn').on('click', function() {
        var cacheKey = $(this).data('cache-key');
        if (!cacheKey) {
            layer.msg('请先查询数据', {icon: 2});
            return;
        }
        
        window.location.href = '{% url "mtghpreport:line_output_export" %}?cache_key=' + cacheKey;
    });

    // 添加点击事件处理
    $(document).on('click', '.view-incidents', function(e) {
        e.preventDefault();
        // 先关闭所有其他打开的弹窗
        $('.incident-popup').hide();
        $('.incident-overlay').hide();
        
        // 只显示当前点击的弹窗
        $('.incident-overlay').show();
        $(this).next('.incident-popup').show();
    });

    $(document).on('click', '.close-btn', function(e) {
        e.stopPropagation();
        $('.incident-overlay').hide();
        $('.incident-popup').hide();
    });

    $(document).on('click', '.incident-overlay', function() {
        $('.incident-overlay').hide();
        $('.incident-popup').hide();
    });

    // 修改确认按钮的点击事件处理
    $(document).on('click', '.confirm-btn', function(e) {
        e.preventDefault();
        var btn = $(this);
        var currentRow = table.row(btn.closest('tr'));
        var rowData = currentRow.data();
        
        // 保存当前行的数据，用于后续保存时使用
        $('#confirmModal').data('currentRow', currentRow);
        
        // 填充确认窗口信息
        $('#confirmLine').text(rowData.Line);
        $('#confirmDate').text(rowData.Date);
        $('#confirmShift').text(rowData.shift);
        $('#confirmType').text(rowData.Type || '-');
        $('#confirmPartNumber').text(rowData.PartNumber || '-');
        $('#confirmStation').text(rowData.Station || '-');
        $('#confirmOutput').text(rowData.Output || '0');
        $('#confirmPlanQty').val(rowData.PlanQty || '0');
        
        // 填充停线信息
        var totalStopTime = 0;
        if (rowData.incidents && rowData.incidents.length > 0) {
            // 计算总停线时间
            rowData.incidents.forEach(function(incident) {
                totalStopTime += parseInt(incident.StopTime) || 0;
            });
            
            // 显示停线时长和次数
            $('#confirmStopTime').text(totalStopTime + '分钟');
            $('#confirmStopCount').text(rowData.incidents.length);
            
            // 填充停线详情表
            var incidentsHtml = `
                <table class="table table-sm table-bordered">
                    <thead>
                        <tr>
                            <th>时间段</th>
                            <th>时长(分钟)</th>
                            <th>问题描述</th>
                            <th>责任人</th>
                        </tr>
                    </thead>
                    <tbody>
            `;
            
            rowData.incidents.forEach(function(incident) {
                incidentsHtml += `
                    <tr>
                        <td>${incident.StartTime.substr(11, 5)} - ${incident.EndTime.substr(11, 5)}</td>
                        <td class="text-center">${incident.StopTime}</td>
                        <td>${incident.IssueDescription}</td>
                        <td class="text-center">${incident.ResponsiblePerson}</td>
                    </tr>
                `;
            });
            
            incidentsHtml += '</tbody></table>';
            $('#confirmIncidents').html(incidentsHtml);
        } else {
            // 如果没有停线记录
            $('#confirmStopTime').text('0分钟');
            $('#confirmStopCount').text('0');
            $('#confirmIncidents').html('<p class="text-muted">无停线记录</p>');
        }
        
        // 立即计算并显示差异
        updateCalculatedValues();
        
        // 清空备注
        $('#confirmNote').val('');
        
        // 显示弹窗
        $('#confirmModal').modal('show');
    });

    // 监听计划数量的变化
    $('#confirmPlanQty').on('input change', function() {
        updateCalculatedValues();
    });

    // 修改计算函数
    function updateCalculatedValues() {
        var output = parseInt($('#confirmOutput').text()) || 0;
        var planQty = parseInt($('#confirmPlanQty').val()) || 0;
        
        // 计算差异：产出 - 计划数量
        var gap = output - planQty;
        
        // 更新差异显示，添加颜色样式
        var gapClass = gap < 0 ? 'text-danger' : 'text-success';
        $('#confirmGap').text(gap).removeClass('text-danger text-success').addClass(gapClass);
    }

    // 修改保存确认信息的处理
    $('#saveConfirm').on('click', function() {
        var note = $('#confirmNote').val().trim();
        var planQty = parseInt($('#confirmPlanQty').val()) || 0;
        
        // 添加计划数量验证
        if (planQty === 0) {
            layer.msg('请输入计划数量，不能为0', {icon: 2});
            $('#confirmPlanQty').focus();
            return;
        }
        
        if (!note) {
            layer.msg('请输入备注信息', {icon: 2});
            $('#confirmNote').focus();
            return;
        }
        
        var currentRow = $('#confirmModal').data('currentRow');
        var rowData = currentRow.data();
        
        var data = {
            line: rowData.Line,
            date: rowData.Date,
            shift: rowData.shift,
            note: note,
            type: rowData.Type || '',
            partNumber: rowData.PartNumber || '',
            station: rowData.Station || '',
            output: parseInt($('#confirmOutput').text()) || 0,
            planqty: parseInt($('#confirmPlanQty').val()) || 0,  // 使用输入的计划数量
            gap: parseInt($('#confirmGap').text()) || 0,
            stopTime: $('#confirmStopTime').text() || '0分钟',
            stopCount: rowData.incidents ? rowData.incidents.length : '0',
            incidentsDetail: rowData.incidents ? rowData.incidents.map(inc => 
                `时间: ${inc.StartTime.substr(11, 5)}-${inc.EndTime.substr(11, 5)}, ` +
                `时长: ${inc.StopTime}分钟, ` +
                `问题: ${inc.IssueDescription}, ` +
                `责任人: ${inc.ResponsiblePerson}`
            ).join('\n') : '',
            csrfmiddlewaretoken: '{{ csrf_token }}'
        };
        
        $.ajax({
            url: '{% url "mtghpreport:save_confirmation" %}',
            type: 'POST',
            data: data,
            success: function(response) {
                if (response.success) {
                    layer.msg('确认信息已保存', {icon: 1});
                    $('#confirmModal').modal('hide');
                    
                    // 更新行数据中的确认状态和新的计划数量、UPH
                    var rowData = currentRow.data();
                    rowData.isConfirmed = true;
                    rowData.PlanQty = parseInt($('#confirmPlanQty').val()) || 0;
                    rowData.Gap = parseInt($('#confirmGap').text()) || 0;
                    rowData.StopTime = $('#confirmStopTime').text() || '0分钟';
                    rowData.StopCount = rowData.incidents ? rowData.incidents.length : '0';
                    currentRow.data(rowData).draw(false);
                    
                } else {
                    layer.msg(response.error || '保存失败', {icon: 2});
                }
            },
            error: function() {
                layer.msg('系统错误，请稍后重试', {icon: 2});
            }
        });
    });

    // 添加窗口大小改变时的处理
    $(window).on('resize', function() {
        table.columns.adjust();
    });

    // 在 DataTable 初始化后加以下代码
    function initTypeFilters(data) {
        // 获取所有唯一的 Type 值并按字母排序
        var types = [...new Set(data.map(item => item.Type))]
            .filter(type => type) // ���滤掉空值
            .sort((a, b) => {
                // 获取第一个字符并转换为小写进行比较
                const aFirst = (a || '').charAt(0).toLowerCase();
                const bFirst = (b || '').charAt(0).toLowerCase();
                return aFirst.localeCompare(bFirst);
            });
        
        // 清空现有按钮（保留"全部"按钮）
        $('.type-filter-buttons button:not(:first)').remove();
        
        // 添加每种类型的按钮
        types.forEach(type => {
            $('.type-filter-buttons').append(
                `<button type="button" class="btn btn-outline-primary" data-type="${type}">${type}</button>`
            );
        });
        
        // 修改过滤按钮点击事件处理
        $('.type-filter-buttons button').on('click', function() {
            var selectedType = $(this).data('type');
            var currentLine = $('#lineFilter').val(); // 获取当前选中的线别
            
            // 更新按钮状态
            $('.type-filter-buttons button').removeClass('active');
            $(this).addClass('active');
            
            // 应用类型筛选
            if (selectedType === 'all') {
                table.column(0).search('').draw();
            } else {
                table.column(0).search('^' + selectedType + '$', true, false).draw();
            }
            
            // 重新应用线别筛选（如果有）
            if (currentLine) {
                table.column(1).search('^' + currentLine + '$', true, false).draw();
            }
        });
    }

    // 在 initTypeFilters 函数后添加初始化线别筛选的函数
    function initLineFilter(data) {
        // 获取所有唯一的线别值并排序
        var lines = [...new Set(data.map(item => item.Line))]
            .filter(line => line) // 过滤掉空值
            .sort();
        
        // 清空现有选项（保留"所有线别"选项）
        $('#lineFilter option:not(:first)').remove();
        
        // 添加线别选项
        lines.forEach(line => {
            $('#lineFilter').append(
                `<option value="${line}">${line}</option>`
            );
        });
    }

    // 添加线别筛选事件处理
    $('#lineFilter').on('change', function() {
        var selectedLine = $(this).val();
        
        // 应用线别筛选
        if (selectedLine === '') {
            table.column(1).search('').draw();
        } else {
            table.column(1).search('^' + selectedLine + '$', true, false).draw();
        }
    });
});
</script>
{% endblock %} 
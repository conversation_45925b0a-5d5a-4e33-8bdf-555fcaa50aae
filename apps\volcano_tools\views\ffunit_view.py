from django.shortcuts import render
from django.http import JsonResponse
from django.views.decorators.http import require_POST
from django.db import connections
import json
import logging
from datetime import datetime
from ..models import ffUnitState
from django.core.serializers.json import DjangoJ<PERSON>NEncoder
from django.db.models import Q
import socket

logger = logging.getLogger(__name__)

def ffunit_query_view(request):
    """
    Render the main view for the FF Unit Query page
    """
    unit_states = ffUnitState.objects.using('VolcanoFFDB').all().order_by('ID')
    return render(request, 'ffunit/ffunit_query.html', {'unit_states': unit_states})

@require_POST
def query_unit_states(request):
    try:
        data = json.loads(request.body)
        serial_numbers_input = data.get('serial_numbers', '')

        if not serial_numbers_input:
            return JsonResponse({'error': 'Serial numbers are required'}, status=400)

        # Process serial numbers
        serial_numbers = [f"'{sn.strip()}'" for sn in serial_numbers_input.splitlines() if sn.strip()]
        serial_numbers_str = ','.join(serial_numbers)

        with connections['VolcanoFFDB'].cursor() as cursor:
            query = f"""
                SELECT sn.Value, p.PartNumber, u.UnitStateID, us.Description, u.LastUpdate 
                FROM dbo.ffSerialNumber sn 
                JOIN dbo.ffUnit u ON u.id=sn.UnitID
                JOIN dbo.ffUnitState us ON us.id=u.UnitStateID
                JOIN dbo.ffPart p ON p.id=u.PartID
                WHERE sn.Value IN ({serial_numbers_str})
            """
            
            cursor.execute(query)
            results = cursor.fetchall()

            data = [
                {
                    'serial_number': row[0],
                    'part_number': row[1],
                    'unit_state_id': row[2],
                    'state_description': row[3],
                    'last_update': row[4].strftime('%Y-%m-%d %H:%M:%S') if isinstance(row[4], datetime) else str(row[4])
                }
                for row in results
            ]

            return JsonResponse({'data': data})

    except json.JSONDecodeError:
        return JsonResponse({'error': 'Invalid JSON format'}, status=400)
    except Exception as e:
        logger.exception("Error in query_unit_states")
        return JsonResponse({'error': str(e)}, status=500)

def get_unit_states(request):
    """Get list of unit states for dropdown with search"""
    search = request.GET.get('search', '').strip()
    states = ffUnitState.objects.using('VolcanoFFDB')
    
    if search:
        # 构建 Q 对象进行复杂查询
        try:
            # 尝试将搜索词转换为数字
            state_id = int(search)
            # 使用 Q 对象组合查询条件：
            # 1. ID 以搜索数字开头
            # 2. 或者 Description 包含搜索词
            states = states.filter(
                Q(ID__startswith=state_id) |
                Q(Description__icontains=search)
            )
        except ValueError:
            # 如果不是数字，只搜索 Description
            states = states.filter(Description__icontains=search)
    else:
        # 如果没有搜索词，返回前100条记录
        states = states.all()[:100]
    
    # 按ID排序
    states = states.order_by('ID')
    
    states_list = [
        {
            'id': state.ID,
            'text': f"{state.ID} - {state.Description}"
        }
        for state in states
    ]
    
    return JsonResponse({'results': states_list})

@require_POST 
def update_unit_states(request):
    """Batch update unit states using stored procedure"""
    try:
        data = json.loads(request.body)
        serial_numbers_input = data.get('serial_numbers', '')
        new_state_id = data.get('new_state_id')
        remark = data.get('remark', '').strip()

        logger.info(f"Received update request - State ID: {new_state_id}, Remark: {remark}")
        logger.info(f"Serial numbers input: {serial_numbers_input}")

        # 增强输入验证
        validation_errors = []
        if not serial_numbers_input:
            validation_errors.append("Serial numbers are required")
        if not new_state_id:
            validation_errors.append("New state is required")
        if not remark:
            validation_errors.append("Remark is required")

        if validation_errors:
            return JsonResponse({
                'error': 'Validation failed',
                'details': validation_errors
            }, status=400)

        # Process serial numbers
        serial_numbers = [sn.strip() for sn in serial_numbers_input.splitlines() if sn.strip()]
        if not serial_numbers:
            return JsonResponse({
                'error': 'No valid serial numbers provided'
            }, status=400)

        logger.info(f"Processing {len(serial_numbers)} serial numbers")
        
        success_count = 0
        errors = []

        with connections['VolcanoFFDB'].cursor() as cursor:
            for sn in serial_numbers:
                try:
                    logger.info(f"Updating SN: {sn} to state: {new_state_id}")
                    
                    # 将所有操作合并到一个批处理中
                    sql = """
                    BEGIN
                        SET NOCOUNT ON;
                        DECLARE @ret INT;
                        EXEC @ret = dbo.RW_UpdateUnitState @SN = %s, @newstateid = %s, @remark = %s;
                        SELECT @ret AS return_value;
                    END
                    """
                    
                    cursor.execute(sql, [sn, new_state_id, remark])
                    result = cursor.fetchone()
                    ret_code = result[0] if result else None
                    logger.info(f"Update result for {sn}: {ret_code}")
                    
                    error_messages = {
                        8800: "未找到对应的 UnitID",
                        8801: "此条码有卡通号不能改状态",
                        8802: "更新过程中发生错误"
                    }
                    
                    if ret_code == 0:
                        success_count += 1
                        logger.info(f"Successfully updated {sn}")
                    else:
                        error_msg = f"Failed to update {sn}: {error_messages.get(ret_code, f'Unknown error code {ret_code}')}"
                        logger.error(error_msg)
                        errors.append(error_msg)
                except Exception as e:
                    error_msg = f"Error updating {sn}: {str(e)}"
                    logger.exception(error_msg)
                    errors.append(error_msg)

        response = {
            'success_count': success_count,
            'total_count': len(serial_numbers),
            'errors': errors
        }

        # 修改返回逻辑
        if success_count > 0:
            # 只要有成功的就返回 200
            if success_count == len(serial_numbers):
                response['message'] = 'All units updated successfully'
            else:
                response['message'] = f'Updated {success_count} out of {len(serial_numbers)} units'
            logger.info(response['message'])
            return JsonResponse(response)
        else:
            # 完全没有成功才返回 400
            response['message'] = 'Failed to update any units'
            logger.error(response['message'])
            if errors:
                logger.error(f"Errors: {errors}")
            return JsonResponse(response, status=400)

    except Exception as e:
        error_msg = f"Error in update_unit_states: {str(e)}"
        logger.exception(error_msg)
        return JsonResponse({'error': error_msg}, status=500)

def get_client_info(request):
    """Get client information including username"""
    try:
        # 获取登录用户名
        username = request.user.username if request.user.is_authenticated else 'Unknown'
            
        return JsonResponse({
            'username': username
        })
    except Exception as e:
        logger.exception("Error getting client info")
        return JsonResponse({
            'username': 'Unknown'
        })

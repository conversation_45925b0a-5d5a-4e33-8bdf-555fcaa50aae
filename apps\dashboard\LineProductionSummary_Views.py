import pandas as pd
from django.db import connections
from django.shortcuts import render

def get_daily_line_production_summary_chart(request):
    raw_data = []
    error_message = None
    
    # 初始化两组图表数据
    holder_context_data = {
        'categories': [], 'planned_data': [], 'input_data': [], 'gap_data': [],
        'page_title': 'Hermes Holder BB 生产数据', 'data_exists': False
    }
    charger_context_data = {
        'categories': [], 'planned_data': [], 'input_data': [], 'gap_data': [],
        'page_title': 'Hermes Charger BB 生产数据', 'data_exists': False
    }

    type_col_name = None # 将在获取列后定义
    desc_col_name = None
    plan_qty_col_name = None
    input_qty_col_name = None

    try:
        with connections['VolcanoReporDB'].cursor() as cursor:
            cursor.execute("EXEC usp_GetDailyLineProductionSummary_rw")
            columns = [col[0] for col in cursor.description]
            
            if len(columns) < 5: 
                error_message = f"存储过程返回的列数不足。期望至少 5 列，实际返回{len(columns)}列：{', '.join(columns)}"
            else:
                # 假设列的顺序是：ProductType, Description (Line/Part), Plandate, PlannedQuantity, Input
                type_col_name = columns[0] 
                desc_col_name = columns[1] 
                # Plandate_col_name = columns[2] # 未直接用于图表聚合，但存在
                plan_qty_col_name = columns[3] 
                input_qty_col_name = columns[4]

                for row in cursor.fetchall():
                    raw_data.append(dict(zip(columns, row)))
    
    except Exception as e:
        print(f"Database error for Daily Line Production Summary: {e}")
        error_message = f'数据库错误：获取每日产线生产总结数据失败：{str(e)}'

    if not error_message and not raw_data:
        print("No data returned from stored procedure usp_GetDailyLineProductionSummary_rw.")
        # 保持 error_message 为 None，让后续逻辑判断是否真的没有相关类型的数据

    if raw_data and not error_message:
        try:
            df = pd.DataFrame(raw_data)

            # 确保在尝试访问这些列之前它们已经被成功赋值
            if not all([type_col_name, desc_col_name, plan_qty_col_name, input_qty_col_name]):
                 raise ValueError("列名变量 (type_col_name, etc.) 未从数据库元数据正确初始化。")

            required_processing_cols = [type_col_name, desc_col_name, plan_qty_col_name, input_qty_col_name]
            if not all(col in df.columns for col in required_processing_cols):
                missing_df_cols = [col for col in required_processing_cols if col not in df.columns]
                raise ValueError(f"DataFrame 创建后缺少必要的列进行处理。期望：{', '.join(required_processing_cols)}。实际 DataFrame 列：{df.columns.tolist()}。缺失：{', '.join(missing_df_cols)}")

            df[plan_qty_col_name] = pd.to_numeric(df[plan_qty_col_name], errors='coerce').fillna(0)
            df[input_qty_col_name] = pd.to_numeric(df[input_qty_col_name], errors='coerce').fillna(0)

            product_types_to_process = {
                "Hermes Holder BB": holder_context_data,
                "Hermes Charger BB": charger_context_data
            }

            for product_type, context_ref in product_types_to_process.items():
                # 确保 type_col_name 在 DataFrame 中存在
                if type_col_name not in df.columns:
                    raise KeyError(f"用于筛选产品类型的列 '{type_col_name}' 不在 DataFrame 中。可用列：{df.columns.tolist()}")

                df_filtered = df[df[type_col_name] == product_type]
                
                if not df_filtered.empty:
                    # 确保 desc_col_name 在 df_filtered 中存在
                    if desc_col_name not in df_filtered.columns:
                         raise KeyError(f"用于分组的描述列 '{desc_col_name}' 不在筛选后的 DataFrame 中 (类型：{product_type})。可用列：{df_filtered.columns.tolist()}")

                    summary_df = df_filtered.groupby(desc_col_name, as_index=False).agg(
                        PlannedQuantity_Total=(plan_qty_col_name, 'sum'),
                        Input_Total=(input_qty_col_name, 'sum')
                    )
                    summary_df['Gap'] = summary_df['Input_Total'] - summary_df['PlannedQuantity_Total']
                    
                    context_ref['categories'] = summary_df[desc_col_name].tolist()
                    context_ref['planned_data'] = summary_df['PlannedQuantity_Total'].tolist()
                    context_ref['input_data'] = summary_df['Input_Total'].tolist()
                    context_ref['gap_data'] = summary_df['Gap'].tolist()
                    context_ref['data_exists'] = True
                else:
                    print(f"No data found for product type: {product_type}")

        except ValueError as ve:
            print(f"Data processing error (ValueError): {ve}")
            error_message = f'数据处理错误 (值错误): {str(ve)}'
        except KeyError as ke:
            print(f"Data processing error (KeyError): {ke}")
            error_message = f'数据处理错误 (键错误，可能列名不匹配): {str(ke)}'
        except Exception as e_proc:
            print(f"Unexpected error during data processing: {e_proc}")
            error_message = f'数据处理时发生意外错误：{str(e_proc)}'

    final_context = {
        'error_message': error_message,
        'holder_categories': holder_context_data['categories'],
        'holder_planned_data': holder_context_data['planned_data'],
        'holder_input_data': holder_context_data['input_data'],
        'holder_page_title': holder_context_data['page_title'],
        
        'charger_categories': charger_context_data['categories'],
        'charger_planned_data': charger_context_data['planned_data'],
        'charger_input_data': charger_context_data['input_data'],
        'charger_page_title': charger_context_data['page_title'],
    }
    
    if not error_message and not holder_context_data['data_exists'] and not charger_context_data['data_exists']:
        if not raw_data: 
             final_context['error_message'] = '存储过程 usp_GetDailyLineProductionSummary_rw 未返回任何相关数据。'
        else: 
             final_context['error_message'] = '未找到 "Hermes Holder BB" 或 "Hermes Charger BB" 的生产数据。'

    return render(request, 'dashboard/line_production_summary_chart.html', final_context)
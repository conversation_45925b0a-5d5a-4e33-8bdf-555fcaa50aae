{% extends "dashboard/base_dashboard.html" %}
{% load static %}

{% block title %}Volcano Weekly Production Dashboard{% endblock %}

{% block page_styles %}
<style>
    /* Override card-header style for this page */
    .card-header {
        padding: 1.25rem 1.5rem;
        border-bottom: 1px solid var(--border-color);
        display: flex;
        justify-content: center !important; /* Center title */
        align-items: center;
        text-align: center;
        width: 100%;
    }

    .card-header .card-title {
        width: 100%;
        text-align: center;
        font-size: 1.5rem; /* 增大标题字体 */
    }

    .chart-grid {
        display: grid;
        grid-template-columns: repeat(2, 1fr); /* Fixed two columns */
        gap: 2rem; /* Space between charts/cards */
        align-items: start; /* Align cards to the top */
        margin-bottom: 2rem; /* Add margin below the grid */
        width: 100%;
        box-sizing: border-box;
        padding-left: 2vw;
        padding-right: 2vw;
    }

    .chart-container {
        min-height: 260px;
        height: 32vh; /* Viewport height adaptive, for 4 charts on screen */
        max-height: 35vh; /* Slightly increased max-height */
    }

    /* 针对 4K 及超大屏幕优化 */
    @media (min-width: 3000px) {
        .chart-grid {
            max-width: 100vw !important; /* 允许全屏宽度 */
            padding-left: 1vw;
            padding-right: 1vw;
        }

        .chart-container {
            min-height: 400px;
            height: 40vh; /* 增加高度 */
            max-height: 45vh;
        }

        .card-header .card-title {
            font-size: 2rem; /* 超大屏幕更大的标题 */
        }

        .card-body {
            padding: 1rem 1.5rem 1.5rem 1.5rem;
        }

        .legend span {
            font-size: 1.2rem; /* 增大图例文字 */
        }
    }

    /* 针对 2K 及大屏幕优化 */
    @media (min-width: 1920px) and (max-width: 2999px) {
        .chart-grid {
            max-width: 100vw !important; /* 移除宽度限制 */
        }

        .chart-container {
            min-height: 320px;
            height: 38vh;
            max-height: 42vh;
        }

        .card-header .card-title {
            font-size: 1.8rem;
        }
    }

    .card {
        display: flex;
        flex-direction: column;
        height: 100%; /* Ensure card takes full height of its grid cell */
    }
    .card-body {
        flex: 1 1 auto; /* Allow card body to grow and shrink */
        display: flex;
        flex-direction: column;
        justify-content: flex-start; /* Align legend to top, chart below */
        padding: 0.5rem 1rem 1rem 1rem; /* Adjusted padding */
    }
    .legend { /* Styles for the HTML legend, if kept. ECharts legend is styled in JS. */
        margin-bottom: 0.5rem; /* Reduced margin if using ECharts legend */
    }


    @media (max-width: 900px) {
        .chart-grid {
            grid-template-columns: 1fr; /* Stack on smaller screens */
            gap: 1.5rem;
        }
        .chart-container {
            height: 40vh;
            min-height: 280px; /* Increased min-height for smaller screens */
        }
    }
</style>
{% endblock %}

{% block content %}
<div class="dashboard-header">
    <h1 class="dashboard-title">Volcano Weekly Plan VS Actual</h1>
</div>

<div class="chart-grid" style="grid-template-columns: repeat(2, 1fr); width: 98vw; max-width: 100vw; margin: 0 auto;">
    <div class="card">
        <div class="card-header">
            <h2 class="card-title">{{ bb_charger_chart.page_title|default:"Hermes Charger Plan vs Actual" }}</h2>
        </div>
        <div class="card-body">
            <div class="legend" style="margin-bottom: 8px; display: flex; align-items: center; gap: 18px;">
                <span style="display: flex; align-items: center;">
                    <span style="display:inline-block;width:14px;height:14px;background:linear-gradient(0deg, var(--green-primary), var(--green-dark));border-radius:2px;margin-right:5px;"></span>
                    Actual
                </span>
                <span style="display: flex; align-items: center;">
                    <span style="display:inline-block;width:14px;height:14px;background:linear-gradient(0deg, var(--blue-primary), var(--blue-secondary));border-radius:2px;margin-right:5px;"></span>
                    Positive Gap
                </span>
                <span style="display: flex; align-items: center;">
                    <span style="display:inline-block;width:14px;height:14px;background:linear-gradient(0deg, #FFA500, #FF7F00);border-radius:2px;margin-right:5px;"></span>
                    Negative Gap
                </span>
                <span style="display: flex; align-items: center;">
                    <span style="display:inline-block;width:14px;height:14px;background:var(--gray-light);border-radius:2px;margin-right:5px;"></span>
                    Remain Plan
                </span>

            </div>
            {% if bb_charger_chart.categories %}
                <div id="chartBBCharger" class="chart-container"></div>
                {{ bb_charger_chart.categories|json_script:"bb_charger_categories_data" }}
                {{ bb_charger_chart.planned_data|json_script:"bb_charger_planned_data" }}
                {{ bb_charger_chart.actual_data|json_script:"bb_charger_actual_data" }}
                {{ bb_charger_chart.gap_abs_data|json_script:"bb_charger_gap_abs_data" }}
                {{ bb_charger_chart.unmet_target_data|json_script:"bb_charger_unmet_data" }}
                {{ bb_charger_chart.gap_sign_data|json_script:"bb_charger_gap_sign_data" }}
                {{ bb_charger_chart.y_axis_max|json_script:"bb_charger_y_axis_max_data" }}
            {% else %}
                <div class="no-data">No Hermes Charger data available.</div>
            {% endif %}
        </div>
    </div>

    <div class="card">
        <div class="card-header">
            <h2 class="card-title">{{ bb_holder_chart.page_title|default:"Hermes Holder Plan vs Actual" }}</h2>
        </div>
        <div class="card-body">
            <div class="legend" style="margin-bottom: 8px; display: flex; align-items: center; gap: 18px;">
                <span style="display: flex; align-items: center;">
                    <span style="display:inline-block;width:14px;height:14px;background:linear-gradient(0deg, var(--green-primary), var(--green-dark));border-radius:2px;margin-right:5px;"></span>
                    Actual
                </span>
                <span style="display: flex; align-items: center;">
                    <span style="display:inline-block;width:14px;height:14px;background:linear-gradient(0deg, var(--blue-primary), var(--blue-secondary));border-radius:2px;margin-right:5px;"></span>
                    Positive Gap
                </span>
                <span style="display: flex; align-items: center;">
                    <span style="display:inline-block;width:14px;height:14px;background:linear-gradient(0deg, #FFA500, #FF7F00);border-radius:2px;margin-right:5px;"></span>
                    Negative Gap
                </span>
                <span style="display: flex; align-items: center;">
                    <span style="display:inline-block;width:14px;height:14px;background:var(--gray-light);border-radius:2px;margin-right:5px;"></span>
                    Remain Plan
                </span>

            </div>
            {% if bb_holder_chart.categories %}
                <div id="chartBBHolder" class="chart-container"></div>
                {{ bb_holder_chart.categories|json_script:"bb_holder_categories_data" }}
                {{ bb_holder_chart.planned_data|json_script:"bb_holder_planned_data" }}
                {{ bb_holder_chart.actual_data|json_script:"bb_holder_actual_data" }}
                {{ bb_holder_chart.gap_abs_data|json_script:"bb_holder_gap_abs_data" }}
                {{ bb_holder_chart.unmet_target_data|json_script:"bb_holder_unmet_data" }}
                {{ bb_holder_chart.gap_sign_data|json_script:"bb_holder_gap_sign_data" }}
                {{ bb_holder_chart.y_axis_max|json_script:"bb_holder_y_axis_max_data" }}
            {% else %}
                <div class="no-data">No Hermes Holder data available.</div>
            {% endif %}
        </div>
    </div>

    <div class="card">
        <div class="card-header">
            <h2 class="card-title">{{ packing_chart.page_title|default:"Packing Plan vs Actual" }}</h2>
        </div>
        <div class="card-body">
            <div class="legend" style="margin-bottom: 8px; display: flex; align-items: center; gap: 18px;">
                <span style="display: flex; align-items: center;">
                    <span style="display:inline-block;width:14px;height:14px;background:linear-gradient(0deg, var(--green-primary), var(--green-dark));border-radius:2px;margin-right:5px;"></span>
                    Actual
                </span>
                <span style="display: flex; align-items: center;">
                    <span style="display:inline-block;width:14px;height:14px;background:linear-gradient(0deg, var(--blue-primary), var(--blue-secondary));border-radius:2px;margin-right:5px;"></span>
                    Positive Gap
                </span>
                <span style="display: flex; align-items: center;">
                    <span style="display:inline-block;width:14px;height:14px;background:linear-gradient(0deg, #FFA500, #FF7F00);border-radius:2px;margin-right:5px;"></span>
                    Negative Gap
                </span>
                <span style="display: flex; align-items: center;">
                    <span style="display:inline-block;width:14px;height:14px;background:var(--gray-light);border-radius:2px;margin-right:5px;"></span>
                    Remain Plan
                </span>

            </div>
            <div id="chartPacking" class="chart-container"></div>
            {{ packing_chart.categories|json_script:"packing_categories_data" }}
            {{ packing_chart.planned_data|json_script:"packing_planned_data" }}
            {{ packing_chart.actual_data|json_script:"packing_actual_data" }}
            {{ packing_chart.gap_abs_data|json_script:"packing_gap_abs_data" }}
            {{ packing_chart.unmet_target_data|json_script:"packing_unmet_data" }}
            {{ packing_chart.gap_sign_data|json_script:"packing_gap_sign_data" }}
            {{ packing_chart.y_axis_max|json_script:"packing_y_axis_max_data" }}
        </div>
    </div>

    <div class="card">
        <div class="card-header">
            <h2 class="card-title">{{ bb_alpha_chart.page_title|default:"Alpha BB Plan vs Actual" }}</h2>
        </div>
        <div class="card-body">
            <div class="legend" style="margin-bottom: 8px; display: flex; align-items: center; gap: 18px;">
                <span style="display: flex; align-items: center;">
                    <span style="display:inline-block;width:14px;height:14px;background:linear-gradient(0deg, var(--green-primary), var(--green-dark));border-radius:2px;margin-right:5px;"></span>
                    Actual
                </span>
                <span style="display: flex; align-items: center;">
                    <span style="display:inline-block;width:14px;height:14px;background:linear-gradient(0deg, var(--blue-primary), var(--blue-secondary));border-radius:2px;margin-right:5px;"></span>
                    Positive Gap
                </span>
                <span style="display: flex; align-items: center;">
                    <span style="display:inline-block;width:14px;height:14px;background:linear-gradient(0deg, #FFA500, #FF7F00);border-radius:2px;margin-right:5px;"></span>
                    Negative Gap
                </span>
                <span style="display: flex; align-items: center;">
                    <span style="display:inline-block;width:14px;height:14px;background:var(--gray-light);border-radius:2px;margin-right:5px;"></span>
                    Remain Plan
                </span>

            </div>
            {% if bb_alpha_chart.categories %}
                <div id="chartBBAlpha" class="chart-container"></div>
                {{ bb_alpha_chart.categories|json_script:"bb_alpha_categories_data" }}
                {{ bb_alpha_chart.planned_data|json_script:"bb_alpha_planned_data" }}
                {{ bb_alpha_chart.actual_data|json_script:"bb_alpha_actual_data" }}
                {{ bb_alpha_chart.gap_abs_data|json_script:"bb_alpha_gap_abs_data" }}
                {{ bb_alpha_chart.unmet_target_data|json_script:"bb_alpha_unmet_data" }}
                {{ bb_alpha_chart.gap_sign_data|json_script:"bb_alpha_gap_sign_data" }}
                {{ bb_alpha_chart.y_axis_max|json_script:"bb_alpha_y_axis_max_data" }}
            {% else %}
                <div class="no-data">暂无 Alpha BB 生产数据</div>
            {% endif %}
        </div>
    </div>
</div>
{% endblock %}

{% block page_scripts %}
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Global chart manager (optional, but good for cleanup)
        window.volcanoCharts = {};

        // Enhanced getJsonData from base_dashboard.html (assuming it's robust)
        // If not, you can use the more robust one defined in previous examples.
        // function getJsonData(id) { ... } (already in base_dashboard.html)

        function initDashboard() {
            console.log('Starting Volcano weekly dashboard initialization...');

            // Global charts container if not exists
            if (!window.volcanoCharts) {
                window.volcanoCharts = {};
            }

            const chartConfigs = [
                {
                    id: 'chartPacking',
                    prefix: 'packing',
                    noDataMessage: 'Weekly Packing data is currently unavailable.'
                },
                {
                    id: 'chartBBAlpha',
                    prefix: 'bb_alpha',
                    noDataMessage: 'Weekly Alpha BB data is currently unavailable.'
                },
                {
                    id: 'chartBBCharger',
                    prefix: 'bb_charger',
                    noDataMessage: 'Weekly Hermes Charger data is currently unavailable.'
                },
                {
                    id: 'chartBBHolder',
                    prefix: 'bb_holder',
                    noDataMessage: 'Weekly Hermes Holder data is currently unavailable.'
                }
            ];

            chartConfigs.forEach(config => {
                try {
                    const categories = getJsonData(`${config.prefix}_categories_data`);
                    if (categories && Array.isArray(categories) && categories.length > 0) {
                        const planned = getJsonData(`${config.prefix}_planned_data`);
                        const actual = getJsonData(`${config.prefix}_actual_data`);
                        const gapAbs = getJsonData(`${config.prefix}_gap_abs_data`);
                        const unmet = getJsonData(`${config.prefix}_unmet_data`); // Corresponds to 'unmet_target_data' in your HTML
                        const gapSign = getJsonData(`${config.prefix}_gap_sign_data`);
                        const yAxisMax = getJsonData(`${config.prefix}_y_axis_max_data`);

                        console.log(`Data for ${config.id}:`, { categories, planned, actual, gapAbs, unmet, gapSign, yAxisMax });

                        renderProductionChart(config.id, categories, planned, actual, gapAbs, unmet, gapSign, yAxisMax);
                    } else {
                        const chartElement = document.getElementById(config.id);
                        if (chartElement) {
                            chartElement.innerHTML = `<div class="no-data" style="padding-top: 40%;">${config.noDataMessage}</div>`;
                        }
                        console.warn(`No categories data for ${config.id}. Chart will not be rendered.`);
                    }
                } catch (error) {
                    console.error(`Error initializing chart ${config.id}:`, error);
                    const chartElement = document.getElementById(config.id);
                    if (chartElement) {
                        chartElement.innerHTML = `<div class="error-message">Error loading chart data</div>`;
                    }
                }
            });
        }

        function renderProductionChart(chartId, categories, plannedData, actualData, gapAbsData, unmetData, gapSignData, yAxisMaxValueParam) {
            try {
                const element = document.getElementById(chartId);
                if (!element) {
                    console.error(`Chart element not found: ${chartId}`);
                    return;
                }

                // Cleanup existing chart instance if any
                if (window.volcanoCharts && window.volcanoCharts[chartId]) {
                    try {
                        window.volcanoCharts[chartId].dispose();
                    } catch (e) {
                        console.warn(`Failed to dispose existing chart ${chartId}:`, e);
                    }
                }

                // Ensure ECharts is loaded
                if (!window.echarts) {
                    console.error("ECharts library is not loaded!");
                    element.innerHTML = `<div class="no-data" style="padding-top: 40%;">Chart library missing.</div>`;
                    return;
                }

                // Set fixed size for init then resize in rAF, or rely on CSS for initial size
                element.style.width = '100%'; // Ensure it takes up card-body width
                element.style.height = '100%'; // Ensure it takes up card-body height

                requestAnimationFrame(() => {
                    try {
                        // It's crucial to initialize ECharts AFTER the DOM element is visible and has dimensions.
                        const chart = echarts.init(element, null, {
                            renderer: 'svg', // Changed to SVG for potentially sharper rendering
                            // Width and height will be taken from the container by default
                        });
                        window.volcanoCharts[chartId] = chart;

                        // 创建图表选项时使用动态字体大小
                        const fontSizeBase = window.innerWidth >= 3000 ? 16 : (window.innerWidth >= 1920 ? 14 : 12);
                        const labelFontSize = window.innerWidth >= 3000 ? 15 : (window.innerWidth >= 1920 ? 13 : 11);

                        // Utility functions for data normalization
                        const utils = {
                            normalizeNumber: (val, defaultValue = 0) => {
                                if (val === null || val === undefined) return defaultValue;
                                const num = Number(val);
                                return isNaN(num) ? defaultValue : num;
                            },
                            normalizeString: (val, defaultValue = '') => {
                                if (val === null || val === undefined) return defaultValue;
                                return String(val);
                            },
                            normalizeArray: (arr, mapperFn, defaultValueForElement) => {
                                if (!arr || !Array.isArray(arr)) {
                                    console.warn(`Expected array but got: ${typeof arr}`, arr);
                                    return [];
                                }
                                return arr.map(item => mapperFn(item, defaultValueForElement));
                            },
                            ensureLength: (arr, length, defaultValue) => {
                                if (!arr || !Array.isArray(arr)) {
                                    console.warn(`Cannot ensure length of non-array: ${typeof arr}`, arr);
                                    return Array(length || 0).fill(defaultValue);
                                }
                                const result = [...arr];
                                while (result.length < length) result.push(defaultValue);
                                return result.slice(0, length); // Truncate if longer
                            }
                        };

                        const dataLength = (categories && Array.isArray(categories) && categories.length > 0) ? categories.length : 0;
                        if (dataLength === 0) {
                            console.warn(`No category data for chart ${chartId}. Rendering empty chart or message.`);
                            element.innerHTML = `<div class="no-data" style="padding-top: 40%;">No data to display for categories.</div>`;
                            return;
                        }

                        const finalCategories = utils.ensureLength(utils.normalizeArray(categories, utils.normalizeString, ''), dataLength, '');
                        const finalPlanned = utils.ensureLength(utils.normalizeArray(plannedData, utils.normalizeNumber, 0), dataLength, 0);
                        const finalActual = utils.ensureLength(utils.normalizeArray(actualData, utils.normalizeNumber, 0), dataLength, 0);
                        const finalGapAbs = utils.ensureLength(utils.normalizeArray(gapAbsData, utils.normalizeNumber, 0), dataLength, 0);
                        const finalUnmet = utils.ensureLength(utils.normalizeArray(unmetData, utils.normalizeNumber, 0), dataLength, 0);
                        const finalGapSign = utils.ensureLength(utils.normalizeArray(gapSignData, utils.normalizeString, 'positive'), dataLength, 'positive');

                        // Calculate dynamic Y-axis max value based on the highest bar plus some padding
                        let calculatedMax = 0;

                        // Calculate the visual top Y value for each bar category
                        // This will be used as the anchor for the 'Plan Background' labels
                        const visualTopYvalues = [];
                        for (let i = 0; i < dataLength; i++) {
                            const planned_val = finalPlanned[i] || 0;
                            const actual_val = finalActual[i] || 0;
                            const gap_abs_val = finalGapAbs[i] || 0;
                            // const unmet_val = finalUnmet[i] || 0; 
                            // const is_positive = finalGapSign[i] === 'positive'; // Old condition

                            // New condition: Align with label formatter's logic for showing a '+' prefix.
                            // The formatter effectively treats falsy finalGapSign[i] (like '') as positive for the prefix.
                            const effectivelyPositiveSign = (finalGapSign[i] || 'positive') === 'positive';

                            let topY = 0;
                            if (effectivelyPositiveSign) { // Actual > Plan, or sign indicates positive treatment
                                topY = actual_val + gap_abs_val;
                            } else { // Actual <= Plan or sign is explicitly negative
                                topY = planned_val;
                            }
                            visualTopYvalues.push(topY);

                            // Update calculatedMax logic to use this consistent topY for Y-axis scaling
                            // This replaces the previous complex totalHeight calculation within the loop
                            calculatedMax = Math.max(calculatedMax, topY);
                        }

                        // Find the maximum value considering plan, actual, positive gaps, and remain plan
                        // The loop above for visualTopYvalues already calculates the effective top for each bar.
                        // So, calculatedMax can be derived from visualTopYvalues.
                        // However, the original loop for calculatedMax iterates through various components, let's ensure consistency or simplify.
                        // The existing calculatedMax logic:
                        /*
                        for (let i = 0; i < dataLength; i++) {
                            const planned = finalPlanned[i] || 0;
                            const actual = finalActual[i] || 0;
                            const gap = finalGapAbs[i] || 0;
                            const unmet = finalUnmet[i] || 0;
                            const isPositiveGap = finalGapSign[i] === 'positive';

                            const totalHeight = Math.max(
                                planned,
                                actual + (isPositiveGap ? gap : 0),
                                actual + unmet
                            );
                            calculatedMax = Math.max(calculatedMax, totalHeight);
                        }
                        */
                        // The new calculatedMax derived from visualTopYvalues should be equivalent if visualTopYvalues correctly represents the max height.
                        // The loop for visualTopYvalues now also assigns to calculatedMax.

                        // Add 15% padding to the top for labels and better visualization
                        calculatedMax = Math.ceil(calculatedMax * 1.15);

                        // Round to a nice number (e.g., nearest 100 or 1000 depending on scale)
                        const roundTo = calculatedMax > 10000 ? 1000 : (calculatedMax > 1000 ? 100 : 10);
                        calculatedMax = Math.ceil(calculatedMax / roundTo) * roundTo;

                        // Use the provided yAxisMaxValueParam if available, otherwise use our calculated value
                        // with a minimum default of 100 to avoid empty charts
                        const yAxisMaxValue = utils.normalizeNumber(yAxisMaxValueParam, Math.max(calculatedMax, 100));

                        const colors = {
                            actual: new echarts.graphic.LinearGradient(0, 0, 0, 1, [{ offset: 0, color: 'var(--green-primary)' }, { offset: 1, color: 'var(--green-dark)' }]),
                            gap_pos: new echarts.graphic.LinearGradient(0, 0, 0, 1, [{ offset: 0, color: 'var(--blue-primary)' }, { offset: 1, color: 'var(--blue-secondary)' }]),
                            gap_neg: new echarts.graphic.LinearGradient(0, 0, 0, 1, [{ offset: 0, color: '#FFA500' }, { offset: 1, color: '#FF7F00' }]),
                            unmet: 'var(--gray-light)'
                        };

                        // 将图表系列按照我们需要的顺序排列
                        const seriesOrder = [
                            // 透明的计划背景（用于显示标签）
                            {
                                name: 'Plan Background',
                                type: 'bar',
                                stack: 'plan',
                                data: visualTopYvalues, // Use the calculated visual top Y values
                                itemStyle: { color: 'transparent' },
                                barWidth: '50%',
                                z: 10,
                                label: {
                                    show: true,
                                    position: 'top',
                                    distance: 10, // 增加与柱子的距离
                                    offset: [0, -5], // 向上偏移5像素
                                    formatter: function(params) {
                                        var idx = params.dataIndex;
                                        var planned = finalPlanned[idx] || 0;
                                        var gap = finalGapAbs[idx] || 0;
                                        var gapSign = (finalGapSign && finalGapSign[idx]) ? finalGapSign[idx] : 'positive';
                                        var gapPrefix = gapSign === 'positive' ? '+' : '-';
                                        return '{plan|Week Plan: ' + planned + '}\n' + 
                                              '{gap|Gap: ' + gapPrefix + gap + '}';
                                    },
                                    rich: {
                                        plan: { 
                                            color: '#fff', 
                                            fontWeight: 'bold', 
                                            fontSize: labelFontSize + 4, 
                                            padding: [0, 0, 2, 0],
                                            textShadowColor: 'rgba(0,0,0,0.8)',
                                            textShadowBlur: 4,
                                            textShadowOffsetY: 1,
                                            textBorderWidth: 2,
                                            textBorderColor: 'rgba(0,0,0,0.3)'
                                        },
                                        gap: { 
                                            color: '#fff', 
                                            fontWeight: 'bold', 
                                            fontSize: labelFontSize + 2, 
                                            padding: [0, 0, 0, 0],
                                            textShadowColor: 'rgba(0,0,0,0.8)',
                                            textShadowBlur: 4,
                                            textShadowOffsetY: 1,
                                            textBorderWidth: 2,
                                            textBorderColor: 'rgba(0,0,0,0.3)'
                                        }
                                    }
                                }
                            },
                            // 1. 实际产量（绿色）- 在底部
                            {
                                name: 'Actual',
                                type: 'bar',
                                stack: 'stack1',
                                data: finalActual,
                                itemStyle: { color: 'var(--green-primary)' },
                                barWidth: '50%',
                                z: 2,
                                label: {
                                    show: true,
                                    position: 'inside',
                                    formatter: function(params) {
                                        return params.value;
                                    },
                                    color: '#fff',
                                    fontWeight: 'bold',
                                    fontSize: labelFontSize + 6,
                                    textShadowColor: 'rgba(0,0,0,0.5)',
                                    textShadowBlur: 3,
                                    textBorderWidth: 1,
                                    textBorderColor: 'rgba(0,0,0,0.2)'
                                }
                            },
                            // 2. 正向Gap（蓝色）- 在中间
                            {
                                name: 'Positive Gap',
                                type: 'bar',
                                stack: 'stack1',
                                data: finalGapAbs.map((value, index) => {
                                    // 只有当Gap为正时显示，并且堆叠在Actual上方
                                    if (finalGapSign[index] === 'positive' && value > 0) {
                                        return value;
                                    }
                                    return 0;
                                }),
                                itemStyle: { color: 'var(--blue-primary)' },
                                barWidth: '50%',
                                z: 3
                            },
                            // 3. 负向Gap（橙色）- 在中间
                            {
                                name: 'Negative Gap',
                                type: 'bar',
                                stack: 'stack1',
                                data: finalGapAbs.map((value, index) => {
                                    // 只有当Gap为负时显示，并且堆叠在Actual上方
                                    if (finalGapSign[index] === 'negative' && value > 0) {
                                        return value;
                                    }
                                    return 0;
                                }),
                                itemStyle: { color: '#FFA500' },
                                barWidth: '50%',
                                z: 3
                            },
                            // 4. 剩余计划（灰色）- 在顶部
                            {
                                name: 'Remain Plan',
                                type: 'bar',
                                stack: 'stack1',
                                data: finalUnmet,
                                itemStyle: { color: 'var(--gray-light)' },
                                barWidth: '50%',
                                z: 2,
                                label: {
                                    show: true,
                                    position: 'inside',
                                    formatter: function(params) {
                                        if (params.value > 0) {
                                            return params.value;
                                        }
                                        return '';
                                    },
                                    color: '#fff',
                                    fontWeight: 'bold',
                                    fontSize: labelFontSize + 4,
                                    textShadowColor: 'rgba(0,0,0,0.7)',
                                    textShadowBlur: 4,
                                    textShadowOffsetY: 1
                                }
                            }
                        ];
                        
                        // 创建图表选项时使用动态字体大小
                        const option = {
                            backgroundColor: 'transparent',
                            // 关闭动画，避免动画过程中访问 undefined 的 length
                            animation: false,
                            tooltip: {
                                trigger: 'axis',
                                axisPointer: { type: 'shadow' },
                                formatter: function (params) {
                                    if (!params || !Array.isArray(params) || params.length === 0 || !params[0]) return '';
                                    const categoryIndex = params[0].dataIndex;
                                    if (categoryIndex === undefined || categoryIndex === null) return '';

                                    const categoryName = finalCategories && finalCategories[categoryIndex] || 'N/A';
                                    const plan = finalPlanned && finalPlanned[categoryIndex] || 0;
                                    const actual = finalActual && finalActual[categoryIndex] || 0;
                                    const gap = actual - plan;
                                    const remainPlan = finalUnmet && finalUnmet[categoryIndex] || 0;

                                    let tooltipText = `<div style="font-weight:bold;font-size:${labelFontSize}px;margin-bottom:8px;">${categoryName}</div>`;
                                    tooltipText += `<div style="display:flex;justify-content:space-between;margin:5px 0;"><span>Weekly Plan:</span><span style="font-weight:bold;">${plan}</span></div>`;
                                    tooltipText += `<div style="display:flex;justify-content:space-between;margin:5px 0;"><span>Weekly Actual:</span><span style="font-weight:bold;color:${gap >= 0 ? 'var(--green-primary)' : 'var(--red-primary)'};">${actual}</span></div>`;
                                    tooltipText += `<div style="display:flex;justify-content:space-between;margin:5px 0;"><span>Gap:</span><span style="font-weight:bold;color:${gap >= 0 ? 'var(--blue-primary)' : 'var(--red-primary)'};">${gap}</span></div>`;
                                    tooltipText += `<div style="display:flex;justify-content:space-between;margin:5px 0;"><span>Remain Plan:</span><span style="font-weight:bold;">${remainPlan}</span></div>`;
                                    if (plan > 0) {
                                         tooltipText += `<div style="display:flex;justify-content:space-between;margin:5px 0;"><span>Completion:</span><span style="font-weight:bold;">${Math.round((actual/plan)*100)}%</span></div>`;
                                    }
                                    return tooltipText;
                                },
                                backgroundColor: 'var(--bg-card)', borderColor: 'var(--border-color)',
                                textStyle: {
                                    color: 'var(--text-primary)',
                                    fontSize: labelFontSize
                                },
                                extraCssText: 'box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15); border-radius: 8px; padding: 12px;'
                            },
                            legend: {
                                show: false
                            },
                            grid: { left: '3%', right: '4%', bottom: '3%', top: '20%', containLabel: true }, // Adjusted top for legend
                            xAxis: [{
                                type: 'category', data: finalCategories,
                                axisTick: { alignWithLabel: true, lineStyle: { color: 'var(--border-color)' } },
                                axisLine: { lineStyle: { color: 'var(--border-color)' } },
                                axisLabel: {
                                    color: 'var(--text-secondary)', interval: 0, rotate: 30, margin: 12, fontSize: fontSizeBase,
                                    formatter: function(value) {
                                        if (typeof value === 'string') {
                                            return value.length > 15 ? value.substring(0, 15) + '...' : value;
                                        }
                                        return '';
                                    }
                                }
                            }],
                            yAxis: [{
                                type: 'value', name: 'Quantity', max: yAxisMaxValue,
                                nameTextStyle: {
                                    color: 'var(--text-secondary)',
                                    fontSize: fontSizeBase + 1,
                                    padding: [0, 0, 0, -10]
                                },
                                splitLine: { lineStyle: { color: 'var(--border-color)', type: 'dashed' } },
                                axisLine: { show: true, lineStyle: { color: 'var(--border-color)' } },
                                axisLabel: {
                                    color: 'var(--text-secondary)',
                                    fontSize: fontSizeBase,
                                    formatter: function(value) { return value; }
                                }
                            }],
                            series: seriesOrder,
                        };

                        // Validate data integrity before setting options
                        if (!option.xAxis[0].data || !option.series[0].data ||
                            !option.series[1].data || !option.series[2].data ||
                            !option.series[3].data || !option.series[4].data) {
                            console.error(`Invalid chart data for ${chartId}. Data missing in series.`);
                            element.innerHTML = `<div class="error-message">Error: Invalid chart data</div>`;
                            return;
                        }

                        // Ensure all series data arrays have the same length
                        const seriesLengths = [
                            option.series[0].data.length,
                            option.series[1].data.length,
                            option.series[2].data.length,
                            option.series[3].data.length,
                            option.series[4].data.length
                        ];

                        if (!seriesLengths.every(length => length === seriesLengths[0])) {
                            console.error(`Data length mismatch in series for ${chartId}`);
                            // Try to fix the issue by ensuring all series are the same length
                            const maxLength = Math.max(...seriesLengths);
                            option.series.forEach(series => {
                                if (Array.isArray(series.data) && series.data.length < maxLength) {
                                    while (series.data.length < maxLength) {
                                        series.data.push(0);
                                    }
                                }
                            });
                        }

                        chart.setOption(option, true); // `true` to not merge with previous options

                        // Resize listener
                        let resizeTimer;
                        const debouncedResize = () => {
                            clearTimeout(resizeTimer);
                            resizeTimer = setTimeout(() => {
                                if (chart && !chart.isDisposed()) {
                                    chart.resize();
                                }
                            }, 250);
                        };
                        window.addEventListener('resize', debouncedResize);

                        // Cleanup on element removal (if page is dynamic, not strictly needed for full page loads)
                        element._chartCleanup = () => {
                            window.removeEventListener('resize', debouncedResize);
                            if (chart && !chart.isDisposed()) {
                                chart.dispose();
                            }
                            delete window.volcanoCharts[chartId];
                        };

                        console.log(`Chart ${chartId} initialized and options set.`);
                    } catch (err) {
                        console.error(`Error rendering chart ${chartId}:`, err);
                        if (element) {
                            element.innerHTML = `<div class="error-message">Chart rendering failed: ${err.message}</div>`;
                        }
                    }
                });
            } catch (err) {
                console.error(`Error rendering chart ${chartId}:`, err);
                if (element) {
                    element.innerHTML = `<div class="error-message">Chart rendering failed: ${err.message}</div>`;
                }
            }
        }

        // Initialize all charts on the dashboard
        initDashboard();

        // Optional: Cleanup charts if the page is left (for SPAs or dynamic content)
        // window.addEventListener('beforeunload', function() {
        //     Object.values(window.volcanoCharts).forEach(chart => {
        //         if (chart && !chart.isDisposed()) {
        //             chart.dispose();
        //         }
        //     });
        //     window.volcanoCharts = {};
        // });
    });
</script>
{% endblock %}
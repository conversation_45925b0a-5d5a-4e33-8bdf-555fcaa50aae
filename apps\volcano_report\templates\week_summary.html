{% extends 'base1.html' %}
{% load static %}

{% block head %}
<!-- 修改为本地 DataTables CSS 引用 -->
<link rel="stylesheet" type="text/css" href="{% static 'plugins/datatables/jquery.dataTables.min.css' %}">
<link rel="stylesheet" type="text/css" href="{% static 'plugins/datatables/extensions/FixedHeader/css/fixedHeader.dataTables.min.css' %}">
<link rel="stylesheet" type="text/css" href="{% static 'plugins/datatables/extensions/FixedColumns/css/fixedColumns.dataTables.min.css' %}">
{% endblock %}

{% block content %}
<div class="app-container">
    <h1 class="page-title">Volcano周产出汇总数据</h1>

    <div class="form-section">
        <form method="get" class="form-inline">
            <div class="form-layout">
                <div class="form-group">
                    <label for="week_range">选择周期</label>
                    <select name="week_range" class="form-control" id="week_range" onchange="this.form.submit()">
                        {% for value, label in week_choices %}
                        <option value="{{ value }}" {% if value == selected_range %}selected{% endif %}>
                            {{ label }}
                        </option>
                        {% endfor %}
                    </select>
                </div>
                <div class="action-buttons">
                    <a href="{% url 'volcanoreport:export_week_summary' %}?start_date={{ start_date }}&end_date={{ end_date }}" 
                       class="btn btn-info">
                        <i class="fas fa-file-excel"></i> 导出Excel
                    </a>
                </div>
            </div>
        </form>
    </div>

    <!-- 数据表格 -->
    <div class="table-section">
        <div class="table-container">
            <table class="table table-striped" id="summaryTable" style="width:100%">
                <thead>
                    <tr>
                        <th style="min-width:120px">线别</th>
                        <th style="min-width:120px">类型</th>
                        <th style="min-width:120px">站别</th>
                        <th style="min-width:150px">料号</th>
                        <th class="text-right" style="min-width:100px">实际产出</th>
                        <th class="text-right" style="min-width:100px">计划产出</th>
                        <th class="text-right" style="min-width:100px">差异</th>
                        <th class="text-right" style="min-width:80px">停线次数</th>
                        <th class="text-right" style="min-width:100px">停线时长(分钟)</th>
                        <th class="text-right" style="min-width:100px">达成率</th>
                    </tr>
                </thead>
                <tbody>
                    {% for item in summary_data %}
                    <tr>
                        <td>{{ item.Line }}</td>
                        <td>{{ item.Type }}</td>
                        <td>{{ item.Station }}</td>
                        <td>{{ item.PartNumber }}</td>
                        <td class="text-right">{{ item.total_output }}</td>
                        <td class="text-right">{{ item.total_planqty }}</td>
                        <td class="text-right {% if item.gap < 0 %}text-danger{% endif %}">
                            {{ item.gap }}
                        </td>
                        <td class="text-right">{{ item.StopCount|default_if_none:0 }}</td>
                        <td class="text-right">{{ item.StopTime|default_if_none:0 }} 分钟</td>
                        <td class="text-right">
                            {% if item.total_planqty > 0 %}
                                {% widthratio item.total_output item.total_planqty 100 %}%
                            {% else %}
                                -
                            {% endif %}
                        </td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
    </div>
</div>

<style>
.app-container {
    padding: 20px;
    position: relative;
    max-width: 100%;
    height: 95vh;  /* 设置容器高度为视窗高度的95% */
    display: flex;
    flex-direction: column;
}

.page-title {
    text-align: center;
    margin-bottom: 15px;
    font-size: 24px;
    color: #333;
    font-weight: 500;
}

.form-section {
    background: #fff;
    padding: 10px 20px;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    margin-bottom: 10px;
}

.form-layout {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.form-group {
    margin: 0;
}

.form-control {
    width: 300px;
}

.table-section {
    background: #fff;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    padding: 0;
    margin-top: 10px;
    flex: 1;  /* 让表格区域占据剩余空间 */
    display: flex;
    flex-direction: column;
}

.table-container {
    margin: 0;
    padding: 0;
    width: 100%;
    flex: 1;  /* 让表格容器占据剩余空间 */
    overflow: hidden;  /* 防止出现双滚动条 */
}

/* DataTables 样式覆盖 */
.dataTables_wrapper {
    padding: 10px;
    height: 100%;
    display: flex;
    flex-direction: column;
}

.dataTables_filter {
    margin-bottom: 10px;
}

.dataTables_filter input {
    border: 1px solid #ddd;
    border-radius: 4px;
    padding: 6px 12px;
}

table.dataTable thead th {
    background-color: #f8f9fa;
    border-bottom: 2px solid #dee2e6;
    font-weight: 600;
    padding: 8px 12px;
    white-space: nowrap;
}

table.dataTable tbody td {
    padding: 6px 12px;
    vertical-align: middle;
}

.text-right {
    text-align: right;
}

.text-danger {
    color: #dc3545;
    font-weight: bold;
}

/* 固定表头样式 */
.dataTables_scrollHead {
    background: #fff;
    border-bottom: 2px solid #dee2e6;
}

/* 滚动条样式 */
.dataTables_scrollBody::-webkit-scrollbar {
    width: 8px;
    height: 8px;
}

.dataTables_scrollBody::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 4px;
}

.dataTables_scrollBody::-webkit-scrollbar-thumb {
    background: #888;
    border-radius: 4px;
}

.dataTables_scrollBody::-webkit-scrollbar-thumb:hover {
    background: #555;
}

/* 添加固定列的样式 */
.dataTables_wrapper .DTFC_LeftWrapper {
    background-color: white;
    border-right: 1px solid #dee2e6;
}

.DTFC_LeftBodyLiner {
    background-color: white;
}

/* 确保表格占满容器 */
.dataTable {
    width: 100% !important;
    margin: 0 !important;
}

/* 调整固定列的阴影效果 */
.DTFC_LeftWrapper::after {
    content: '';
    position: absolute;
    top: 0;
    right: 0;
    bottom: 0;
    width: 4px;
    background: linear-gradient(to right, rgba(0,0,0,0.1), rgba(0,0,0,0));
    pointer-events: none;
}

.dataTables_scrollBody {
    flex: 1;  /* 让滚动区域占据剩余空间 */
    min-height: 0;  /* 修复 Firefox 中的滚动问题 */
}
</style>

{% endblock %}

{% block extra_js %}
<!-- 修改为本地 DataTables JS 引用 -->
<!-- <script type="text/javascript" src="{% static 'plugins/datatables/jquery.dataTables.min.js' %}"></script>
<script type="text/javascript" src="{% static 'plugins/datatables/dataTables.fixedHeader.min.js' %}"></script>
<script type="text/javascript" src="{% static 'plugins/datatables/dataTables.fixedColumns.min.js' %}"></script> -->

<script>
$(document).ready(function() {
    // 初始化DataTables
    var table = $('#summaryTable').DataTable({
        dom: '<"top"f>rt<"bottom"lp><"clear">',  // 自定义布局
        language: {
            search: "搜索:",
            zeroRecords: "没有找到匹配的记录",
            info: "显示第 _START_ 至 _END_ 项结果，共 _TOTAL_ 项",
            infoEmpty: "显示第 0 至 0 项结果，共 0 项",
            infoFiltered: "(由 _MAX_ 项结果过滤)",
            lengthMenu: "显示 _MENU_ 项结果",
            paginate: {
                first: "首页",
                previous: "上页",
                next: "下页",
                last: "末页"
            }
        },
        pageLength: -1,  // 显示所有记录
        ordering: true,  // 启用排序
        order: [[0, 'asc']],  // 默认按线别排序
        scrollY: '75vh',  // 增加垂直滚动高度
        scrollX: true,   // 水平滚动
        scrollCollapse: true,
        fixedHeader: true,
        fixedColumns: {
            left: 2  // 固定前两列
        },
        columnDefs: [
            { 
                targets: [4,5,6], 
                className: 'text-right'
            },
            {
                targets: [7,8],  // 停线次数和停线时长列
                className: 'text-right',
                render: function(data, type, row, meta) {
                    if (type === 'display') {
                        if (meta.col === 7) {  // 停线次数列
                            return data || '0';
                        } else if (meta.col === 8) {  // 停线时长列
                            // 移除可能已经添加的"分钟"后缀
                            let value = String(data || '0').replace('分钟', '').trim();
                            return value + ' 分钟';
                        }
                    }
                    // 用于排序和过滤的值
                    if (meta.col === 8) {
                        return parseFloat(String(data || '0').replace('分钟', '').trim());
                    }
                    return data || 0;
                }
            }
        ],
        initComplete: function() {
            // 调整列宽
            this.api().columns.adjust();
        }
    });

    // 响应窗口调整大小
    $(window).on('resize', function() {
        table.columns.adjust();
    });

    // 添加双击行展开/收起详细信息的功能
    $('#summaryTable tbody').on('dblclick', 'tr', function() {
        var tr = $(this);
        var row = table.row(tr);

        if (row.child.isShown()) {
            row.child.hide();
            tr.removeClass('shown');
        } else {
            row.child(format(row.data())).show();
            tr.addClass('shown');
        }
    });
});

// 格式化详细信息的函数
function format(d) {
    return '<div class="row-details">' +
           '<p><strong>线别:</strong> ' + d[0] + '</p>' +
           '<p><strong>类型:</strong> ' + d[1] + '</p>' +
           '<p><strong>站别:</strong> ' + d[2] + '</p>' +
           '<p><strong>料号:</strong> ' + d[3] + '</p>' +
           '</div>';
}
</script>
{% endblock %} 
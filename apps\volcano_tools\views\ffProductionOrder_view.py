from django.shortcuts import render
from django.contrib import messages
from ..models import ffProductionOrder, ffProductionOrderDetail
from django import forms
from django.db import transaction
from ..views.udtonholddevice_view import add_fs_language

class ProductionOrderForm(forms.Form):
    production_order_numbers = forms.CharField(
        widget=forms.Textarea(attrs={'rows': 5}),
        label='Production Order Numbers',
        help_text='每行输入一个Production Order Number'
    )
    reason = forms.CharField(
        widget=forms.Textarea(attrs={'rows': 3}),
        label='Reason',
        required=False,
        help_text='请输入原因'
    )
    # 复选框
    hold_16 = forms.BooleanField(label='Hold 16', required=False)
    hold_first_packing_43 = forms.BooleanField(label='HoldFirstPacking 43', required=False)
    hold_carton_40 = forms.BooleanField(label='HoldCarton 40', required=False)
    hold_qa_approved_31 = forms.BooleanField(label='HoldQAApproved 31', required=False)

def production_order_query(request):
    form = ProductionOrderForm(request.session.get('search_form_data') if request.method == 'GET' else None)
    results = []

    if request.method == 'POST':
        form = ProductionOrderForm(request.POST)
        if form.is_valid():
            action = request.POST.get('action')
            
            if action == 'reset':
                form = ProductionOrderForm()
            elif action == 'search':
                results = search_action(request, form)
            elif action == 'insert':
                results = insert_action(request, form)
            elif action == 'release':
                results = release_action(request, form)

            request.session['search_form_data'] = request.POST

    return render(request, 'production_order/production_order.html', {
        'form': form,
        'results': results
    })

def insert_action(request, form):
    try:
        with transaction.atomic():
            order_numbers = [num.strip() for num in form.cleaned_data['production_order_numbers'].split('\n') if num.strip()]
            reason = form.cleaned_data['reason']
            
            # 获取选中的DetailDefIDs
            selected_def_ids = []
            if form.cleaned_data['hold_16']:
                selected_def_ids.append(16)
            if form.cleaned_data['hold_first_packing_43']:
                selected_def_ids.append(43)
            if form.cleaned_data['hold_carton_40']:
                selected_def_ids.append(40)
            if form.cleaned_data['hold_qa_approved_31']:
                selected_def_ids.append(31)

            if not selected_def_ids and not reason:
                messages.error(request, '请至少选择一个Hold类型或输入原因')
                return []

            # 生成原因码
            retcode = add_fs_language(reason, 'ffProductionOrderDetail')

            # 获取或创建工单
            for order_number in order_numbers:
                try:
                    order = ffProductionOrder.objects.get(ProductionOrderNumber=order_number)
                except ffProductionOrder.DoesNotExist:
                    messages.error(request, f"工单号 {order_number} 不存在，请确认后再试。")
                    continue

                # 处理选中的Hold记录
                for def_id in selected_def_ids:
                    detail, created = ffProductionOrderDetail.objects.get_or_create(
                        ProductionOrderID=order.ID,
                        ProductionOrderDetailDefID=def_id,
                        defaults={'Content': 'ON'}
                    )
                    if not created:
                        detail.Content = 'ON'
                        detail.save()

                # 处理原因
                if reason:
                    # 存储原始原因文本到DetailDefID=2
                    detail, created = ffProductionOrderDetail.objects.get_or_create(
                        ProductionOrderID=order.ID,
                        ProductionOrderDetailDefID=2,
                        defaults={'Content': reason}
                    )
                    if not created:
                        detail.Content = reason
                        detail.save()

                    # 存储原因码到DetailDefID=33
                    detail, created = ffProductionOrderDetail.objects.get_or_create(
                        ProductionOrderID=order.ID,
                        ProductionOrderDetailDefID=33,
                        defaults={'Content': str(retcode)}
                    )
                    if not created:
                        detail.Content = str(retcode)
                        detail.save()

            messages.success(request, f'成功处理 {len(order_numbers)} 个工单的Hold记录')
            return search_action(request, form)

    except Exception as e:
        messages.error(request, f'处理出错: {str(e)}')
        return []

def search_action(request, form):
    try:
        order_numbers = [num.strip() for num in form.cleaned_data['production_order_numbers'].split('\n') if num.strip()]
        
        orders = ffProductionOrder.objects.filter(ProductionOrderNumber__in=order_numbers)
        
        results = []
        for order in orders:
            details = {
                'ID': order.ID,
                'Hold': None,
                'HoldFirstPackage': None,
                'HoldCarton': None,
                'HoldQAapprove': None,
                'ErrorCode': None,
                'OnholdReason': None
            }
            
            order_details = ffProductionOrderDetail.objects.filter(
                ProductionOrderID=order.ID,
                ProductionOrderDetailDefID__in=[16, 43, 40, 31, 33, 2]
            )
            
            detail_mapping = {
                16: 'Hold',
                43: 'HoldFirstPackage',
                40: 'HoldCarton',
                31: 'HoldQAapprove',
                33: 'ErrorCode',
                2: 'OnholdReason'
            }
            
            for detail in order_details:
                field_name = detail_mapping.get(detail.ProductionOrderDetailDefID)
                if field_name:
                    details[field_name] = detail.Content
            
            result = {
                'ProductionOrderNumber': order.ProductionOrderNumber,
                **details
            }
            results.append(result)
            
        return results
        
    except Exception as e:
        messages.error(request, f'查询出错: {str(e)}')
        return []

def release_action(request, form):
    try:
        with transaction.atomic():
            order_numbers = [num.strip() for num in form.cleaned_data['production_order_numbers'].split('\n') if num.strip()]
            
            # 获取选中的DetailDefIDs
            selected_def_ids = []
            if form.cleaned_data['hold_16']:
                selected_def_ids.append(16)
            if form.cleaned_data['hold_first_packing_43']:
                selected_def_ids.append(43)
            if form.cleaned_data['hold_carton_40']:
                selected_def_ids.append(40)
            if form.cleaned_data['hold_qa_approved_31']:
                selected_def_ids.append(31)

            if not selected_def_ids:
                messages.error(request, '请至少选择一个Hold类型')
                return []

            # 获取工单
            for order_number in order_numbers:
                try:
                    order = ffProductionOrder.objects.get(ProductionOrderNumber=order_number)
                except ffProductionOrder.DoesNotExist:
                    messages.error(request, f"工单号 {order_number} 不存在，请确认后再试。")
                    continue

                # 只处理选中的Hold记录，将状态更新为OFF
                for def_id in selected_def_ids:
                    detail = ffProductionOrderDetail.objects.filter(
                        ProductionOrderID=order.ID,
                        ProductionOrderDetailDefID=def_id
                    ).first()
                    
                    if detail:
                        detail.Content = 'OFF'
                        detail.save()

            messages.success(request, f'成功Release {len(order_numbers)} 个工单的Hold记录')
            return search_action(request, form)

    except Exception as e:
        messages.error(request, f'Release出错: {str(e)}')
        return []
from django.shortcuts import render, redirect
from django.contrib import messages
from ..udtSNLinkPO_dao import SNLinkPODao

dao = SNLinkPODao()

def udtSNLinkPO_view(request):
    if request.method == "POST":
        action = request.POST.get("action")
        sn_list = request.POST.get("sn_list", "").strip().splitlines()
        sn_list = [sn.strip() for sn in sn_list if sn.strip()]
        po = request.POST.get("po", "").strip()
        snc = request.POST.get("snc", "").strip()
        requestBy = request.POST.get("requestBy", "").strip()
        context = {"sn_list": "\n".join(sn_list), "po": po, "snc": snc, "requestBy": requestBy}
        remark = f"{po} bind SN list {snc} {requestBy}"

        try:
            if action == "query":
                if po:
                    data = dao.query_udtSNLinkPO(sn_list, ponumber=po, query_type="ponumber")
                else:
                    data = dao.query_udtSNLinkPO(sn_list, query_type="snlist")
                if not data:
                    messages.info(request, "没有找到相关记录")
                context["data"] = data

            elif action == "insert":
                if not (sn_list and snc and requestBy):
                    messages.warning(request, "SN列表、SNC编号和申请人不能为空！")
                else:
                    poid = dao.query_ponumber(po)
                    if poid == 0:
                        messages.warning(request, "PO号无效！")
                    else:
                        dao.insert_LinkPO(sn_list, poid, snc, requestBy, remark)
                        messages.success(request, "插入成功！")
                        # 查询最新数据
                        context["data"] = dao.query_udtSNLinkPO(sn_list, query_type="snlist")

            elif action == "update":
                if not sn_list:
                    messages.warning(request, "SN列表不能为空！")
                else:
                    dao.update_LinkPO(sn_list)
                    messages.success(request, "更新成功！")
                    context["data"] = dao.query_udtSNLinkPO(sn_list, query_type="snlist")

            elif action == "clear":
                context = {}

        except Exception as e:
            messages.error(request, f"操作失败：{str(e)}")

        return render(request, "udtSNLinkPO/index.html", context)

    # GET 请求
    return render(request, "udtSNLinkPO/index.html")

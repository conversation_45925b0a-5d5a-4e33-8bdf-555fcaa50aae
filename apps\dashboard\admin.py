from django.contrib import admin
from import_export.admin import ImportExportModelAdmin
from import_export import resources, fields
from import_export.widgets import ForeignKeyWidget
from .models import UDTProductionPlanByHour, ffLine
from django.db import transaction

class UDTProductionPlanByHourResource(resources.ModelResource):
    line = fields.Field(
        column_name='line',
        attribute='line',
        widget=ForeignKeyWidget(ffLine, 'Description')
    )

    class Meta:
        model = UDTProductionPlanByHour
        fields = ('line', 'PlanDate', 'PlanHour', 'PlannedQuantity')
        export_order = ('line', 'PlanDate', 'PlanHour', 'PlannedQuantity')
        import_id_fields = [] # 确保总是创建新记录
        skip_unchanged = True
        report_skipped = False
        use_bulk = True  # 启用批量创建
        batch_size = 100  # 设置批量处理大小

@admin.register(UDTProductionPlanByHour)
class UDTProductionPlanByHourAdmin(ImportExportModelAdmin):
    resource_class = UDTProductionPlanByHourResource
    
    list_display = ('PlanDate', 'PlanHour', 'line', 'PlannedQuantity', 'createdat', 'updatedat')
    list_filter = ('PlanDate', 'line', )
    search_fields = ('line__Description', ) 
    ordering = ('-PlanDate', '-PlanHour', 'line')
    date_hierarchy = 'PlanDate'
    readonly_fields = ('createdat', 'updatedat')
    
    # 设置导入时使用事务和批量操作
    def get_import_resource_kwargs(self, request, *args, **kwargs):
        return {
            'use_transactions': True,
            'batch_size': 100,
        }

    def formfield_for_foreignkey(self, db_field, request, **kwargs):
        if db_field.name == "line":
            kwargs["queryset"] = ffLine.objects.all() 
        return super().formfield_for_foreignkey(db_field, request, **kwargs)
{% load static %}
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{{ page_title|default:"Prime Holder Detail" }}</title>
    <link rel="stylesheet" type="text/css" href="{% static 'plugins/datatables/fixedHeader.dataTables.min.css' %}">
    <link rel="stylesheet" type="text/css" href="{% static 'plugins/datatables/fixedColumns.dataTables.min.css' %}">
    <link rel="stylesheet" href="{% static 'bootstrap/css/bootstrap.min.css' %}">
    <script src="{% static 'bootstrap/js/bootstrap.bundle.min.js' %}"></script>
    <script src="{% static 'js/jquery-3.6.1.min.js' %}"></script>
    <script src="{% static 'plugins/datatables/jquery.dataTables.min.js' %}"></script>
    <script src="{% static 'plugins/datatables/dataTables.fixedHeader.min.js' %}"></script>
    <script src="{% static 'plugins/datatables/dataTables.fixedColumns.min.js' %}"></script>
    <script src="{% static 'js/xlsx.full.min.js' %}"></script>
    <style>
        body {
            background-color: #121212;
            color: #e0e0e0;
            font-family: 'Montserrat', sans-serif;
            margin: 0;
            padding: 0;
            background-image:
                radial-gradient(circle at 20% 30%, rgba(41, 98, 255, 0.03) 0%, transparent 50%),
                radial-gradient(circle at 80% 70%, rgba(255, 41, 112, 0.03) 0%, transparent 50%);
        }

        header {
            text-align: center;
            padding: 20px 0;
            position: relative;
            overflow: hidden;
            background: linear-gradient(to right, rgba(30, 30, 30, 0.9), rgba(40, 40, 40, 0.9));
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
        }
        h1 {
            color: #ffffff;
            font-weight: 600;
            margin: 0;
            font-size: 28px;
            letter-spacing: 1px;
            position: relative;
            text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
        }
        .container {
            max-width: 95%;
            margin: 20px auto;
            padding: 20px;
            background: linear-gradient(145deg, #1e1e1e, #252525);
            border-radius: 16px;
            box-shadow: 0 0 18px 3px rgba(74, 144, 226, 0.4);
        }
        .back-button {
            position: absolute;
            left: 20px;
            top: 50%;
            transform: translateY(-50%);
            background: linear-gradient(145deg, #4a90e2, #3a7bc9);
            color: white;
            border: none;
            border-radius: 50%;
            width: 40px;
            height: 40px;
            font-size: 18px;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.3);
            transition: all 0.3s ease;
        }
        .back-button:hover {
            transform: translateY(-50%) scale(1.1);
        }
        table.dataTable {
            width: 100% !important;
            color: #e0e0e0;
            border-collapse: separate;
            border-spacing: 0;
            border-radius: 8px;
            overflow: hidden;
            margin: 20px 0;
        }
        table.dataTable thead th {
            background-color: #333;
            color: #fff;
            border-bottom: 2px solid #4a90e2;
            padding: 12px 10px;
            font-weight: 600;
            text-align: left;
        }

        table.dataTable tbody td {
            padding: 10px;
            border-bottom: 1px solid #333;
            vertical-align: middle;
            color: #e0e0e0;
        }

        /* MODIFIED: Removed !important from striping background-color */
        table.dataTable.stripe tbody tr.odd td {
            background-color: #2a2a2a; /* No !important */
        }
        table.dataTable.stripe tbody tr.even td {
            background-color: #252525; /* No !important */
        }

        table.dataTable tbody tr:hover td {
            background-color: rgba(74, 144, 226, 0.2) !important; /* Hover can override normal and aging colors */
        }

        /* Styling for TotalQty column (9th visual column, index 8) */
        table.dataTable tbody td:nth-child(9) {
            font-weight: bold;
        }

        .dataTables_wrapper {
            color: #e0e0e0;
        }

        table.dataTable thead .sorting:after,
        table.dataTable thead .sorting_asc:after,
        table.dataTable thead .sorting_desc:after {
            color: #4a90e2;
        }
        .dataTables_wrapper .dataTables_length,
        .dataTables_wrapper .dataTables_filter,
        .dataTables_wrapper .dataTables_info,
        .dataTables_wrapper .dataTables_processing,
        .dataTables_wrapper .dataTables_paginate {
            color: #e0e0e0;
            margin: 10px 0;
        }
        .dataTables_wrapper .dataTables_filter input {
            background-color: #333;
            color: #fff;
            border: 1px solid #555;
            border-radius: 4px;
            padding: 5px 10px;
            margin-left: 10px;
        }
        .dataTables_wrapper .dataTables_length select {
            background-color: #333;
            color: #fff;
            border: 1px solid #555;
            border-radius: 4px;
            padding: 5px 10px;
        }
        .dataTables_wrapper .dataTables_paginate .paginate_button {
            color: #e0e0e0 !important;
            border: 1px solid #333;
            border-radius: 4px;
            background: linear-gradient(to bottom, #333 0%, #222 100%);
            margin: 0 2px;
            padding: 6px 12px;
            text-shadow: none;
        }
        .dataTables_wrapper .dataTables_paginate .paginate_button:hover {
            background: linear-gradient(to bottom, #4a90e2 0%, #3a7bc9 100%);
            color: white !important;
            border: 1px solid #3a7bc9;
            box-shadow: 0 0 5px rgba(74, 144, 226, 0.5);
        }
        .dataTables_wrapper .dataTables_paginate .paginate_button.current,
        .dataTables_wrapper .dataTables_paginate .paginate_button.current:hover {
            background: linear-gradient(to bottom, #4a90e2 0%, #3a7bc9 100%);
            color: white !important;
            border: 1px solid #3a7bc9;
            font-weight: bold;
            box-shadow: 0 0 8px rgba(74, 144, 226, 0.7);
        }
        .dataTables_wrapper .dataTables_paginate .paginate_button.disabled,
        .dataTables_wrapper .dataTables_paginate .paginate_button.disabled:hover {
            color: #666 !important;
            background: #222;
            border: 1px solid #333;
            cursor: default;
            box-shadow: none;
        }

        .aging-green {
            background-color: #4CAF50 !important;
            color: #ffffff !important;
            font-weight: bold !important;
        }
        .aging-yellow {
            background-color: #FFC107 !important;
            color: #000000 !important;
            font-weight: bold !important;
        }
        .aging-red {
            background-color: #F44336 !important;
            color: #ffffff !important;
            font-weight: bold !important;
        }

        /* 控制按钮和图例的容器 */
        .controls-container {
            display: flex;
            align-items: center;
            justify-content: space-between;
            gap: 20px;
            margin-bottom: 20px;
            padding: 15px;
            background-color: #2c2c2c;
            border-radius: 8px;
            border: 1px solid #444;
        }

        /* Excel 下载按钮样式 */
        .excel-download-btn {
            background: linear-gradient(145deg, #28a745, #20a83a);
            color: white;
            border: none;
            border-radius: 6px;
            padding: 10px 20px;
            font-size: 14px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            box-shadow: 0 2px 8px rgba(40, 167, 69, 0.3);
            min-width: 120px;
            height: 40px;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .excel-download-btn:hover {
            background: linear-gradient(145deg, #20a83a, #1e7e34);
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(40, 167, 69, 0.4);
        }

        /* 图例容器 */
        .aging-legend-bar {
            display: flex;
            gap: 12px;
            align-items: center;
        }

        /* 图例项目样式 */
        .legend-item {
            padding: 10px 16px;
            border-radius: 6px;
            font-size: 14px;
            font-weight: 600;
            text-align: center;
            min-width: 120px;
            height: 40px;
            display: flex;
            align-items: center;
            justify-content: center;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
        }

        .summary-info {
            background-color: rgba(74, 144, 226, 0.1);
            border-left: 4px solid #4a90e2;
            padding: 15px;
            margin-bottom: 20px;
            border-radius: 4px;
        }

        @media (max-width: 768px) {
            .container {
                padding: 10px;
            }
            h1 {
                font-size: 26px;
            }
            .controls-container {
                flex-direction: column;
                align-items: stretch;
                gap: 15px;
            }
            .aging-legend-bar {
                justify-content: center;
                flex-wrap: wrap;
            }
            .legend-item {
                flex: 1;
                min-width: 100px;
            }
            .excel-download-btn {
                width: 100%;
            }
        }
    </style>
</head>
<body>
    <header>
        <!-- 添加返回按钮 dashboard/pcbasupermarket/-->
        <!-- <button class="back-button" onclick="window.history.back()" title="返回">←</button> -->
        <h1>{{ page_title|default:"Assembly Supermarket PCBA Detail" }}</h1>
    </header>


        <script id="detail-data-all" type="application/json">{{ detail_data|safe }}</script>
        {% if error_message %}
            <div style="background-color: rgba(255, 0, 0, 0.1); border-left: 4px solid #ff5252; padding: 15px; margin: 20px; border-radius: 4px;">
                <p style="color: #ff5252; margin: 0;">{{ error_message }}</p>
            </div>
        {% endif %}

        <div class="container">
            <!-- 控制按钮和图例在同一行 -->
            <div class="controls-container">
                <button id="exportExcelBtn" class="btn btn-primary">下载 Excel</button>

                <div class="aging-legend-bar">
                    <span class="legend-item aging-green">0~2 Days</span>
                    <span class="legend-item aging-yellow">2~3 Days</span>
                    <span class="legend-item aging-red">&gt;3 Days</span>
                </div>
            </div>

            <table id="holderDetailTable" class="display stripe" style="width:100%">
                <thead>
                    <tr>
                        <th>Project</th>        <th>ReelNo</th>         <th>PartNumber</th>     <th>Quantity</th>       <th>DateCode</th>       <th>LotCode</th>        <th>Supplier</th>       <th>QuantityByD/C</th>  <th>TotalQty</th>       <th>Location</th>       <th>AgingDay</th>       <th>OnShelfTime</th>    <th>OnShelfUser</th>    <th>Status</th>         <th>PAOwner</th>        </tr>
                </thead>
                <tbody>
                    {% for item in detail_data %}
                    <tr>
                        <td>{{ item.Project|default:"-" }}</td>
                        <td>{{ item.ReelNo|default:"-" }}</td>
                        <td>{{ item.PartNumber|default:"-" }}</td>
                        <td>{{ item.Quantity|default:"-" }}</td>
                        <td>{{ item.DateCode|default:"-" }}</td>
                        <td>{{ item.LotCode|default:"-" }}</td>
                        <td>{{ item.Supplier|default:"-" }}</td>
                        <td>{{ item.QuantityByDC|default:"-" }}</td>
                        <td>{{ item.TotalQty|default:"-" }}</td>      <td>{{ item.Location|default:"-" }}</td>     <td>{% if item.AgingDay is not None %}{{ item.AgingDay }}{% else %}-{% endif %}</td>     <td>{{ item.OnShelfTime|default:"-" }}</td>
                        <td>{{ item.OnShelfUser|default:"-" }}</td>
                        <td>{{ item.Status|default:"-" }}</td>
                        <td>{{ item.PAOwner|default:"-" }}</td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
    <script>
        $(document).ready(function() {
            $('#holderDetailTable').DataTable({
                dom: '<"top"lf>rt<"bottom"ip><"clear">',
                language: {
                    search: "搜索：",
                    zeroRecords: "没有找到匹配的记录",
                    info: "显示第 _START_ 至 _END_ 项结果，共 _TOTAL_ 项",
                    infoEmpty: "显示第 0 至 0 项结果，共 0 项",
                    infoFiltered: "(由 _MAX_ 项结果过滤)",
                    lengthMenu: "显示 _MENU_ 项结果",
                    paginate: {
                        first: "首页",
                        previous: "上页",
                        next: "下页",
                        last: "末页"
                    }
                },
                paging: false,
                ordering: true,
                order: [[8, 'desc']],
                scrollY: '60vh',
                scrollX: true,
                scrollCollapse: true,
                fixedHeader: true,
                stripeClasses: ['odd', 'even'],
                rowCallback: function(row, data, index) {
                    // TotalQty column (9th column visually, index 8) - bold
                    $('td:eq(8)', row).css('font-weight', 'bold');

                    // AgingDay column color coding (11th column visually, index 10)
                    var agingDayCell = $('td:eq(10)', row);
                    var agingDayValue = agingDayCell.text().trim();

                    agingDayCell.removeClass('aging-green aging-yellow aging-red');

                    if (agingDayValue !== '-' && agingDayValue !== '') {
                        var agingDays = parseFloat(agingDayValue);

                        if (!isNaN(agingDays) && agingDays >= 0) {
                            if (agingDays >= 0 && agingDays <= 2) {
                                agingDayCell.addClass('aging-green');
                            } else if (agingDays > 2 && agingDays <= 3) {
                                agingDayCell.addClass('aging-yellow');
                            } else if (agingDays > 3) {
                                agingDayCell.addClass('aging-red');
                            }
                        }
                    }
                }
            });

            $('.dataTables_length, .dataTables_filter, .dataTables_info, .dataTables_paginate').css({
                'color': '#e0e0e0'
            });
            $('.dataTables_filter input, .dataTables_length select').css({
                'background-color': '#333',
                'color': '#fff',
                'border': '1px solid #555',
                'border-radius': '4px',
                'padding': '5px 10px'
            });
        });

        document.getElementById('exportExcelBtn').addEventListener('click', function() {
            var table = document.getElementById('holderDetailTable');
            var wb = XLSX.utils.table_to_book(table, {sheet: 'Sheet1'});
            var pageTitle = document.title || 'PrimeHolderDetail';
            var now = new Date();
            var ts = now.getFullYear() + ('0'+(now.getMonth()+1)).slice(-2) + ('0'+now.getDate()).slice(-2) + '_' + ('0'+now.getHours()).slice(-2) + ('0'+now.getMinutes()).slice(-2) + ('0'+now.getSeconds()).slice(-2);
            var filename = pageTitle.replace(/\s+/g, '_') + '_' + ts + '.xlsx';
            XLSX.writeFile(wb, filename);
        });
    </script>
</body>
</html>
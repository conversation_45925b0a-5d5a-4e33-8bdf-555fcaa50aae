from django.db import models

# Create your models here.
from django.db import models

# Create your models here.

from django.db import models


class MyManager(models.Manager):
    def get_queryset(self):
        return super().get_queryset().using('VolcanoFFDB')


class FFPart(models.Model):

    PartNumber = models.CharField(max_length=255)
    # 下面的可为空

    Revision = models.CharField(max_length=255, null=True, blank=True)
    Description = models.TextField(null=True, blank=True)
    PartFamilyID = models.IntegerField(null=True, blank=True)
    UOM = models.CharField(max_length=255, null=True, blank=True)
    # 下面的默认值为1,
    # IsUnit bit 可选是0和1

    IsUnit = models.SmallIntegerField(choices=[(0, 'No'), (1, 'Yes')], default=0)
    # 下面的默认值为1
    Status = models.SmallIntegerField(default=1, null=True, blank=True)

    class Meta:
        db_table = 'FFPart'
        ordering = ['id']
        managed = False  # 表示该模型映射一个现有的数据库表。


'''
sqlserver table:FFPartdetail
ID	int
PartID	int
PartDetailDefID	int
Content	varchar

'''


class FFPartdetail(models.Model):

    # ID = models.IntegerField()
    # PartID = models.IntegerField()
    PartID = models.IntegerField(db_column='PartID')
    PartDetailDefID = models.IntegerField()
    Content = models.TextField()

    class Meta:
        db_table = 'FFPartdetail'
        managed = False



class luPartDetailDef(models.Model):

    Description = models.TextField()

    class Meta:
        db_table = 'luPartDetailDef'
        managed = False

    def __str__(self):
        # 这将决定下拉列表中每个选项的显示方式
        # 例如："1 - Some content here"
        return f"{self.id} - {self.Description}"


class FfSerialNumber(models.Model):

    # UnitID = models.ForeignKey(FfUnit, on_delete=models.CASCADE)
    # unitid = models.IntegerField(blank=False, null=False)
    unitid = models.IntegerField(primary_key=True)
    SerialNumberTypeID = models.IntegerField()
    Value = models.CharField(max_length=200)

    class Meta:
        db_table = 'ffSerialNumber'
        managed = False


class OnHoldDevice(models.Model):

    id = models.AutoField(primary_key=True, db_column="id")  # Django 内部这样处理隐式的自增主键
    unitid = models.IntegerField(blank=True, null=True)
    cartonID = models.IntegerField(blank=True, null=True)
    PalletID = models.IntegerField(blank=True, null=True)
    lastupdate = models.DateTimeField(blank=True, null=True)
    remark = models.CharField(max_length=200, blank=True, null=True)
    status = models.IntegerField(blank=True, null=True)
    isEngravingAssy = models.IntegerField(blank=True, null=True)
    isHoldCarton = models.IntegerField(blank=True, null=True)
    isHoldQAapproved = models.IntegerField(blank=True, null=True)
    retcode = models.IntegerField(blank=True, null=True)
    dynamiccode = models.CharField(max_length=50, blank=True, null=True)

    class Meta:
        db_table = 'udtOnHoldDevice'
        managed = False




class UdtUnbindNotMRB(models.Model):
    id = models.AutoField(primary_key=True, db_column='ID')
    unit_id = models.IntegerField(db_column='UnitID')
    serial_number = models.CharField(max_length=255, db_column='SerialNumber')
    snc_number = models.CharField(max_length=255, db_column='SNCNumber')
    creation_time = models.DateTimeField(db_column='CreationTime')
    last_update = models.DateTimeField(db_column='LastUpdate')
    remark = models.TextField(db_column='Remark')

    class Meta:
        managed = False  # 告诉Django这个模型是一个已存在的数据库表
        db_table = 'udtUnbindNotMRB'  # 指定数据库中的表名

    def __str__(self):
        return f"UdtUnbindNotMRB {self.id}: {self.serial_number}"

'''
fsLanguage
Column_name	Type
ID	int
Language	varchar
Type	varchar
Code	int
Description	nvarchar
Status	tinyint
'''
class FsLanguage(models.Model):
    id = models.AutoField(primary_key=True, db_column='ID')
    language = models.CharField(max_length=255, db_column='Language')
    type = models.CharField(max_length=255, db_column='Type')
    code = models.IntegerField(db_column='Code')
    description = models.TextField(db_column='Description')
    status = models.SmallIntegerField(db_column='Status')

    class Meta:
        managed = False
        db_table = 'fsLanguage'

    def __str__(self):
        return f"FsLanguage {self.id}: {self.language} - {self.type}"


class ffStationType(models.Model):
   ID = models.AutoField(primary_key=True)
   Description = models.CharField(max_length=50)

   def __str__(self):
       return f" {self.Description}"

   class Meta:
       db_table = 'ffStationType'  # This is the name of your table
       managed = False


class ffLine(models.Model):
   ID = models.AutoField(primary_key=True)
   Description = models.CharField(max_length=50)

   def __str__(self):
       return f" {self.Description}"

   class Meta:
       db_table = 'ffLine'  # This is the name of your table
       managed = False

class UdtFWCheckByLine(models.Model):
    id = models.AutoField(primary_key=True, db_column='ID')
    part_family_id = models.IntegerField(db_column='PartFamilyID')
    part_family = models.CharField(max_length=255, db_column='PartFamily')
    fw_ver = models.CharField(max_length=255, db_column='FWVer')
    creation_time = models.DateTimeField(db_column='CreationTime')
    last_update = models.DateTimeField(db_column='LastUpdate')
    line_id = models.IntegerField(db_column='LineID')
    looper_id = models.IntegerField(db_column='LooperID')
    reel_id = models.CharField(max_length=255, db_column='ReelID')
    qty = models.IntegerField(db_column='QTY')

    class Meta:
        managed = False
        db_table = 'udtFWCheckByLine'

    def __str__(self):
        return f"UdtFWCheckByLine {self.id}: {self.part_family} - {self.fw_ver}"


#Column_name	Type
# ffUnitState
# ID	int
# Description	varchar
class ffUnitState(models.Model):
    ID = models.AutoField(primary_key=True)
    Description = models.CharField(max_length=255)

    def __str__(self):
        return f"{self.Description}"

    class Meta:
        db_table = 'ffUnitState'
        managed = False

#ffProductionOrder
    # ID
    # ProductionOrderNumber
class ffProductionOrder(models.Model):
    ID = models.AutoField(primary_key=True)
    ProductionOrderNumber = models.CharField(max_length=255)

    class Meta:
        db_table = 'ffProductionOrder'
        managed = False

#ffProductionOrderDetail
# ID
# ProductionOrderID
# ProductionOrderDetailDefID
# Content
class ffProductionOrderDetail(models.Model):
    ID = models.AutoField(primary_key=True)
    ProductionOrderID = models.IntegerField()
    ProductionOrderDetailDefID = models.IntegerField()
    Content = models.TextField()

    class Meta:
        db_table = 'ffProductionOrderDetail'
        managed = False



class UdtMismatchLine(models.Model):
    ID = models.AutoField(primary_key=True)
    UnitID = models.CharField(max_length=100, null=True, blank=True)
    Serialnumber = models.CharField(max_length=100)
    
    class Meta:
        db_table = 'UdtMismatchLine'
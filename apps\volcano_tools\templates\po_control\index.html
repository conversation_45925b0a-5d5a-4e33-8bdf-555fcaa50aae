{% extends 'base_mdb.html' %}
{% load static %}

{% block title %}PO 绑定管理{% endblock %}

{% block extra_css %}
<style>
    .card {
        border-radius: 16px;
        box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
        background-color: white;
        padding: 2rem;
        margin-bottom: 2rem;
    }

    .form-outline {
        margin-bottom: 1.5rem;
        position: relative;
    }

    .form-outline .form-control {
        border: 2px solid #e0e6ed;
        border-radius: 8px;
        padding: 12px 16px;
        font-size: 14px;
        background-color: #ffffff;
        transition: all 0.3s ease;
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
    }

    .form-outline .form-control:focus {
        border-color: #3b82f6;
        box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
        background-color: #ffffff;
    }

    .form-outline .form-label {
        font-weight: 600;
        color: #374151;
        margin-bottom: 8px;
        font-size: 14px;
    }

    .form-section {
        background-color: #f8fafc;
        border-radius: 12px;
        padding: 1.5rem;
        margin-bottom: 2rem;
        border: 1px solid #e2e8f0;
    }

    .form-section-title {
        font-size: 16px;
        font-weight: 700;
        color: #1e293b;
        margin-bottom: 1rem;
        padding-bottom: 0.5rem;
        border-bottom: 2px solid #e2e8f0;
    }

    .table {
        border-radius: 8px;
        overflow: hidden;
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
    }

    .badge {
        padding: 0.5rem 0.75rem;
        border-radius: 6px;
        font-weight: 600;
    }

    .badge-success {
        background-color: #10b981;
    }

    .badge-error {
        background-color: #ef4444;
    }

    .btn-action {
        min-width: 80px;
        border-radius: 8px;
        font-weight: 600;
    }

    .btn-primary {
        background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
        border: none;
        border-radius: 8px;
        padding: 12px 24px;
        font-weight: 600;
        box-shadow: 0 4px 12px rgba(59, 130, 246, 0.3);
        transition: all 0.3s ease;
    }

    .btn-primary:hover {
        transform: translateY(-2px);
        box-shadow: 0 6px 16px rgba(59, 130, 246, 0.4);
    }
</style>
{% endblock %}

{% block content %}
<div class="row justify-content-center">
    <div class="col-12">
        <!-- 标题部分 -->
        <div class="text-center mb-4">
            <h2 class="fw-bold">PO 绑定管理 [udtCheckPOControl]</h2>
            <p class="text-muted">提示：Kit PO > DVC PO > DAC PO > Coil PO > SubFrame PO</p>
        </div>

        {% if messages %}
        <div class="mb-4">
            {% for message in messages %}
            <div class="alert {% if message.tags == 'error' %}alert-danger{% else %}alert-success{% endif %} shadow-4">
                {{ message }}
            </div>
            {% endfor %}
        </div>
        {% endif %}

        <!-- 主要内容卡片 -->
        <div class="card">
            <!-- 表单部分 -->
            <form method="post" id="mainForm" onsubmit="return validateForm()">
                {% csrf_token %}

                <!-- 查询条件区域 -->
                <div class="form-section">
                    <div class="form-section-title">查询条件</div>
                    <div class="row">
                        <div class="col-md-4">
                            <div class="form-outline">
                                <label class="form-label" for="kitpo">Kit PO</label>
                                <input type="text" id="kitpo" name="kitpo" class="form-control" value="{{ kitpo }}" placeholder="输入 Kit PO"/>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="form-outline">
                                <label class="form-label" for="bbpo">BB PO</label>
                                <input type="text" id="bbpo" name="bbpo" class="form-control" value="{{ bbpo }}" placeholder="输入 BB PO"/>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="form-outline">
                                <label class="form-label" for="subpo">Sub PO</label>
                                <input type="text" id="subpo" name="subpo" class="form-control" value="{{ subpo }}" placeholder="输入 Sub PO"/>
                            </div>
                        </div>
                    </div>
                    <div class="text-center mt-3">
                        <button type="button" class="btn btn-outline-primary me-2" onclick="performQuery()">
                            <i class="fas fa-search me-1"></i>查询
                        </button>
                        <button type="button" class="btn btn-outline-secondary" onclick="clearForm()">
                            <i class="fas fa-eraser me-1"></i>清空
                        </button>
                    </div>
                </div>

                <!-- 新增绑定区域 -->
                <div class="form-section">
                    <div class="form-section-title">新增绑定</div>
                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-outline">
                                <label class="form-label" for="request_by">Request By</label>
                                <input type="text" id="request_by" name="request_by" class="form-control" placeholder="输入请求人"/>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-outline">
                                <label class="form-label" for="request_snc">Request SNC</label>
                                <input type="text" id="request_snc" name="request_snc" class="form-control" placeholder="输入请求 SNC"/>
                            </div>
                        </div>
                    </div>
                    <div class="text-center mt-3">
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-plus me-1"></i>添加绑定
                        </button>
                    </div>
                </div>
            </form>

            <!-- 结果表格 -->
            {% if data %}
            <div class="table-responsive mt-5">
                <table class="table table-hover">
                    <thead class="table-light">
                        <tr>
                            <th class="text-center">ID</th>
                            <th>BB PO</th>
                            <th>Kit PO</th>
                            <th>Sub PO</th>
                            <th class="text-center">Status</th>
                            <th>Request By</th>
                            <th>Request SNC</th>
                            <th>Last Update</th>
                            <th class="text-center">Action</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for row in data %}
                        <tr>
                            <td class="text-center">{{ row.ID }}</td>
                            <td>{{ row.BBPO }}</td>
                            <td>{{ row.KitPO }}</td>
                            <td>{{ row.subPO }}</td>
                            <td class="text-center">
                                <span class="badge {% if row.Status == 0 %}badge-error{% else %}badge-success{% endif %}">
                                    {% if row.Status == 0 %}已绑定{% else %}已释放{% endif %}
                                </span>
                            </td>
                            <td>{{ row.RequestBy }}</td>
                            <td>{{ row.RequestSNC }}</td>
                            <td>{{ row.Lastupdate|date:"Y-m-d H:i" }}</td>
                            <td class="text-center">
                                {% if row.Status == 0 %}
                                <button type="button" 
                                        onclick="updateStatus('{{ row.ID }}', 1)" 
                                        class="btn btn-success btn-sm btn-action">
                                    释放
                                </button>
                                {% else %}
                                <button type="button" 
                                        onclick="updateStatus('{{ row.ID }}', 0)" 
                                        class="btn btn-danger btn-sm btn-action">
                                    绑定
                                </button>
                                {% endif %}
                            </td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
            {% endif %}
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
// 页面加载时初始化
document.addEventListener('DOMContentLoaded', function() {
    // 恢复保存的数据
    const savedRequestBy = localStorage.getItem('savedRequestBy');
    const savedRequestSnc = localStorage.getItem('savedRequestSnc');

    if (savedRequestBy) {
        document.getElementById('request_by').value = savedRequestBy;
    }
    if (savedRequestSnc) {
        document.getElementById('request_snc').value = savedRequestSnc;
    }
});

// 保存表单数据到 localStorage
function saveFormData() {
    const requestBy = document.getElementById('request_by').value;
    const requestSnc = document.getElementById('request_snc').value;

    localStorage.setItem('savedRequestBy', requestBy);
    localStorage.setItem('savedRequestSnc', requestSnc);
}

// 执行查询
function performQuery() {
    const kitpo = document.getElementById('kitpo').value.trim();
    const bbpo = document.getElementById('bbpo').value.trim();
    const subpo = document.getElementById('subpo').value.trim();

    // 构建查询参数
    const params = new URLSearchParams();
    if (kitpo) params.append('kitpo', kitpo);
    if (bbpo) params.append('bbpo', bbpo);
    if (subpo) params.append('subpo', subpo);

    // 跳转到查询结果页面
    window.location.href = window.location.pathname + '?' + params.toString();
}

// 清空表单
function clearForm() {
    document.getElementById('kitpo').value = '';
    document.getElementById('bbpo').value = '';
    document.getElementById('subpo').value = '';
    document.getElementById('request_by').value = '';
    document.getElementById('request_snc').value = '';

    // 清除保存的数据
    localStorage.removeItem('savedRequestBy');
    localStorage.removeItem('savedRequestSnc');
}

// 表单验证
function validateForm() {
    const bbpo = document.querySelector('input[name="bbpo"]').value.trim();
    const kitpo = document.querySelector('input[name="kitpo"]').value.trim();
    const subpo = document.querySelector('input[name="subpo"]').value.trim();
    const requestBy = document.querySelector('input[name="request_by"]').value.trim();
    const requestSnc = document.querySelector('input[name="request_snc"]').value.trim();

    if (!bbpo) {
        alert('BB PO 不能为空！');
        return false;
    }

    if (!kitpo && !subpo) {
        alert('Kit PO 和 Sub PO 至少需要填写一个！');
        return false;
    }

    if (!requestBy || !requestSnc) {
        alert('Request By 和 Request SNC 都必须填写！');
        return false;
    }

    // 如果验证通过，保存当前输入的值
    saveFormData();
    return true;
}

function updateStatus(id, status) {
    // 阻止事件冒泡
    event.preventDefault();
    event.stopPropagation();

    if (!confirm('确定要' + (status === 1 ? '释放' : '绑定') + '这条记录吗？')) {
        return;
    }

    // 禁用所有按钮，防止重复点击
    const buttons = document.querySelectorAll('button');
    buttons.forEach(button => button.disabled = true);

    fetch('{% url "volcano_tools:update_status" %}', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-CSRFToken': document.querySelector('[name=csrfmiddlewaretoken]').value
        },
        body: JSON.stringify({
            id: id,
            status: status
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            // 使用 window.location.href 确保完全刷新页面
            window.location.href = window.location.pathname + window.location.search;
        } else {
            alert('操作失败：' + data.error);
            // 操作失败时重新启用按钮
            buttons.forEach(button => button.disabled = false);
        }
    })
    .catch(error => {
        alert('操作失败：' + error);
        // 发生错误时重新启用按钮
        buttons.forEach(button => button.disabled = false);
    });

    return false;
}
</script>
{% endblock %}

{% extends 'daisyui_base.html' %}
{% load static %}

{% block content %}
<div class="flex flex-col items-center w-full">
    <!-- 标题部分 -->
    <div class="w-full text-center mb-8">
        <h2 class="text-2xl font-bold text-gray-800">PO绑定管理[udtCheckPOControl]</h2>
        提示：Kit PO > DVC PO > DAC PO >Coil PO >SubFrame PO
    </div>
    
    {% if messages %}
    <div class="w-full mb-6">
        {% for message in messages %}
        <div class="alert {% if message.tags == 'error' %}alert-error{% else %}alert-success{% endif %} shadow-lg">
            {{ message }}
        </div>
        {% endfor %}
    </div>
    {% endif %}
    
    <!-- 标签按钮居中 -->
    <div class="w-full text-center mb-6">
        <div class="inline-flex space-x-4">
            <button class="btn btn-sm" onclick="showTab('query')">查询</button>
            <button class="btn btn-sm btn-primary" onclick="showTab('insert')">新增</button>
        </div>
    </div>

    <!-- 主要内容卡片 -->
    <div class="w-full bg-white rounded-lg shadow-md p-6">
        <!-- 表单部分 -->
        <form method="get" id="mainForm" onsubmit="return validateForm()" class="max-w-2xl mx-auto">
            {% csrf_token %}
            
            <div class="space-y-4">
               
                
                <div class="form-control">
                    <label class="label">
                        <span class="label-text font-medium">Kit PO</span>
                    </label>
                    <input type="text" name="kitpo" 
                           value="{{ kitpo }}"
                           class="input input-bordered w-full" 
                           placeholder="输入Kit PO">
                </div>

                <div class="form-control">
                    <label class="label">
                        <span class="label-text font-medium">BB PO</span>
                    </label>
                    <input type="text" name="bbpo" 
                           value="{{ bbpo }}"
                           class="input input-bordered w-full" 
                           placeholder="输入BB PO">
                </div>
                
                <div class="form-control">
                    <label class="label">
                        <span class="label-text font-medium">Sub PO</span>
                    </label>
                    <input type="text" name="subpo" 
                           value="{{ subpo }}"
                           class="input input-bordered w-full" 
                           placeholder="输入Sub PO">
                </div>
                
                <div id="insertFields" style="display: none;" class="space-y-6">
                    <div class="form-control">
                        <label class="label">
                            <span class="label-text font-medium">Request By</span>
                        </label>
                        <input type="text" name="request_by" id="request_by"
                               class="input input-bordered w-full"
                               onchange="saveFormData()">
                    </div>
                    
                    <div class="form-control">
                        <label class="label">
                            <span class="label-text font-medium">Request SNC</span>
                        </label>
                        <input type="text" name="request_snc" id="request_snc"
                               class="input input-bordered w-full"
                               onchange="saveFormData()">
                    </div>
                </div>
            </div>

            <div class="mt-8 text-center">
                <button type="submit" class="btn btn-secondary w-32" id="submitBtn">查询</button>
            </div>
        </form>

        <!-- 结果表格 -->
        {% if data %}
        <div class="mt-8 overflow-x-auto w-full">
            <table class="table w-full">
                <thead>
                    <tr>
                        <th class="bg-base-200 text-center">ID</th>
                        <th class="bg-base-200">BB PO</th>
                        <th class="bg-base-200">Kit PO</th>
                        <th class="bg-base-200">Sub PO</th>
                        <th class="bg-base-200 text-center">Status</th>
                        <th class="bg-base-200">Request By</th>
                        <th class="bg-base-200">Request SNC</th>
                        <th class="bg-base-200">Last Update</th>
                        <th class="bg-base-200 text-center w-24">Action</th>
                    </tr>
                </thead>
                <tbody>
                    {% for row in data %}
                    <tr class="hover">
                        <td class="text-center">{{ row.ID }}</td>
                        <td>{{ row.BBPO }}</td>
                        <td>{{ row.KitPO }}</td>
                        <td>{{ row.subPO }}</td>
                        <td class="text-center">
                            <span class="badge {% if row.Status == 0 %}badge-error{% else %}badge-success{% endif %}">
                                {% if row.Status == 0 %}已绑定{% else %}已释放{% endif %}
                            </span>
                        </td>
                        <td>{{ row.RequestBy }}</td>
                        <td>{{ row.RequestSNC }}</td>
                        <td>{{ row.Lastupdate|date:"Y-m-d H:i" }}</td>
                        <td class="text-center">
                            {% if row.Status == 0 %}
                            <button type="button" 
                                    onclick="updateStatus('{{ row.ID }}', 1)" 
                                    class="btn btn-sm btn-success min-w-[80px]">
                                释放
                            </button>
                            {% else %}
                            <button type="button" 
                                    onclick="updateStatus('{{ row.ID }}', 0)" 
                                    class="btn btn-sm btn-error min-w-[80px]">
                                绑定
                            </button>
                            {% endif %}
                        </td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
        {% endif %}
    </div>
</div>

<script>
// 页面加载时恢复保存的数据
document.addEventListener('DOMContentLoaded', function() {
    const savedRequestBy = localStorage.getItem('savedRequestBy');
    const savedRequestSnc = localStorage.getItem('savedRequestSnc');
    
    if (savedRequestBy) {
        document.getElementById('request_by').value = savedRequestBy;
    }
    if (savedRequestSnc) {
        document.getElementById('request_snc').value = savedRequestSnc;
    }
});

// 保存表单数据到localStorage
function saveFormData() {
    const requestBy = document.getElementById('request_by').value;
    const requestSnc = document.getElementById('request_snc').value;
    
    localStorage.setItem('savedRequestBy', requestBy);
    localStorage.setItem('savedRequestSnc', requestSnc);
}

// 修改原有的showTab函数
function showTab(tab) {
    const insertFields = document.getElementById('insertFields');
    const submitBtn = document.getElementById('submitBtn');
    const mainForm = document.getElementById('mainForm');
    
    if (tab === 'query') {
        insertFields.style.display = 'none';
        submitBtn.textContent = '查询';
        mainForm.method = 'get';
    } else {
        insertFields.style.display = 'block';
        submitBtn.textContent = '添加';
        mainForm.method = 'post';
        
        // 切换到新增标签时，恢复保存的数据
        const savedRequestBy = localStorage.getItem('savedRequestBy');
        const savedRequestSnc = localStorage.getItem('savedRequestSnc');
        
        if (savedRequestBy) {
            document.getElementById('request_by').value = savedRequestBy;
        }
        if (savedRequestSnc) {
            document.getElementById('request_snc').value = savedRequestSnc;
        }
    }
    
    // 更新按钮样式
    document.querySelectorAll('.btn-sm').forEach(btn => {
        btn.classList.remove('btn-active');
    });
    event.target.classList.add('btn-active');
}

// 在表单提交成功后清除保存的数据
function clearSavedData() {
    localStorage.removeItem('savedRequestBy');
    localStorage.removeItem('savedRequestSnc');
}

// 修改validateForm函数
function validateForm() {
    if (document.getElementById('mainForm').method === 'post') {
        const bbpo = document.querySelector('input[name="bbpo"]').value.trim();
        const kitpo = document.querySelector('input[name="kitpo"]').value.trim();
        const subpo = document.querySelector('input[name="subpo"]').value.trim();
        const requestBy = document.querySelector('input[name="request_by"]').value.trim();
        const requestSnc = document.querySelector('input[name="request_snc"]').value.trim();
        
        if (!bbpo) {
            alert('BB PO不能为空！');
            return false;
        }
        
        if (!kitpo && !subpo) {
            alert('Kit PO和Sub PO至少需要填写一个！');
            return false;
        }
        
        if (!requestBy || !requestSnc) {
            alert('Request By和Request SNC都必须填写！');
            return false;
        }
        
        // 如果验证通过，保存当前输入的值
        saveFormData();
    }
    return true;
}

function updateStatus(id, status) {
    // 阻止事件冒泡
    event.preventDefault();
    event.stopPropagation();
    
    if (!confirm('确定要' + (status === 1 ? '释放' : '绑定') + '这条记录吗？')) {
        return;
    }
    
    // 禁用所有按钮，防止重复点击
    const buttons = document.querySelectorAll('button');
    buttons.forEach(button => button.disabled = true);
    
    fetch('/volcano_tools/po-control/update-status/', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-CSRFToken': document.querySelector('[name=csrfmiddlewaretoken]').value
        },
        body: JSON.stringify({
            id: id,
            status: status
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            // 使用window.location.href确保完全刷新页面
            window.location.href = window.location.pathname + window.location.search;
        } else {
            alert('操作失败：' + data.error);
            // 操作失败时重新启用按钮
            buttons.forEach(button => button.disabled = false);
        }
    })
    .catch(error => {
        alert('操作失败：' + error);
        // 发生错误时重新启用按钮
        buttons.forEach(button => button.disabled = false);
    });
    
    return false;
}
</script>
{% endblock %} 
{% extends 'base_mdb.html' %}
{% load static %}

{% block title %}PO 绑定管理{% endblock %}

{% block extra_css %}
<style>
    .card {
        border-radius: 16px;
        box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
        background-color: white;
        padding: 1.5rem;
        margin-bottom: 2rem;
    }
    
    .btn-tab {
        padding: 0.5rem 1.5rem;
        border-radius: 8px;
        font-weight: 600;
        transition: all 0.3s ease;
        margin: 0 0.5rem;
    }
    
    .btn-tab.active {
        background: var(--primary-gradient);
        color: white;
    }
    
    .form-outline {
        margin-bottom: 1.5rem;
    }
    
    .table {
        border-radius: 8px;
        overflow: hidden;
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
    }
    
    .badge {
        padding: 0.5rem 0.75rem;
        border-radius: 6px;
        font-weight: 600;
    }
    
    .badge-success {
        background-color: #10b981;
    }
    
    .badge-error {
        background-color: #ef4444;
    }
    
    .btn-action {
        min-width: 80px;
        border-radius: 8px;
        font-weight: 600;
    }
</style>
{% endblock %}

{% block content %}
<div class="row justify-content-center">
    <div class="col-12">
        <!-- 标题部分 -->
        <div class="text-center mb-4">
            <h2 class="fw-bold">PO 绑定管理 [udtCheckPOControl]</h2>
            <p class="text-muted">提示：Kit PO > DVC PO > DAC PO > Coil PO > SubFrame PO</p>
        </div>
        
        {% if messages %}
        <div class="mb-4">
            {% for message in messages %}
            <div class="alert {% if message.tags == 'error' %}alert-danger{% else %}alert-success{% endif %} shadow-4">
                {{ message }}
            </div>
            {% endfor %}
        </div>
        {% endif %}
        
        <!-- 标签按钮居中 -->
        <div class="text-center mb-4">
            <div class="d-inline-flex">
                <button class="btn btn-tab active" id="queryTab" onclick="showTab('query')">查询</button>
                <button class="btn btn-tab" id="insertTab" onclick="showTab('insert')">新增</button>
            </div>
        </div>

        <!-- 主要内容卡片 -->
        <div class="card">
            <!-- 表单部分 -->
            <form method="get" id="mainForm" onsubmit="return validateForm()" class="mx-auto" style="max-width: 600px;">
                {% csrf_token %}
                
                <div class="form-outline mb-4">
                    <input type="text" id="kitpo" name="kitpo" class="form-control" value="{{ kitpo }}"/>
                    <label class="form-label" for="kitpo">Kit PO</label>
                </div>

                <div class="form-outline mb-4">
                    <input type="text" id="bbpo" name="bbpo" class="form-control" value="{{ bbpo }}"/>
                    <label class="form-label" for="bbpo">BB PO</label>
                </div>
                
                <div class="form-outline mb-4">
                    <input type="text" id="subpo" name="subpo" class="form-control" value="{{ subpo }}"/>
                    <label class="form-label" for="subpo">Sub PO</label>
                </div>
                
                <div id="insertFields" style="display: none;">
                    <div class="form-outline mb-4">
                        <input type="text" id="request_by" name="request_by" class="form-control" onchange="saveFormData()"/>
                        <label class="form-label" for="request_by">Request By</label>
                    </div>
                    
                    <div class="form-outline mb-4">
                        <input type="text" id="request_snc" name="request_snc" class="form-control" onchange="saveFormData()"/>
                        <label class="form-label" for="request_snc">Request SNC</label>
                    </div>
                </div>

                <div class="text-center mt-4">
                    <button type="submit" class="btn btn-primary" id="submitBtn" style="min-width: 120px;">查询</button>
                </div>
            </form>

            <!-- 结果表格 -->
            {% if data %}
            <div class="table-responsive mt-5">
                <table class="table table-hover">
                    <thead class="table-light">
                        <tr>
                            <th class="text-center">ID</th>
                            <th>BB PO</th>
                            <th>Kit PO</th>
                            <th>Sub PO</th>
                            <th class="text-center">Status</th>
                            <th>Request By</th>
                            <th>Request SNC</th>
                            <th>Last Update</th>
                            <th class="text-center">Action</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for row in data %}
                        <tr>
                            <td class="text-center">{{ row.ID }}</td>
                            <td>{{ row.BBPO }}</td>
                            <td>{{ row.KitPO }}</td>
                            <td>{{ row.subPO }}</td>
                            <td class="text-center">
                                <span class="badge {% if row.Status == 0 %}badge-error{% else %}badge-success{% endif %}">
                                    {% if row.Status == 0 %}已绑定{% else %}已释放{% endif %}
                                </span>
                            </td>
                            <td>{{ row.RequestBy }}</td>
                            <td>{{ row.RequestSNC }}</td>
                            <td>{{ row.Lastupdate|date:"Y-m-d H:i" }}</td>
                            <td class="text-center">
                                {% if row.Status == 0 %}
                                <button type="button" 
                                        onclick="updateStatus('{{ row.ID }}', 1)" 
                                        class="btn btn-success btn-sm btn-action">
                                    释放
                                </button>
                                {% else %}
                                <button type="button" 
                                        onclick="updateStatus('{{ row.ID }}', 0)" 
                                        class="btn btn-danger btn-sm btn-action">
                                    绑定
                                </button>
                                {% endif %}
                            </td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
            {% endif %}
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
// 页面加载时初始化 MDB 表单组件
document.addEventListener('DOMContentLoaded', function() {
    document.querySelectorAll('.form-outline').forEach((formOutline) => {
        new mdb.Input(formOutline).init();
    });
    
    const savedRequestBy = localStorage.getItem('savedRequestBy');
    const savedRequestSnc = localStorage.getItem('savedRequestSnc');
    
    if (savedRequestBy) {
        document.getElementById('request_by').value = savedRequestBy;
        new mdb.Input(document.getElementById('request_by').closest('.form-outline')).update();
    }
    if (savedRequestSnc) {
        document.getElementById('request_snc').value = savedRequestSnc;
        new mdb.Input(document.getElementById('request_snc').closest('.form-outline')).update();
    }
});

// 保存表单数据到 localStorage
function saveFormData() {
    const requestBy = document.getElementById('request_by').value;
    const requestSnc = document.getElementById('request_snc').value;
    
    localStorage.setItem('savedRequestBy', requestBy);
    localStorage.setItem('savedRequestSnc', requestSnc);
}

// 修改原有的 showTab 函数
function showTab(tab) {
    const insertFields = document.getElementById('insertFields');
    const submitBtn = document.getElementById('submitBtn');
    const mainForm = document.getElementById('mainForm');
    const queryTab = document.getElementById('queryTab');
    const insertTab = document.getElementById('insertTab');
    
    if (tab === 'query') {
        insertFields.style.display = 'none';
        submitBtn.textContent = '查询';
        mainForm.method = 'get';
        queryTab.classList.add('active');
        insertTab.classList.remove('active');
    } else {
        insertFields.style.display = 'block';
        submitBtn.textContent = '添加';
        mainForm.method = 'post';
        queryTab.classList.remove('active');
        insertTab.classList.add('active');
        
        // 切换到新增标签时，恢复保存的数据
        const savedRequestBy = localStorage.getItem('savedRequestBy');
        const savedRequestSnc = localStorage.getItem('savedRequestSnc');
        
        if (savedRequestBy) {
            document.getElementById('request_by').value = savedRequestBy;
            new mdb.Input(document.getElementById('request_by').closest('.form-outline')).update();
        }
        if (savedRequestSnc) {
            document.getElementById('request_snc').value = savedRequestSnc;
            new mdb.Input(document.getElementById('request_snc').closest('.form-outline')).update();
        }
    }
}

// 在表单提交成功后清除保存的数据
function clearSavedData() {
    localStorage.removeItem('savedRequestBy');
    localStorage.removeItem('savedRequestSnc');
}

// 修改 validateForm 函数
function validateForm() {
    if (document.getElementById('mainForm').method === 'post') {
        const bbpo = document.querySelector('input[name="bbpo"]').value.trim();
        const kitpo = document.querySelector('input[name="kitpo"]').value.trim();
        const subpo = document.querySelector('input[name="subpo"]').value.trim();
        const requestBy = document.querySelector('input[name="request_by"]').value.trim();
        const requestSnc = document.querySelector('input[name="request_snc"]').value.trim();
        
        if (!bbpo) {
            alert('BB PO 不能为空！');
            return false;
        }
        
        if (!kitpo && !subpo) {
            alert('Kit PO 和 Sub PO 至少需要填写一个！');
            return false;
        }
        
        if (!requestBy || !requestSnc) {
            alert('Request By 和 Request SNC 都必须填写！');
            return false;
        }
        
        // 如果验证通过，保存当前输入的值
        saveFormData();
    }
    return true;
}

function updateStatus(id, status) {
    // 阻止事件冒泡
    event.preventDefault();
    event.stopPropagation();
    
    if (!confirm('确定要' + (status === 1 ? '释放' : '绑定') + '这条记录吗？')) {
        return;
    }
    
    // 禁用所有按钮，防止重复点击
    const buttons = document.querySelectorAll('button');
    buttons.forEach(button => button.disabled = true);
    
    fetch('{% url "volcano_tools:update_status" %}', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-CSRFToken': document.querySelector('[name=csrfmiddlewaretoken]').value
        },
        body: JSON.stringify({
            id: id,
            status: status
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            // 使用 window.location.href 确保完全刷新页面
            window.location.href = window.location.pathname + window.location.search;
        } else {
            alert('操作失败：' + data.error);
            // 操作失败时重新启用按钮
            buttons.forEach(button => button.disabled = false);
        }
    })
    .catch(error => {
        alert('操作失败：' + error);
        // 发生错误时重新启用按钮
        buttons.forEach(button => button.disabled = false);
    });
    
    return false;
}
</script>
{% endblock %} 

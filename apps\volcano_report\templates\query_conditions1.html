{% extends 'base1.html' %}
{% load static %}

{% block content %}
<div class="bg-light text-dark py-3 mb-4 shadow-sm">
    <div class="container">
        <h3 class="display-6">Query Conditions</h3>
        <p class="lead">Report</p>
    </div>
</div>

<div class="container">
    {% if conditions %}
    <div class="row row-cols-1 row-cols-md-2 row-cols-lg-3">
        {% for condition in conditions %}
        <div class="col mb-4">
            <div class="card h-100 shadow-sm transition card-image">
                <div class="card-body card-overlay d-flex flex-column justify-content-between">
                    <h5 class="card-title mb-3">
                        <span class="badge badge-dark mr-2">{{ condition.ID }}</span>
                        {{ condition.Reason }}
                    </h5>
                    <p class="card-text">{{ condition.UDP }}</p>
                    <a href="{% url 'volcanoreport:query_page' condition.ID %}" class="btn btn-dark btn-block mt-auto">Open Query</a>
                </div>
            </div>
        </div>
        {% endfor %}
    </div>
    {% else %}
    <div class="text-center">
        <div class="spinner-border text-primary" role="status">
            <span class="sr-only">Loading...</span>
        </div>
    </div>
    {% endif %}
</div>

<!-- 隐藏的canvas元素，用于生成背景图像 -->
<canvas id="card-bg-canvas" style="display: none;"></canvas>
{% endblock %}

{% block extra_js %}
<script>
    $(document).ready(function() {
        // 生成卡片背景图像
        var canvas = document.getElementById('card-bg-canvas');
        var ctx = canvas.getContext('2d');

        // 设置画布大小
        canvas.width = 800;
        canvas.height = 500;

        // 创建渐变
        var gradient = ctx.createLinearGradient(0, 0, canvas.width, 0);
        gradient.addColorStop(0, '#ccd1d9');  // 灰色
        gradient.addColorStop(1, '#596275');  // 蓝灰色

        // 填充渐变
        ctx.fillStyle = gradient;
        ctx.fillRect(0, 0, canvas.width, canvas.height);

        // 获取图像数据URL
        var cardBgImageUrl = canvas.toDataURL('image/jpeg');

        // 将图像数据URL设置为背景图像
        $('.card-image').css('background-image', 'url(' + cardBgImageUrl + ')');
    });
</script>
{% endblock %}

{% block extra_css %}
<style>
    .transition {
        transition: all 0.2s ease-in-out;
    }

    .card-overlay {
        background-color: rgba(0, 0, 0, 0.6);
        color: white;
        padding: 1.25rem;
        display: flex;
        flex-direction: column;
        justify-content: space-between;
    }

    .btn-dark {
        transition: all 0.2s ease-in-out;
        background-color: rgba(0, 0, 0, 0.85);
        color: white;
        border: none;
    }

    .btn-dark:hover {
        background-color: rgba(0, 0, 0, 1);
        color: #ffffff;
    }

    .card:hover {
        transform: translateY(-5px);
        box-shadow: 0 4px 20px rgba(0, 0, 0, 0.25);
    }

    .card-title {
        font-weight: bold;
        color: #e9ecef;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
    }

    .card-text {
        font-weight: 300;
        opacity: 0.85;
    }

    .spinner-border.text-primary {
        color: #007bff;
    }

    .bg-light {
        background-color: #f8f9fa !important;
        color: #343a40;
    }

    .display-6 {
        font-size: 1.5rem;
    }

    .lead {
        font-size: 1rem;
        font-weight: 300;
    }

    .container {
        background-color: #ffffff;
        padding: 20px;
        border-radius: 0.375rem;
        box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
    }

    .table-responsive {
        overflow-x: auto;
    }

    table {
        table-layout: auto;
        white-space: nowrap;
    }

    th, td {
        padding: 10px;
        text-align: left;
        vertical-align: top;
    }
</style>
{% endblock %}

.dw-trans .dw-persp {
    overflow: hidden;
    -webkit-perspective: 1000px;
    -moz-perspective: 1000px;
    perspective: 1000px;
}
.dw-trans .dw,
.dw-trans .dwo {
    -webkit-animation-fill-mode: forwards;
    -webkit-animation-duration: 200ms;
    -moz-animation-fill-mode: forwards;
    -moz-animation-duration: 200ms;
    animation-fill-mode: forwards;
    animation-duration: 200ms;
}
.dw-trans .dwo { 
    -webkit-backface-visibility: hidden; 
}
.dw-in .dw {
    -webkit-animation-timing-function: ease-out;
    -moz-animation-timing-function: ease-out;
    animation-timing-function: ease-out;
}
.dw-out .dw {
    -webkit-animation-timing-function: ease-in;
    -moz-animation-timing-function: ease-in;
    animation-timing-function: ease-in;
}
.dw-in .dwo {
    -webkit-animation-name: dw-f-in;
    -moz-animation-name: dw-f-in;
    animation-name: dw-f-in;
}
.dw-out .dwo {
    -webkit-animation-name: dw-f-out;
    -moz-animation-name: dw-f-out;
    animation-name: dw-f-out;
}
.dw-flip,
.dw-swing,
.dw-slidehorizontal,
.dw-slidevertical,
.dw-slidedown,
.dw-slideup,
.dw-fade {
    -webkit-backface-visibility: hidden;
    -webkit-transform: translateX(0);
    -moz-backface-visibility: hidden;
    -moz-transform: translateX(0);
    backface-visibility: hidden;
    transform: translateX(0);
}
.dw-swing,
.dw-slidehorizontal,
.dw-slidevertical,
.dw-slidedown,
.dw-slideup,
.dw-fade {
    -webkit-transform-origin: 0 0;
    -moz-transform-origin: 0 0;
    transform-origin: 0 0;
}
.dw-flip,
.dw-pop {
    -webkit-transform-origin: 50% 50%;
    -moz-transform-origin: 50% 50%;
    transform-origin: 50% 50%;
}
.dw-in .dw-pop {
    opacity: 1;
    -webkit-animation-name: dw-p-in;
    -webkit-transform: scale(1);
    -moz-animation-name: dw-p-in;
    -moz-transform: scale(1);
    transform: scale(1);
    animation-name: dw-p-in;
}
.dw-out .dw-pop {
    opacity: 0;
    -webkit-animation-name: dw-p-out;
    -moz-animation-name: dw-p-out;
    animation-name: dw-p-out;
}
.dw-in .dw-flip {
    opacity: 1;
    -webkit-animation-name: dw-fl-in;
    -webkit-transform: scale(1);
    -moz-animation-name: dw-fl-in;
    -moz-transform: scale(1);
    animation-name: dw-fl-in;
    transform: scale(1);
}
.dw-out .dw-flip {
    opacity: 0;
    animation-name: dw-fl-out;
    -webkit-animation-name: dw-fl-out;
    -moz-animation-name: dw-fl-out;
}
.dw-in .dw-swing {
    opacity: 1;
    -webkit-animation-name: dw-sw-in;
    -webkit-transform: scale(1);
    -moz-animation-name: dw-sw-in;
    -moz-transform: scale(1);
    transform: scale(1);
    animation-name: dw-sw-in;
}
.dw-out .dw-swing {
    opacity: 0;
    -webkit-animation-name: dw-sw-out;
    -moz-animation-name: dw-sw-out;
    animation-name: dw-sw-out;
}
.dw-in .dw-slidehorizontal {
    opacity: 1;
    -webkit-animation-name: dw-sh-in;
    -webkit-transform: scale(1);
    -moz-animation-name: dw-sh-in;
    -moz-transform: scale(1);
    transform: scale(1);
    animation-name: dw-sh-in;
}
.dw-out .dw-slidehorizontal {
    opacity: 0;
    -webkit-animation-name: dw-sh-out;
    -moz-animation-name: dw-sh-out;
    animation-name: dw-sh-out;
}
.dw-in .dw-slidevertical {
    opacity: 1;
    -webkit-animation-name: dw-sv-in;
    -webkit-transform: scale(1);
    -moz-animation-name: dw-sv-in;
    -moz-transform: scale(1);
    animation-name: dw-sv-in;
    transform: scale(1);
}
.dw-out .dw-slidevertical {
    opacity: 0;
    -webkit-animation-name: dw-sv-out;
    -moz-animation-name: dw-sv-out;
    animation-name: dw-sv-out;
}
.dw-in .dw-slidedown {
    -webkit-animation-name: dw-sd-in;
    -webkit-transform: scale(1);
    -moz-animation-name: dw-sd-in;
    -moz-transform: scale(1);
    animation-name: dw-sd-in;
    transform: scale(1);
}
.dw-out .dw-slidedown {
    animation-name: dw-sd-out;
    -webkit-animation-name: dw-sd-out;
    -webkit-transform: translateY(-100%);
    -moz-animation-name: dw-sd-out;
    -moz-transform: translateY(-100%);
}
.dw-in .dw-slideup {
    -webkit-animation-name: dw-su-in;
    -webkit-transform: scale(1);
    -moz-animation-name: dw-su-in;
    -moz-transform: scale(1);
    transform: scale(1);
    animation-name: dw-su-in;
}
.dw-out .dw-slideup {
    animation-name: dw-su-out;
    -webkit-animation-name: dw-su-out;
    -webkit-transform: translateY(100%);
    -moz-animation-name: dw-su-out;
    -moz-transform: translateY(100%);
}
.dw-in .dw-fade {
    opacity: 1;
    -webkit-animation-name: dw-f-in;
    -moz-animation-name: dw-f-in;
    animation-name: dw-f-in;
}
.dw-out .dw-fade {
    opacity: 0;
    -webkit-animation-name: dw-f-out;
    -moz-animation-name: dw-f-out;
    animation-name: dw-f-out;
}
/* Fade in */
@keyframes dw-f-in {
    from {
        opacity: 0;
    }
    to {
        opacity: 1;
    }
}
@-webkit-keyframes dw-f-in {
    from {
        opacity: 0;
    }
    to {
        opacity: 1;
    }
}
@-moz-keyframes dw-f-in {
    from {
        opacity: 0;
    }
    to {
        opacity: 1;
    }
}
/* Fade out */
@keyframes dw-f-out {
    from {
        visibility: visible;
        opacity: 1;
    }
    to {
        opacity: 0;
    }
}
@-webkit-keyframes dw-f-out {
    from {
        visibility: visible;
        opacity: 1;
    }
    to {
        opacity: 0;
    }
}
@-moz-keyframes dw-f-out {
    from {
        visibility: visible;
        opacity: 1;
    }
    to {
        opacity: 0;
    }
}
/* Pop in */
@keyframes dw-p-in {
    from {
        opacity: 0;
        transform: scale(0.8);
    }
    to {
        opacity: 1;
        transform: scale(1);
    }
}
@-webkit-keyframes dw-p-in {
    from {
        opacity: 0;
        -webkit-transform: scale(0.8);
    }
    to {
        opacity: 1;
        -webkit-transform: scale(1);
    }
}
@-moz-keyframes dw-p-in {
    from {
        opacity: 0;
        -moz-transform: scale(0.8);
    }
    to {
        opacity: 1;
        -moz-transform: scale(1);
    }
}
/* Pop out */
@keyframes dw-p-out {
    from {
        opacity: 1;
        transform: scale(1);
    }
    to {
        opacity: 0;
        transform: scale(0.8);
    }
}
@-webkit-keyframes dw-p-out {
    from {
        opacity: 1;
        -webkit-transform: scale(1);
    }
    to {
        opacity: 0;
        -webkit-transform: scale(0.8);
    }
}
@-moz-keyframes dw-p-out {
    from {
        opacity: 1;
        -moz-transform: scale(1);
    }
    to {
        opacity: 0;
        -moz-transform: scale(0.8);
    }
}
/* Flip in */
@keyframes dw-fl-in {
    from {
        opacity: 0;
        transform: rotateY(90deg);
    }
    to {
        opacity: 1;
        transform: rotateY(0);
    }
}
@-webkit-keyframes dw-fl-in {
    from {
        opacity: 0;
        -webkit-transform: rotateY(90deg);
    }
    to {
        opacity: 1;
        -webkit-transform: rotateY(0);
    }
}
@-moz-keyframes dw-fl-in {
    from {
        opacity: 0;
        -moz-transform: rotateY(90deg);
    }
    to {
        opacity: 1;
        -moz-transform: rotateY(0);
    }
}
/* Flip out */
@keyframes dw-fl-out {
    from {
        opacity: 1;
        transform: rotateY(0deg);
    }
    to {
        opacity: 0;
        transform: rotateY(-90deg);
    }
}
@-webkit-keyframes dw-fl-out {
    from {
        opacity: 1;
        -webkit-transform: rotateY(0deg);
    }
    to {
        opacity: 0;
        -webkit-transform: rotateY(-90deg);
    }
}
@-moz-keyframes dw-fl-out {
    from {
        opacity: 1;
        -moz-transform: rotateY(0deg);
    }
    to {
        opacity: 0;
        -moz-transform: rotateY(-90deg);
    }
}
/* Swing in */
@keyframes dw-sw-in {
    from {
        opacity: 0;
        transform: rotateY(-90deg);
    }
    to {
        opacity: 1;
        transform: rotateY(0deg);
    }
}
@-webkit-keyframes dw-sw-in {
    from {
        opacity: 0;
        -webkit-transform: rotateY(-90deg);
    }
    to {
        opacity: 1;
        -webkit-transform: rotateY(0deg);
    }
}
@-moz-keyframes dw-sw-in {
    from {
        opacity: 0;
        -moz-transform: rotateY(-90deg);
    }
    to {
        opacity: 1;
        -moz-transform: rotateY(0deg);
    }
}
/* Swing out */
@keyframes dw-sw-out {
    from {
        opacity: 1;
        transform: rotateY(0deg);
    }
    to {
        opacity: 0;
        transform: rotateY(-90deg);
    }
}
@-webkit-keyframes dw-sw-out {
    from {
        opacity: 1;
        -webkit-transform: rotateY(0deg);
    }
    to {
        opacity: 0;
        -webkit-transform: rotateY(-90deg);
    }
}
@-moz-keyframes dw-sw-out {
    from {
        opacity: 1;
        -moz-transform: rotateY(0deg);
    }
    to {
        opacity: 0;
        -moz-transform: rotateY(-90deg);
    }
}
/* Slide horizontal in */
@keyframes dw-sh-in {
    from {
        opacity: 0;
        transform: translateX(-100%);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}
@-webkit-keyframes dw-sh-in {
    from {
        opacity: 0;
        -webkit-transform: translateX(-100%);
    }
    to {
        opacity: 1;
        -webkit-transform: translateX(0);
    }
}
@-moz-keyframes dw-sh-in {
    from {
        opacity: 0;
        -moz-transform: translateX(-100%);
    }
    to {
        opacity: 1;
        -moz-transform: translateX(0);
    }
}
/* Slide horizontal out */
@keyframes dw-sh-out {
    from {
        opacity: 1;
        transform: translateX(0);
    }
    to {
        opacity: 0;
        transform: translateX(100%);
    }
}
@-webkit-keyframes dw-sh-out {
    from {
        opacity: 1;
        -webkit-transform: translateX(0);
    }
    to {
        opacity: 0;
        -webkit-transform: translateX(100%);
    }
}
@-moz-keyframes dw-sh-out {
    from {
        opacity: 1;
        -moz-transform: translateX(0);
    }
    to {
        opacity: 0;
        -moz-transform: translateX(100%);
    }
}
/* Slide vertical in */
@keyframes dw-sv-in {
    from {
        opacity: 0;
        transform: translateY(-100%);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}
@-webkit-keyframes dw-sv-in {
    from {
        opacity: 0;
        -webkit-transform: translateY(-100%);
    }
    to {
        opacity: 1;
        -webkit-transform: translateY(0);
    }
}
@-moz-keyframes dw-sv-in {
    from {
        opacity: 0;
        -moz-transform: translateY(-100%);
    }
    to {
        opacity: 1;
        -moz-transform: translateY(0);
    }
}
/* Slide vertical out */
@keyframes dw-sv-out {
    from {
        opacity: 1;
        transform: translateY(0);
    }
    to {
        opacity: 0;
        transform: translateY(100%);
    }
}
@-webkit-keyframes dw-sv-out {
    from {
        opacity: 1;
        -webkit-transform: translateY(0);
    }
    to {
        opacity: 0;
        -webkit-transform: translateY(100%);
    }
}
@-moz-keyframes dw-sv-out {
    from {
        opacity: 1;
        -moz-transform: translateY(0);
    }
    to {
        opacity: 0;
        -moz-transform: translateY(100%);
    }
}
/* Slide Down In */
@keyframes dw-sd-in {
    from {
        transform: translateY(-100%);
    }
    to {
        transform: translateY(0);
    }
}
@-webkit-keyframes dw-sd-in {
    from {
        opacity: 1;
        -webkit-transform: translateY(-100%);
    }
    to {
        opacity: 1;
        -webkit-transform: translateY(0);
    }
}
@-moz-keyframes dw-sd-in {
    from {
        -moz-transform: translateY(-100%);
    }
    to {
        -moz-transform: translateY(0);
    }
}
/* Slide down out */
@keyframes dw-sd-out {
    from {
        transform: translateY(0);
    }
    to {
        transform: translateY(-100%);
    }
}
@-webkit-keyframes dw-sd-out {
    from {
        opacity: 1;
        -webkit-transform: translateY(0);
    }
    to {
        opacity: 1;
        -webkit-transform: translateY(-100%);
    }
}
@-moz-keyframes dw-sd-out {
    from {
        -moz-transform: translateY(0);
    }
    to {
        -moz-transform: translateY(-100%);
    }
}
/* Slide Up In */
@keyframes dw-su-in {
    from {
        transform: translateY(100%);
    }
    to {
        transform: translateY(0);
    }
}
@-webkit-keyframes dw-su-in {
    from {
        opacity: 1;
        -webkit-transform: translateY(100%);
    }
    to {
        opacity: 1;
        -webkit-transform: translateY(0);
    }
}
@-moz-keyframes dw-su-in {
    from {
        -moz-transform: translateY(100%);
    }
    to {
        -moz-transform: translateY(0);
    }
}
/* Slide up out */
@keyframes dw-su-out {
    from {
        transform: translateY(0);
    }
    to {
        transform: translateY(100%);
    }
}
@-webkit-keyframes dw-su-out {
    from {
        opacity: 1;
        -webkit-transform: translateY(0);
    }
    to {
        opacity: 1;
        -webkit-transform: translateY(100%);
    }
}
@-moz-keyframes dw-su-out {
    from {
        -moz-transform: translateY(0);
    }
    to {
        -moz-transform: translateY(100%);
    }
}

.dw,
.dwo {
    -webkit-font-smoothing: antialiased;
    -webkit-tap-highlight-color: rgba(0, 0, 0, 0);
}

.mbsc-fr-lock {
    -ms-touch-action: none;
    touch-action: none;
}

.dw {
    max-width: 98%;
    position: absolute;
    top: 0;
    left: 0;
    z-index: 2;
    font-size: 12px;
    text-shadow: none;
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none;
    -ms-touch-action: pan-y;
    touch-action: pan-y;
}

.dw:focus {
    outline: none;
}

.dw:focus {
    outline-offset: -2px;
}

.dw-rtl {
    direction: rtl;
}
/* Box sizing */

.dw,
.dwbc {
    -webkit-box-sizing: border-box;
    -moz-box-sizing: border-box;
    box-sizing: border-box;
}

.dwwr {
    min-width: 200px;
    zoom: 1;
    overflow: hidden;
    text-align: center;
    font-family: arial, verdana, sans-serif;
}
/* Modal overlay */

.dw-persp,
.dwo {
    width: 100%;
    height: 100%;
    position: absolute;
    top: 0;
    left: 0;
}

.dw-persp {
    z-index: 99998;
    pointer-events: auto;
}

.dwo {
    z-index: 1;
    background: #000;
    background: rgba(0, 0, 0, .7);
    filter: Alpha(Opacity=70);
}
/* Liquid mode */

.dw-liq .dw {
    max-width: 100%;
}
/* Top/Bottom mode */

.dw-top .dw,
.dw-bottom .dw {
    width: 100%;
    max-width: 100%;
}
/* Inline mode */

.dw-inline .dw {
    position: static;
    display: inline-block;
    max-width: 100%;
}

.dw-inline.dw-liq .dw-persp .dw {
    display: block;
}

.dw-inline .dw-persp {
    position: static;
}
/* Bubble mode */

.dw-bubble .dw {
    margin: 20px 0;
}

.dw-bubble .dw-arrw {
    position: absolute;
    z-index: 1;
    left: 0;
    width: 100%;
}

.dw-bubble-top .dw-arrw {
    bottom: -36px;
}

.dw-bubble-bottom .dw-arrw {
    top: -36px;
}

.dw-bubble .dw-arrw-i {
    margin: 0 30px;
    position: relative;
    height: 36px;
}

.dw-bubble .dw-arr {
    display: block;
}

.dw-arr {
    display: none;
    position: absolute;
    left: 0;
    width: 0;
    height: 0;
    border-width: 18px 18px;
    border-style: solid;
    margin-left: -18px;
}

.dw-bubble-bottom .dw-arr {
    top: 0;
}

.dw-bubble-top .dw-arr {
    bottom: 0;
}

.dw-hidden {
    width: 0;
    height: 0;
    margin: 0;
    padding: 0;
    border: 0;
    overflow: hidden;
}
/* Header */

.dwv {
    overflow: hidden;
    text-overflow: ellipsis;
}
/* Buttons */

.dwb {
    overflow: hidden;
    display: block;
    text-decoration: none;
    white-space: nowrap;
    text-overflow: ellipsis;
    vertical-align: top;
}

.dwb-e {
    cursor: pointer;
}

.dwb-d {
    cursor: default;
}
/* Button container */

.dwbc {
    display: table;
    width: 100%;
    text-align: center;
}

.dwbc .dwb-d {
    opacity: .3;
}
/* Button wrapper */

.dwbw {
    vertical-align: top;
    display: table-cell;
    position: relative;
    z-index: 5;
}

.dwbw .dwb:before {
    padding: .375em;
}
/* Widget content styling */

.mbsc-wdg .dwcc {
    position: relative;
    z-index: 0;
    padding: 1em;
    font-size: 14px;
    text-align: left;
    white-space: normal;
}
/* Default theme */

.mbsc-mobiscroll .dwwr {
    min-width: 16em;
    background: #f7f7f7;
    color: #454545;
    font-size: 16px;
}

.mbsc-mobiscroll .dwv {
    padding: 0 .6666em;
    padding-top: .6666em;
    color: #4eccc4;
    font-size: .75em;
    text-transform: uppercase;
    min-height: 2em;
    line-height: 2em;
}

.mbsc-mobiscroll .dwbc {
    display: block;
    overflow: hidden;
    text-align: right;
    padding: 0 .5em .5em .5em;
}

.mbsc-mobiscroll .dwbw {
    display: block;
    float: right;
}

.mbsc-mobiscroll .dw-rtl .dwbw {
    float: left;
}

.mbsc-mobiscroll .dwb {
    height: 2.5em;
    line-height: 2.5em;
    padding: 0 1em;
    color: #4eccc4;
    text-transform: uppercase;
}

.mbsc-mobiscroll .dwb-a {
    background: rgba(78, 204, 196, .3);
}

.mbsc-mobiscroll .dw-bubble-bottom .dw-arr {
    border-color: transparent transparent #f7f7f7 transparent;
}

.mbsc-mobiscroll .dw-bubble-top .dw-arr {
    border-color: #f7f7f7 transparent transparent transparent;
}

.mbsc-ios .dwo {
    background: rgba(0, 0, 0, .2);
    filter: Alpha(Opacity=20);
}

.mbsc-ios .dwwr {
    position: relative;
    background: #f7f7f7;
    color: #000;
    padding-top: 3.333334em;
    font-size: 12px;
}

.mbsc-ios .dwv {
    padding: 0 .416667em;
    color: #9d9d9d;
    line-height: 2.5em;
    min-height: 2.5em;
    border-bottom: 1px solid #acacac;
}

.mbsc-ios .dwbc {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    border-bottom: 1px solid #acacac;
}

.mbsc-ios .dwb {
    height: 2.352941em;
    line-height: 2.352941em;
    padding: 0 .588235em;
    display: inline-block;
    color: #007aff;
    font-size: 1.416667em;
}

.mbsc-ios .dwb-a {
    opacity: .5;
}

.mbsc-ios .dwbw {
    display: block;
    float: right;
}

.mbsc-ios .dwb-c {
    float: left;
}

/* Bubble arrow */

.mbsc-ios .dw-bubble-bottom .dw-arr {
    border-color: transparent transparent #f7f7f7 transparent;
}

.mbsc-ios .dw-bubble-top .dw-arr {
    border-color: #f7f7f7 transparent transparent transparent;
}

.mbsc-ios.dw-bubble .dwwr {
    border-radius: 8px;
}
/* Inline display */

.mbsc-ios.dw-nobtn .dwwr {
    padding-top: 0;
}

.dwwb,
.dwwo,
.dwwol {
    -webkit-transform: translateZ(0);
}
/* Wheel container wrapper */

.dwc {
    max-width: 100%;
    vertical-align: middle;
    display: inline-block;
    overflow: hidden;
    -webkit-box-sizing: border-box;
    -moz-box-sizing: border-box;
    box-sizing: border-box;
}
/* Wheel label */

.dwl {
    line-height: 30px;
    height: 30px;
    top: -30px;
    left: 0;
    text-align: center;
    white-space: nowrap;
    position: absolute;
    width: 100%;
}
/* Wheel container */

.dwwc {
    margin: 0 auto;
    position: relative;
    zoom: 1;
    display: -webkit-box;
    display: -webkit-flex;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-pack: center;
    -webkit-justify-content: center;
    justify-content: center;
}

.dwfl {
    max-width: 100%;
    -webkit-box-flex: 1;
    -webkit-flex: 1 auto;
    -ms-flex: 1 auto;
    flex: 1 auto;
    -ms-touch-action: none;
    touch-action: none;
}
/* Wheels */

.dwwl {
    position: relative;
    z-index: 5;
}

.dwww {
    position: relative;
    padding: 1px;
}

.dww {
    overflow: hidden;
    position: relative;
    /* Forces IE to respect overflow hidden while animating */

    border-radius: 1px;
    /* Fixes Firefox rendering issues */

    border-top: 1px solid transparent;
    border-bottom: 1px solid transparent;
}

.dw-bf {
    -webkit-backface-visibility: hidden;
    -webkit-perspective: 1000px;
    backface-visibility: hidden;
    perspective: 1000px;
}

.dw-ul {
    position: relative;
    z-index: 3;
}

.dw-li {
    padding: 0 5px;
    position: relative;
    text-align: center;
    white-space: nowrap;
    vertical-align: bottom;
    opacity: .3;
    filter: Alpha(Opacity=30);
    cursor: pointer;
    -webkit-transition: opacity .2s ease-out;
    -moz-transition: opacity .2s ease-out;
    transition: opacity .2s ease-out;
}
/* Valid entry */

.dw-li.dw-v,
.dw-li.dw-fv {
    opacity: 1;
    filter: Alpha(Opacity=100);
}
/* Hidden entry */

.dw-li.dw-h {
    visibility: hidden;
}

.dw-i {
    position: relative;
    height: 100%;
    overflow: hidden;
    text-overflow: ellipsis;
}
/* Clickpick mode */

.dwwb {
    position: absolute;
    z-index: 4;
    left: 0;
    cursor: pointer;
    width: 100%;
    text-decoration: none;
    text-align: center;
    opacity: 1;
    -webkit-transition: opacity .2s linear;
    transition: opacity .2s linear;
}

.dwa .dwwb {
    opacity: 0;
}

.dwpm .dwwbp {
    top: 0;
}

.dwpm .dwwbm {
    bottom: 0;
}

.dwpm .dwwol {
    display: none;
}
/* Wheel overlay */

.dwwo {
    position: absolute;
    z-index: 3;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    pointer-events: none;
}
/* Background line */

.dwwol {
    position: absolute;
    z-index: 1;
    top: 50%;
    left: 0;
    width: 100%;
    pointer-events: none;
}
/* Liquid mode */

.dw-liq .dwc {
    display: block;
}

.dw-liq .dw-tbl {
    width: 100%;
    table-layout: fixed;
}
/* Hidden label */

.dwhl .dwl {
    display: none;
}
/* Hidden select element */

.dw-hsel {
    position: absolute;
    height: 1px !important;
    width: 1px !important;
    left: 0;
    overflow: hidden;
    clip: rect(1px, 1px, 1px, 1px);
}
/* Multiple lines */

.dw-ml .dw-li {
    overflow: hidden;
}

.dw-ml .dw-li .dw-i {
    width: 100%;
    height: auto;
    display: inline-block;
    vertical-align: middle;
    white-space: normal;
}
/* Multiple selection */

.dwwms .dw-li {
    padding: 0 40px;
}

.dwwms .dwwol {
    display: none;
}

.dw-msel:before {
    width: 40px;
    text-align: center;
    position: absolute;
    top: 0;
    left: 0;
}
/* Select groups */

.dww .dw-w-gr {
    padding: 0 5px;
    opacity: 1;
    font-weight: bold;
    text-align: left;
}
/* Default theme */

.mbsc-mobiscroll .dwc {
    padding: 2em .25em 0 .25em;
}

.mbsc-mobiscroll .dwl {
    color: #4eccc4;
    font-size: .75em;
    text-transform: uppercase;
}

.mbsc-mobiscroll .dwhl {
    padding-top: 0;
}

.mbsc-mobiscroll .dwfl {
    padding: .5em .25em;
}

.mbsc-mobiscroll .dw-li {
    font-size: 1.375em;
}

.mbsc-mobiscroll .dw-hl {
    background: rgba(78, 204, 196, .3);
}

.mbsc-mobiscroll .dwwol {
    border-top: 1px solid #4eccc4;
    border-bottom: 1px solid #4eccc4;
}
/* Clickpick mode */

.mbsc-mobiscroll .dwpm .dwwol {
    display: block;
}

.mbsc-mobiscroll .dwwb {
    color: #4eccc4;
    background: #f7f7f7;
}

.mbsc-mobiscroll .dwwbp {
    bottom: 0;
    top: auto;
}

.mbsc-mobiscroll .dwwbm {
    top: 0;
    bottom: auto;
}

.mbsc-mobiscroll .dwwb span {
    display: none;
}

.mbsc-mobiscroll .dwwb:before {
    position: absolute;
    left: 0;
    top: 0;
    width: 100%;
    font-size: 24px;
    text-align: center;
}

.mbsc-mobiscroll .dwwb.dwb-a:before {
    background: rgba(78, 204, 196, .3);
}
/* Group select */

.mbsc-mobiscroll .dw-w-gr {
    font-size: 1.125em;
}
/* Multiple select */

.mbsc-mobiscroll .dw-msel:before {
    font-size: 40px;
    color: #4eccc4;
}

.mbsc-mobiscroll .dwwms .dwwol {
    display: none;
}

.mbsc-ios .dwl {
    text-align: left;
    text-indent: .4166667em;
    color: #ababab;
}

.mbsc-ios .dwwc {
    padding: 2.5em .833333em .833333em .833333em;
}

.mbsc-ios .dwhl .dwwc {
    padding-top: .833333em;
}

.mbsc-ios .dwwo {
    background: -webkit-gradient(linear, left bottom, left top, from(#f7f7f7), color-stop(0.52, rgba(245, 245, 245, 0)), color-stop(0.48, rgba(245, 245, 245, 0)), to(#f7f7f7));
    background: -webkit-linear-gradient(#f7f7f7, rgba(245, 245, 245, 0) 52%, rgba(245, 245, 245, 0) 48%, #f7f7f7);
    background: -moz-linear-gradient(#f7f7f7, rgba(245, 245, 245, 0) 52%, rgba(245, 245, 245, 0) 48%, #f7f7f7);
    background: linear-gradient(#f7f7f7, rgba(245, 245, 245, 0) 52%, rgba(245, 245, 245, 0) 48%, #f7f7f7);
}

.mbsc-ios .dwwol {
    padding: 0 .833333em;
    height: 2.833333em;
    margin: -1.5em 0 0 -.833333em;
    border-top: 1px solid #dbdbdb;
    border-bottom: 1px solid #dbdbdb;
}

.mbsc-ios .dw-li {
    color: #9d9d9d;
    font-size: 1.833333em;
    text-align: left;
}

.mbsc-ios .dw-hl {
    background: rgba(0, 122, 255, .2);
}

.mbsc-ios .dw-sel {
    color: #000;
}
/* Clickpick mode */

.mbsc-ios .dwpm .dw-li {
    text-align: center;
}

.mbsc-ios .dwpm .dwwol {
    display: block;
}

.mbsc-ios .dwwb {
    color: #007aff;
    background: #f7f7f7;
}

.mbsc-ios .dwwbp {
    bottom: 0;
    top: auto;
}

.mbsc-ios .dwwbm {
    top: 0;
    bottom: auto;
}

.mbsc-ios .dwwb span {
    display: none;
}

.mbsc-ios .dwwb:before {
    position: absolute;
    left: 0;
    top: 0;
    width: 100%;
    font-size: 2em;
    text-align: center;
}
/* Select */

.mbsc-ios.dw-select .dwwo {
    display: none;
}
/* Multiple select */

.mbsc-ios .dwwms .dw-li {
    padding: 0 .227272em 0 1.818181em;
    color: #000;
}

.mbsc-ios .dwwms .dw-msel {
    color: #007aff;
}

.mbsc-ios .dw-msel:before {
    font-size: 1.818181em;
}
/* Group select */

.mbsc-ios .dw-select-gr .dw-li {
    padding-left: 1.818181em;
}

.mbsc-ios .dw-select-gr .dw-w-gr {
    padding-left: .277777em;
    font-weight: normal;
    font-size: 1.222222em;
}























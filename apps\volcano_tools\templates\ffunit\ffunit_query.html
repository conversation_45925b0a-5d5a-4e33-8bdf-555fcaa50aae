{% extends "base1.html" %}

{% block extra_css %}
<style>
    .select2-container {
        width: 100% !important;
        z-index: 1000;
    }

    .select2-dropdown {
        border-radius: 12px;
        border: 1px solid #d2d2d7;
        box-shadow: 0 4px 12px rgba(0,0,0,0.1);
    }

    .select2-container--default .select2-selection--single {
        border: none;
        background: transparent;
        height: 38px;
    }

    .select2-container--default .select2-selection--single .select2-selection__rendered {
        line-height: 38px;
        padding-left: 0;
    }

    .select2-container--default .select2-search--dropdown .select2-search__field {
        border-radius: 6px;
        border: 1px solid #d2d2d7;
        padding: 6px 12px;
    }

    .select2-container--default .select2-results__option--highlighted[aria-selected] {
        background-color: #0066cc;
    }

    /* 添加必填字段的视觉提示 */
    .required-field::after {
        content: " *";
        color: red;
    }

    .input-area.error {
        border-color: #dc3545;
        box-shadow: 0 0 0 0.2rem rgba(220, 53, 69, 0.25);
    }
</style>
{% endblock %}

{% block content %}
<div class="app-container">
    <h1 class="page-title">FF Unit State Query & Update</h1>

    <div class="form-section">
        <div class="form-layout">
            <div class="form-column">
                <div class="form-group">
                    <label for="serial_numbers" class="required-field">Serial Numbers</label>
                    <div class="input-area">
                        <textarea id="serial_numbers" name="serial_numbers" 
                                placeholder="Enter serial numbers here..."
                                class="full-height-textarea"></textarea>
                        <small class="form-text">Enter one serial number per line</small>
                    </div>
                </div>
            </div>
            
            <div class="form-column">
                <div class="form-group">
                    <label for="newStateId" class="required-field">New State</label>
                    <div class="input-area">
                        <select id="newStateId" class="form-control select2-state">
                            <option value="">Select a state...</option>
                            {% for state in unit_states %}
                                <option value="{{ state.ID }}">{{ state.ID }} - {{ state.Description }}</option>
                            {% endfor %}
                        </select>
                    </div>
                </div>
                <div class="form-group">
                    <label for="updateRemark" class="required-field">Remark</label>
                    <textarea id="updateRemark" class="form-control" rows="3" 
                            placeholder="Enter remark for the update"></textarea>
                </div>
            </div>
        </div>

        <div class="action-buttons">
            <button type="button" class="btn btn-primary" id="queryBtn">Query</button>
            <button type="button" class="btn btn-success" id="updateBtn">Update States</button>
        </div>
    </div>

    <div id="queryResults" class="results-section"></div>
</div>
{% endblock %}

{% block extra_js %}
<script>
function getCookie(name) {
    let cookieValue = null;
    if (document.cookie && document.cookie !== '') {
        const cookies = document.cookie.split(';');
        for (let i = 0; i < cookies.length; i++) {
            const cookie = cookies[i].trim();
            if (cookie.substring(0, name.length + 1) === (name + '=')) {
                cookieValue = decodeURIComponent(cookie.substring(name.length + 1));
                break;
            }
        }
    }
    return cookieValue;
}

// 自定义消息显示函数
function showMessage(message, type = 'info') {
    const messageDiv = document.createElement('div');
    messageDiv.className = `alert alert-${type} alert-dismissible fade show`;
    messageDiv.style.position = 'fixed';
    messageDiv.style.top = '20px';
    messageDiv.style.left = '50%';
    messageDiv.style.transform = 'translateX(-50%)';
    messageDiv.style.zIndex = '9999';
    messageDiv.style.minWidth = '300px';
    messageDiv.style.textAlign = 'center';
    messageDiv.style.boxShadow = '0 4px 12px rgba(0,0,0,0.15)';
    
    messageDiv.innerHTML = `
        ${message}
        <button type="button" class="close" data-dismiss="alert" aria-label="Close">
            <span aria-hidden="true">&times;</span>
        </button>
    `;
    
    document.body.appendChild(messageDiv);
    
    setTimeout(() => {
        messageDiv.remove();
    }, 3000);
}

async function performQuery() {
    const serialNumbers = document.getElementById('serial_numbers').value;
    const queryResults = document.getElementById('queryResults');

    if (!serialNumbers.trim()) {
        showMessage('Please enter at least one serial number', 'warning');
        return;
    }

    try {
        const response = await fetch('{% url "volcano_tools:query_unit_states" %}', {
            method: 'POST',
            body: JSON.stringify({ serial_numbers: serialNumbers }),
            headers: {
                'Content-Type': 'application/json',
                'X-CSRFToken': getCookie('csrftoken')
            }
        });

        const result = await response.json();

        if (result.error) {
            showMessage(result.error, 'danger');
            return;
        }

        if (!result.data || result.data.length === 0) {
            queryResults.innerHTML = '<div class="alert alert-info">No results found.</div>';
            return;
        }

        // Display results in a table
        let html = '<div class="results-table">';
        html += '<table class="table">';
        html += '<thead>';
        html += '<tr>';
        html += '<th>Serial Number</th>';
        html += '<th>Part Number</th>';
        html += '<th>Unit State ID</th>';
        html += '<th>State Description</th>';
        html += '<th>Last Update</th>';
        html += '</tr>';
        html += '</thead>';
        html += '<tbody>';

        result.data.forEach(item => {
            html += '<tr>';
            html += `<td>${item.serial_number || ''}</td>`;
            html += `<td>${item.part_number || ''}</td>`;
            html += `<td>${item.unit_state_id || ''}</td>`;
            html += `<td>${item.state_description || ''}</td>`;
            html += `<td>${item.last_update || ''}</td>`;
            html += '</tr>';
        });

        html += '</tbody></table></div>';
        queryResults.innerHTML = html;

    } catch (error) {
        console.error('Error:', error);
        showMessage('An error occurred while querying the data', 'danger');
    }
}

async function updateStates() {
    const serialNumbers = document.getElementById('serial_numbers').value;
    const newStateId = document.getElementById('newStateId').value;
    const userRemark = document.getElementById('updateRemark').value;

    if (!serialNumbers.trim()) {
        showMessage('Please enter at least one serial number', 'warning');
        return;
    }

    if (!newStateId) {
        showMessage('Please select a new state', 'warning');
        $('#newStateId').focus();
        return;
    }

    if (!userRemark.trim()) {
        showMessage('Please enter a remark', 'warning');
        $('#updateRemark').focus();
        return;
    }

    try {
        // 获取用户名
        const response = await fetch('{% url "volcano_tools:get_client_info" %}');
        const data = await response.json();
        const username = data.username;
        
        // 组合备注信息：用户输入的备注 + 用户名
        const remark = `${userRemark.trim()} [User: ${username}]`;

        const updateResponse = await fetch('{% url "volcano_tools:update_unit_states" %}', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRFToken': getCookie('csrftoken')
            },
            body: JSON.stringify({
                serial_numbers: serialNumbers,
                new_state_id: newStateId,
                remark: remark
            })
        });

        const result = await updateResponse.json();

        if (updateResponse.ok) {
            showMessage(result.message, 'success');
            if (result.errors && result.errors.length > 0) {
                console.log('Errors:', result.errors);
                showMessage(`Some updates failed. Check console for details.`, 'warning');
            }
            // Refresh the query results if any exist
            if (document.querySelector('.results-table')) {
                performQuery();
            }
        } else {
            if (result.details) {
                // 显示所有验证错误
                showMessage(result.details.join('<br>'), 'danger');
            } else {
                showMessage(result.error || result.message, 'danger');
            }
            if (result.errors) {
                console.log('Errors:', result.errors);
            }
            return;
        }

    } catch (error) {
        console.error('Error:', error);
        showMessage('An error occurred while updating states', 'danger');
    }
}

// Document ready handler
$(document).ready(function() {
    // 修改Select2初始化
    $('.select2-state').select2({
        placeholder: "Select or search for a state...",
        allowClear: true,
        dropdownParent: $('body'),
        width: '100%',
        ajax: {
            url: '{% url "volcano_tools:get_unit_states" %}',
            dataType: 'json',
            delay: 250,
            data: function(params) {
                return {
                    search: params.term || ''
                };
            },
            processResults: function(data) {
                return {
                    results: data.results
                };
            },
            cache: true
        },
        minimumInputLength: 0, // 允许不输入直接显示
        language: {
            searching: function() {
                return 'Searching...';
            },
            noResults: function() {
                return 'No states found';
            }
        }
    });

    // 当Select2打开时，确保它显示在其他元素之上
    $('.select2-state').on('select2:open', function() {
        $('.select2-dropdown').css('z-index', 9999);
    });

    $('#queryBtn').click(performQuery);
    $('#updateBtn').click(updateStates);
    
    $('#serial_numbers').keydown(function(e) {
        if (e.ctrlKey && e.key === 'Enter') {
            performQuery();
        }
    });
});
</script>
{% endblock %} 
Stack trace:
Frame         Function      Args
0007FFFFA900  00021005FE8E (000210285F68, 00021026AB6E, 0007FFFFA900, 0007FFFF9800) msys-2.0.dll+0x1FE8E
0007FFFFA900  0002100467F9 (000000000000, 000000000000, 000000000000, 0007FFFFABD8) msys-2.0.dll+0x67F9
0007FFFFA900  000210046832 (000210286019, 0007FFFFA7B8, 0007FFFFA900, 000000000000) msys-2.0.dll+0x6832
0007FFFFA900  000210068CF6 (000000000000, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x28CF6
0007FFFFA900  000210068E24 (0007FFFFA910, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x28E24
0007FFFFABE0  00021006A225 (0007FFFFA910, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x2A225
End of stack trace
Loaded modules:
000100400000 bash.exe
7FFBDA630000 ntdll.dll
7FFBD90C0000 KERNEL32.DLL
7FFBD7C80000 KERNELBASE.dll
7FFBD8480000 USER32.dll
7FFBD80F0000 win32u.dll
7FFBD9090000 GDI32.dll
7FFBD7950000 gdi32full.dll
7FFBD7BE0000 msvcp_win.dll
7FFBD8210000 ucrtbase.dll
000210040000 msys-2.0.dll
7FFBD9D70000 advapi32.dll
7FFBD8AC0000 msvcrt.dll
7FFBD8F70000 sechost.dll
7FFBD8330000 bcrypt.dll
7FFBD8360000 RPCRT4.dll
7FFBD70A0000 CRYPTBASE.DLL
7FFBD8190000 bcryptPrimitives.dll
7FFBD9220000 IMM32.DLL

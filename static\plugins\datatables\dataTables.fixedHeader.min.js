/*! FixedHeader 3.3.2
 * © SpryMedia Ltd - datatables.net/license
 */
!function(o){var i,s;"function"==typeof define&&define.amd?define(["jquery","datatables.net"],function(t){return o(t,window,document)}):"object"==typeof exports?(i=require("jquery"),s=function(t,e){e.fn.dataTable||require("datatables.net")(t,e)},"undefined"!=typeof window?module.exports=function(t,e){return t=t||window,e=e||i(t),s(t,e),o(e,t,t.document)}:(s(window,i),module.exports=o(i,window,window.document))):o(jQuery,window,document)}(function(b,H,x,v){"use strict";function s(t,e){if(!(this instanceof s))throw"FixedHeader must be initialised with the 'new' keyword.";if(!0===e&&(e={}),t=new n.Api(t),this.c=b.extend(!0,{},s.defaults,e),this.s={dt:t,position:{theadTop:0,tbodyTop:0,tfootTop:0,tfootBottom:0,width:0,left:0,tfootHeight:0,theadHeight:0,windowHeight:b(H).height(),visible:!0},headerMode:null,footerMode:null,autoWidth:t.settings()[0].oFeatures.bAutoWidth,namespace:".dtfc"+o++,scrollLeft:{header:-1,footer:-1},enable:!0},this.dom={floatingHeader:null,thead:b(t.table().header()),tbody:b(t.table().body()),tfoot:b(t.table().footer()),header:{host:null,floating:null,floatingParent:b('<div class="dtfh-floatingparent">'),placeholder:null},footer:{host:null,floating:null,floatingParent:b('<div class="dtfh-floatingparent">'),placeholder:null}},this.dom.header.host=this.dom.thead.parent(),this.dom.footer.host=this.dom.tfoot.parent(),(e=t.settings()[0])._fixedHeader)throw"FixedHeader already initialised on table "+e.nTable.id;(e._fixedHeader=this)._constructor()}var n=b.fn.dataTable,o=0;return b.extend(s.prototype,{destroy:function(){var t=this.dom;this.s.dt.off(".dtfc"),b(H).off(this.s.namespace),t.header.rightBlocker&&t.header.rightBlocker.remove(),t.header.leftBlocker&&t.header.leftBlocker.remove(),t.footer.rightBlocker&&t.footer.rightBlocker.remove(),t.footer.leftBlocker&&t.footer.leftBlocker.remove(),this.c.header&&this._modeChange("in-place","header",!0),this.c.footer&&t.tfoot.length&&this._modeChange("in-place","footer",!0)},enable:function(t,e){this.s.enable=t,!e&&e!==v||(this._positions(),this._scroll(!0))},enabled:function(){return this.s.enable},headerOffset:function(t){return t!==v&&(this.c.headerOffset=t,this.update()),this.c.headerOffset},footerOffset:function(t){return t!==v&&(this.c.footerOffset=t,this.update()),this.c.footerOffset},update:function(t){var e;this.s.enable&&(e=this.s.dt.table().node(),b(e).is(":visible")?this.enable(!0,!1):this.enable(!1,!1),0!==b(e).children("thead").length)&&(this._positions(),this._scroll(t===v||t))},_constructor:function(){var o=this,i=this.s.dt,t=(b(H).on("scroll"+this.s.namespace,function(){o._scroll()}).on("resize"+this.s.namespace,n.util.throttle(function(){o.s.position.windowHeight=b(H).height(),o.update()},50)),b(".fh-fixedHeader")),t=(!this.c.headerOffset&&t.length&&(this.c.headerOffset=t.outerHeight()),b(".fh-fixedFooter"));!this.c.footerOffset&&t.length&&(this.c.footerOffset=t.outerHeight()),i.on("column-reorder.dt.dtfc column-visibility.dt.dtfc column-sizing.dt.dtfc responsive-display.dt.dtfc",function(t,e){o.update()}).on("draw.dt.dtfc",function(t,e){o.update(e!==i.settings()[0])}),i.on("destroy.dtfc",function(){o.destroy()}),this._positions(),this._scroll()},_clone:function(t,e){var o,i,s,n,r=this,d=this.s.dt,a=this.dom[t],f="header"===t?this.dom.thead:this.dom.tfoot;"footer"===t&&this._scrollEnabled()||(!e&&a.floating?a.floating.removeClass("fixedHeader-floating fixedHeader-locked"):(e=b(x).scrollLeft(),o=b(x).scrollTop(),a.floating&&(null!==a.placeholder&&a.placeholder.remove(),this._unsize(t),a.floating.children().detach(),a.floating.remove()),i=b(d.table().node()),s=b(i.parent()),n=this._scrollEnabled(),a.floating=b(d.table().node().cloneNode(!1)).attr("aria-hidden","true").css({"table-layout":"fixed",top:0,left:0}).removeAttr("id").append(f),a.floatingParent.css({width:s.width(),overflow:"hidden",height:"fit-content",position:"fixed",left:n?i.offset().left+s.scrollLeft():0}).css("header"===t?{top:this.c.headerOffset,bottom:""}:{top:"",bottom:this.c.footerOffset}).addClass("footer"===t?"dtfh-floatingparentfoot":"dtfh-floatingparenthead").append(a.floating).appendTo("body"),this._stickyPosition(a.floating,"-"),(d=function(){var t=s.scrollLeft();r.s.scrollLeft={footer:t,header:t},a.floatingParent.scrollLeft(r.s.scrollLeft.header)})(),s.off("scroll.dtfh").on("scroll.dtfh",d),a.placeholder=f.clone(!1),a.placeholder.find("*[id]").removeAttr("id"),a.host.prepend(a.placeholder),this._matchWidths(a.placeholder,a.floating),b(x).scrollTop(o).scrollLeft(e)))},_stickyPosition:function(t,i){var s,n;this._scrollEnabled()&&(n="rtl"===b((s=this).s.dt.table().node()).css("direction"),t.find("th").each(function(){var t,e,o;"sticky"===b(this).css("position")&&(t=b(this).css("right"),e=b(this).css("left"),"auto"===t||n?"auto"!==e&&n&&(o=+e.replace(/px/g,"")+("-"===i?-1:1)*s.s.dt.settings()[0].oBrowser.barWidth,b(this).css("left",0<o?o:0)):(o=+t.replace(/px/g,"")+("-"===i?-1:1)*s.s.dt.settings()[0].oBrowser.barWidth,b(this).css("right",0<o?o:0)))}))},_matchWidths:function(e,o){function t(t){return b(t,e).map(function(){return+b(this).css("width").replace(/[^\d\.]/g,"")}).toArray()}function i(t,e){b(t,o).each(function(t){b(this).css({width:e[t],minWidth:e[t]})})}var s=t("th"),n=t("td");i("th",s),i("td",n)},_unsize:function(t){var e=this.dom[t].floating;e&&("footer"===t||"header"===t&&!this.s.autoWidth)?b("th, td",e).css({width:"",minWidth:""}):e&&"header"===t&&b("th, td",e).css("min-width","")},_horizontal:function(t,e){var o,i=this.dom[t],s=(this.s.position,this.s.scrollLeft);i.floating&&s[t]!==e&&(this._scrollEnabled()&&(o=b(b(this.s.dt.table().node()).parent()).scrollLeft(),i.floating.scrollLeft(o),i.floatingParent.scrollLeft(o)),s[t]=e)},_modeChange:function(t,e,o){this.s.dt;var i,s,n,r,d,a,f,h=this.dom[e],l=this.s.position,c=this._scrollEnabled();"footer"===e&&c||(i=function(o){h.floating.attr("style",function(t,e){return(e||"")+"width: "+o+"px !important;"}),c||h.floatingParent.attr("style",function(t,e){return(e||"")+"width: "+o+"px !important;"})},r=this.dom["footer"===e?"tfoot":"thead"],s=b.contains(r[0],x.activeElement)?x.activeElement:null,d=b(b(this.s.dt.table().node()).parent()),"in-place"===t?(h.placeholder&&(h.placeholder.remove(),h.placeholder=null),this._unsize(e),"header"===e?h.host.prepend(r):h.host.append(r),h.floating&&(h.floating.remove(),h.floating=null,this._stickyPosition(h.host,"+")),h.floatingParent&&h.floatingParent.remove(),b(b(h.host.parent()).parent()).scrollLeft(d.scrollLeft())):"in"===t?(this._clone(e,o),r=d.offset(),f=(n=b(x).scrollTop())+b(H).height(),a=c?r.top:l.tbodyTop,r=c?r.top+d.outerHeight():l.tfootTop,d="footer"===e?f<a?l.tfootHeight:a+l.tfootHeight-f:n+this.c.headerOffset+l.theadHeight-r,a="header"===e?"top":"bottom",f=this.c[e+"Offset"]-(0<d?d:0),h.floating.addClass("fixedHeader-floating"),h.floatingParent.css(a,f).css({left:l.left,height:"header"===e?l.theadHeight:l.tfootHeight,"z-index":2}).append(h.floating),i(l.width),"footer"===e&&h.floating.css("top","")):"below"===t?(this._clone(e,o),h.floating.addClass("fixedHeader-locked"),h.floatingParent.css({position:"absolute",top:l.tfootTop-l.theadHeight,left:l.left+"px"}),i(l.width)):"above"===t&&(this._clone(e,o),h.floating.addClass("fixedHeader-locked"),h.floatingParent.css({position:"absolute",top:l.tbodyTop,left:l.left+"px"}),i(l.width)),s&&s!==x.activeElement&&setTimeout(function(){s.focus()},10),this.s.scrollLeft.header=-1,this.s.scrollLeft.footer=-1,this.s[e+"Mode"]=t)},_positions:function(){var t=this.s.dt,e=t.table(),o=this.s.position,i=this.dom,e=b(e.node()),s=this._scrollEnabled(),n=b(t.table().header()),t=b(t.table().footer()),i=i.tbody,r=e.parent();o.visible=e.is(":visible"),o.width=e.outerWidth(),o.left=e.offset().left,o.theadTop=n.offset().top,o.tbodyTop=(s?r:i).offset().top,o.tbodyHeight=(s?r:i).outerHeight(),o.theadHeight=n.outerHeight(),o.theadBottom=o.theadTop+o.theadHeight,t.length?(o.tfootTop=o.tbodyTop+o.tbodyHeight,o.tfootBottom=o.tfootTop+t.outerHeight(),o.tfootHeight=t.outerHeight()):(o.tfootTop=o.tbodyTop+i.outerHeight(),o.tfootBottom=o.tfootTop,o.tfootHeight=o.tfootTop)},_scroll:function(t){var e,o,i,s,n,r,d,a,f,h,l,c,p,u,g,m;this.s.dt.settings()[0].bDestroying||(e=this._scrollEnabled(),o=(h=b(this.s.dt.table().node()).parent()).offset(),c=h.outerHeight(),i=b(x).scrollLeft(),s=b(x).scrollTop(),a=(l=b(H).height())+s,p=this.s.position,m=e?o.top:p.tbodyTop,r=(e?o:p).left,c=e?o.top+c:p.tfootTop,d=e?h.outerWidth():p.tbodyWidth,a=s+l,this.c.header&&(!this.s.enable||!p.visible||s+this.c.headerOffset+p.theadHeight<=m?f="in-place":s+this.c.headerOffset+p.theadHeight>m&&s+this.c.headerOffset+p.theadHeight<c?(f="in",h=b(b(this.s.dt.table().node()).parent()),s+this.c.headerOffset+p.theadHeight>c||this.dom.header.floatingParent===v?t=!0:this.dom.header.floatingParent.css({top:this.c.headerOffset,position:"fixed"}).append(this.dom.header.floating)):f="below",!t&&f===this.s.headerMode||this._modeChange(f,"header",t),this._horizontal("header",i)),u={offset:{top:0,left:0},height:0},g={offset:{top:0,left:0},height:0},this.c.footer&&this.dom.tfoot.length&&(!this.s.enable||!p.visible||p.tfootBottom+this.c.footerOffset<=a?n="in-place":c+p.tfootHeight+this.c.footerOffset>a&&m+this.c.footerOffset<a?(n="in",t=!0):n="above",!t&&n===this.s.footerMode||this._modeChange(n,"footer",t),this._horizontal("footer",i),l=function(t){return{offset:t.offset(),height:t.outerHeight()}},u=this.dom.header.floating?l(this.dom.header.floating):l(this.dom.thead),g=this.dom.footer.floating?l(this.dom.footer.floating):l(this.dom.tfoot),e)&&g.offset.top>s&&(p=a+((c=s-o.top)>-u.height?c:0)-(u.offset.top+(c<-u.height?u.height:0)+g.height),h.outerHeight(p=p<0?0:p),Math.round(h.outerHeight())>=Math.round(p)?b(this.dom.tfoot.parent()).addClass("fixedHeader-floating"):b(this.dom.tfoot.parent()).removeClass("fixedHeader-floating")),this.dom.header.floating&&this.dom.header.floatingParent.css("left",r-i),this.dom.footer.floating&&this.dom.footer.floatingParent.css("left",r-i),this.s.dt.settings()[0]._fixedColumns!==v&&(this.dom.header.rightBlocker=(m=function(t,e,o){var i;return null!==(o=o===v?0===(i=b("div.dtfc-"+t+"-"+e+"-blocker")).length?null:i.clone().css("z-index",1):o)&&("in"===f||"below"===f?o.appendTo("body").css({top:("top"===e?u:g).offset.top,left:"right"===t?r+d-o.width():r}):o.detach()),o})("right","top",this.dom.header.rightBlocker),this.dom.header.leftBlocker=m("left","top",this.dom.header.leftBlocker),this.dom.footer.rightBlocker=m("right","bottom",this.dom.footer.rightBlocker),this.dom.footer.leftBlocker=m("left","bottom",this.dom.footer.leftBlocker)))},_scrollEnabled:function(){var t=this.s.dt.settings()[0].oScroll;return""!==t.sY||""!==t.sX}}),s.version="3.3.2",s.defaults={header:!0,footer:!1,headerOffset:0,footerOffset:0},b.fn.dataTable.FixedHeader=s,b.fn.DataTable.FixedHeader=s,b(x).on("init.dt.dtfh",function(t,e,o){var i;"dt"===t.namespace&&(t=e.oInit.fixedHeader,i=n.defaults.fixedHeader,t||i)&&!e._fixedHeader&&(i=b.extend({},i,t),!1!==t)&&new s(e,i)}),n.Api.register("fixedHeader()",function(){}),n.Api.register("fixedHeader.adjust()",function(){return this.iterator("table",function(t){t=t._fixedHeader;t&&t.update()})}),n.Api.register("fixedHeader.enable()",function(e){return this.iterator("table",function(t){t=t._fixedHeader;e=e===v||e,t&&e!==t.enabled()&&t.enable(e)})}),n.Api.register("fixedHeader.enabled()",function(){if(this.context.length){var t=this.context[0]._fixedHeader;if(t)return t.enabled()}return!1}),n.Api.register("fixedHeader.disable()",function(){return this.iterator("table",function(t){t=t._fixedHeader;t&&t.enabled()&&t.enable(!1)})}),b.each(["header","footer"],function(t,o){n.Api.register("fixedHeader."+o+"Offset()",function(e){var t=this.context;return e===v?t.length&&t[0]._fixedHeader?t[0]._fixedHeader[o+"Offset"]():v:this.iterator("table",function(t){t=t._fixedHeader;t&&t[o+"Offset"](e)})})}),n});
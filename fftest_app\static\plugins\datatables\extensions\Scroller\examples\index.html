<!DOCTYPE html>
<html>
<head>
	<meta charset="utf-8">
	<link rel="shortcut icon" type="image/ico" href="http://www.datatables.net/favicon.ico">
	<meta name="viewport" content="initial-scale=1.0, maximum-scale=2.0">
	<link rel="stylesheet" type="text/css" href="../../../examples/resources/syntax/shCore.css">
	<link rel="stylesheet" type="text/css" href="../../../examples/resources/demo.css">
	<script type="text/javascript" language="javascript" src="../../../media/js/jquery.js"></script>
	<script type="text/javascript" language="javascript" src="../../../examples/resources/syntax/shCore.js"></script>
	<script type="text/javascript" language="javascript" src="../../../examples/resources/demo.js"></script>

	<title>Scroller examples - <PERSON>roller examples</title>
</head>

<body class="dt-example">
	<div class="container">
		<section>
			<h1>Scroller example <span>Scroller examples</span></h1>

			<div class="info">
				<p>Scroller is a virtual rendering plug-in for DataTables which allows large datasets to be drawn on
				screen every quickly. What the virtual rendering means is that only the visible portion of the table
				(and a bit to either side to make the scrolling smooth) is drawn, while the scrolling container gives
				the visual impression that the whole table is visible. This is done by making use of the pagination
				abilities of DataTables and moving the table around in the scrolling container DataTables adds to the
				page. The scrolling container is forced to the height it would be for the full table display using an
				extra element.</p>

				<p>Scroller is initialised by simply including the letter <code>S</code> in the <a href=
				"//datatables.net/reference/option/dom"><code class="option" title=
				"DataTables initialisation option">dom<span>DT</span></code></a> for the table you want to have this
				feature enabled on. Note that the <code>S</code> must come after the <code>t</code> parameter in
				<a href="//datatables.net/reference/option/dom"><code class="option" title=
				"DataTables initialisation option">dom<span>DT</span></code></a>.</p>

				<p>Key features include:</p>

				<ul class="markdown">
					<li>Speed! The aim of Scroller for DataTables is to make rendering large data sets fast</li>
					<li>Full compatibility with DataTables' deferred rendering for maximum speed</li>
					<li>Integration with state saving in DataTables (scrolling position is saved)</li>
					<li>Easy to use</li>
				</ul>
			</div>
		</section>
	</div>

	<section>
		<div class="footer">
			<div class="gradient"></div>

			<div class="liner">
				<div class="toc">
					<div class="toc-group">
						<h3><a href="./index.html">Examples</a></h3>
						<ul class="toc">
							<li><a href="simple.html">Basic initialisation</a></li>
							<li><a href="state_saving.html">State saving</a></li>
							<li><a href="large_js_source.html">Client-side data source (50,000 rows)</a></li>
							<li><a href="server-side_processing.html">Server-side processing (5,000,000
							rows)</a></li>
							<li><a href="api_scrolling.html">API</a></li>
						</ul>
					</div>
				</div>

				<div class="epilogue">
					<p>Please refer to the <a href="http://www.datatables.net">DataTables documentation</a> for full
					information about its API properties and methods.<br>
					Additionally, there are a wide range of <a href="http://www.datatables.net/extras">extras</a> and
					<a href="http://www.datatables.net/plug-ins">plug-ins</a> which extend the capabilities of
					DataTables.</p>

					<p class="copyright">DataTables designed and created by <a href=
					"http://www.sprymedia.co.uk">SpryMedia Ltd</a> &#169; 2007-2014<br>
					DataTables is licensed under the <a href="http://www.datatables.net/mit">MIT license</a>.</p>
				</div>
			</div>
		</div>
	</section>
</body>
</html>
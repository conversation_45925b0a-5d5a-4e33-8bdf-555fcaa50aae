# database.py
from sqlalchemy import create_engine, text
from sqlalchemy.pool import Queue<PERSON>ool
from django.conf import settings
from contextlib import contextmanager

class DB:
    _instance = None

    def __new__(cls):
        if cls._instance is None:
            cls._instance = super(DB, cls).__new__(cls)
            db_settings = settings.DATABASES['VolcanoFFDB']
            connection_string = f"mssql+pyodbc://{db_settings['USER']}:{db_settings['PASSWORD']}@{db_settings['HOST']},{db_settings['PORT']}/{db_settings['NAME']}?driver=ODBC+Driver+17+for+SQL+Server"
            cls._instance.engine = create_engine(connection_string, poolclass=QueuePool, pool_size=5, max_overflow=10)
        return cls._instance

    @contextmanager
    def get_connection(self):
        connection = self.engine.connect()
        try:
            yield connection
        finally:
            connection.close()

    def exec_query(self, query, parameters=None):
        with self.get_connection() as connection:
            result = connection.execute(text(query), parameters or {})
            return result.fetchall()

    def exec_non_query(self, query, parameters=None):
        with self.get_connection() as connection:
            result = connection.execute(text(query), parameters or {})
            connection.commit()
            return result.rowcount

    def exec_many(self, query, parameters):
        print("Query:", query)
        print("Parameters:", parameters[:5])  # 只打印前5个参数，避免输出过多
        with self.get_connection() as connection:
            connection.execute(text(query), parameters)
            connection.commit()

{% load static %}
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Charger & Holder WIP 状态图表</title>
    <script src="{% static 'js/echarts.min.js' %}"></script>
    <style>
        body {
            background-color: #121212; /* 深黑色背景 */
            color: #e0e0e0; /* 浅色文字以便阅读 */
            font-family: sans-serif;
            margin: 0; /* 移除默认边距 */
            padding: 0; /* 移除默认填充 */
        }
        .charts-grid {
            display: flex;
            flex-wrap: wrap; /* 允许换行 */
            justify-content: space-around; /* 图表间留有空隙 */
            padding: 20px;
        }
        .chart-container {
            width: 48%; /* 每个图表大约占据一半宽度 */
            min-width: 300px; /* 最小宽度 */
            height: 450px; /* 或者您希望的高度 */
            margin-bottom: 20px; /* 图表间垂直间距 */
            border: 1px solid #333; /* 可选：为图表添加边框 */
            background-color: #1e1e1e; /* 图表容器的背景色，略浅于 body */
            border-radius: 8px; /* 可选：圆角 */
            box-sizing: border-box; /* 确保 padding 和 border 不会增加元素的总宽度 */
        }
        h1 {
            text-align: center;
            color: #ffffff; /* 标题文字颜色 */
            margin-top: 20px;
            margin-bottom: 30px;
        }
    </style>
</head>
<body>
    <h1>Hermes Charger & Holder Mid Supermarket WIP 状态</h1>

    {% if error_message %}
        <p style="color: red; text-align: center;">{{ error_message }}</p>
    {% endif %}

    {{ charger_waiting_laser_data|json_script:"charger-waiting-laser-data" }}
    {{ charger_after_laser_data|json_script:"charger-after-laser-data" }}
    {{ holder_waiting_laser_data|json_script:"holder-waiting-laser-data" }}
    {{ holder_after_laser_data|json_script:"holder-after-laser-data" }}

    <div class="charts-grid">
        <div id="chargerWaitingLaserChart" class="chart-container"></div>
        <div id="chargerAfterLaserChart" class="chart-container"></div>
        <div id="holderWaitingLaserChart" class="chart-container"></div>
        <div id="holderAfterLaserChart" class="chart-container"></div>
    </div>

    <script type="text/javascript">
        document.addEventListener('DOMContentLoaded', function () {
            // Charger WaitingLaser 图表数据
            const chargerWaitingLaserDataElement = document.getElementById('charger-waiting-laser-data');
            const chargerWaitingLaserData = chargerWaitingLaserDataElement ? JSON.parse(chargerWaitingLaserDataElement.textContent) : [];
            
            // Charger AfterLaser 图表数据
            const chargerAfterLaserDataElement = document.getElementById('charger-after-laser-data');
            const chargerAfterLaserData = chargerAfterLaserDataElement ? JSON.parse(chargerAfterLaserDataElement.textContent) : [];

            // Holder WaitingLaser 图表数据
            const holderWaitingLaserDataElement = document.getElementById('holder-waiting-laser-data');
            const holderWaitingLaserData = holderWaitingLaserDataElement ? JSON.parse(holderWaitingLaserDataElement.textContent) : [];

            // Holder AfterLaser 图表数据
            const holderAfterLaserDataElement = document.getElementById('holder-after-laser-data');
            const holderAfterLaserData = holderAfterLaserDataElement ? JSON.parse(holderAfterLaserDataElement.textContent) : [];

            var chargerWaitingLaserChart, chargerAfterLaserChart, holderWaitingLaserChart, holderAfterLaserChart;

            const colorMap = {
                'Breeze Blue': '#87CEEB', // 天蓝色
                'Digital Violet': '#8A2BE2', // 紫罗兰蓝
                'Leaf Green': '#90EE90',   // 嫩绿色
                'Midnight Black': '#777777', // 深灰色 (替代黑色以便在黑背景下显示)
                'Vivid Terracotta': '#E2725B', // 鲜艳的陶土色
                // 您可以在这里添加更多颜色映射
            };

            function applyCustomColors(data) {
                return data.map(item => ({
                    ...item,
                    itemStyle: {
                        color: colorMap[item.name] || null // 如果颜色未在 map 中定义，则使用 ECharts 默认颜色
                    }
                }));
            }

            // 初始化 Charger WaitingLaser 图表
            if (chargerWaitingLaserData && chargerWaitingLaserData.length > 0) {
                chargerWaitingLaserChart = echarts.init(document.getElementById('chargerWaitingLaserChart'), 'dark');
                var chargerWaitingLaserTotal = chargerWaitingLaserData.reduce((sum, item) => sum + item.value, 0);
                var chargerWaitingLaserOption = {
                    backgroundColor: 'transparent',
                    title: [
                        {
                            text: 'Charger WaitingLaser 状态颜色分布',
                            left: 'center',
                            top: '5%',
                            textStyle: { color: '#ccc', fontSize: 16 }
                        },
                        {
                            text: '总数\n' + chargerWaitingLaserTotal,
                            left: 'center',
                            top: '53%', // 调整此值以在环中心垂直对齐
                            textAlign: 'center',
                            textStyle: { fontSize: 20, fontWeight: 'bold', color: '#fff' }
                        }
                    ],
                    tooltip: { trigger: 'item', formatter: '{a} <br/>{b} : {c} ({d}%)' },
                    legend: { orient: 'vertical', left: 'left', data: chargerWaitingLaserData.map(function (item) { return item.name; }), textStyle: { color: '#ccc' } },
                    series: [{
                        name: 'Charger WaitingLaser', type: 'pie', 
                        radius: ['45%', '70%'], // 修改为双环图
                        center: ['50%', '55%'], // 确保图表居中，与中心标题对齐
                        data: applyCustomColors(chargerWaitingLaserData),
                        avoidLabelOverlap: true,
                        emphasis: { itemStyle: { shadowBlur: 10, shadowOffsetX: 0, shadowColor: 'rgba(0, 0, 0, 0.5)' } },
                        label: { show: true, formatter: '{b}: {c}\n({d}%)', color: '#ccc' },
                        labelLine: { lineStyle: { color: '#888' } }
                    }]
                };
                chargerWaitingLaserChart.setOption(chargerWaitingLaserOption);
            } else if (document.getElementById('chargerWaitingLaserChart')) {
                document.getElementById('chargerWaitingLaserChart').innerHTML = '<p style="text-align:center; padding-top: 40%; color: #ccc;">Charger WaitingLaser 状态无数据</p>';
            }

            // 初始化 Charger AfterLaser 图表
            if (chargerAfterLaserData && chargerAfterLaserData.length > 0) {
                chargerAfterLaserChart = echarts.init(document.getElementById('chargerAfterLaserChart'), 'dark');
                var chargerAfterLaserTotal = chargerAfterLaserData.reduce((sum, item) => sum + item.value, 0);
                var chargerAfterLaserOption = {
                    backgroundColor: 'transparent',
                     title: [
                        {
                            text: 'Charger AfterLaser 状态颜色分布',
                            left: 'center',
                            top: '5%',
                            textStyle: { color: '#ccc', fontSize: 16 }
                        },
                        {
                            text: '总数\n' + chargerAfterLaserTotal,
                            left: 'center',
                            top: '53%',
                            textAlign: 'center',
                            textStyle: { fontSize: 20, fontWeight: 'bold', color: '#fff' }
                        }
                    ],
                    tooltip: { trigger: 'item', formatter: '{a} <br/>{b} : {c} ({d}%)' },
                    legend: { orient: 'vertical', left: 'left', data: chargerAfterLaserData.map(function (item) { return item.name; }), textStyle: { color: '#ccc' } },
                    series: [{
                        name: 'Charger AfterLaser', type: 'pie', 
                        radius: ['45%', '70%'], // 修改为双环图
                        center: ['50%', '55%'],
                        data: applyCustomColors(chargerAfterLaserData),
                        avoidLabelOverlap: true,
                        emphasis: { itemStyle: { shadowBlur: 10, shadowOffsetX: 0, shadowColor: 'rgba(0, 0, 0, 0.5)' } },
                        label: { show: true, formatter: '{b}: {c}\n({d}%)', color: '#ccc' },
                        labelLine: { lineStyle: { color: '#888' } }
                    }]
                };
                chargerAfterLaserChart.setOption(chargerAfterLaserOption);
            } else if (document.getElementById('chargerAfterLaserChart')) {
                document.getElementById('chargerAfterLaserChart').innerHTML = '<p style="text-align:center; padding-top: 40%; color: #ccc;">Charger AfterLaser 状态无数据</p>';
            }

            // 初始化 Holder WaitingLaser 图表
            if (holderWaitingLaserData && holderWaitingLaserData.length > 0) {
                holderWaitingLaserChart = echarts.init(document.getElementById('holderWaitingLaserChart'), 'dark');
                var holderWaitingLaserTotal = holderWaitingLaserData.reduce((sum, item) => sum + item.value, 0);
                var holderWaitingLaserOption = {
                    backgroundColor: 'transparent',
                    title: [
                        {
                            text: 'Holder WaitingLaser 状态颜色分布',
                            left: 'center',
                            top: '5%',
                            textStyle: { color: '#ccc', fontSize: 16 }
                        },
                        {
                            text: '总数\n' + holderWaitingLaserTotal,
                            left: 'center',
                            top: '53%',
                            textAlign: 'center',
                            textStyle: { fontSize: 20, fontWeight: 'bold', color: '#fff' }
                        }
                    ],
                    tooltip: { trigger: 'item', formatter: '{a} <br/>{b} : {c} ({d}%)' },
                    legend: { orient: 'vertical', left: 'left', data: holderWaitingLaserData.map(function (item) { return item.name; }), textStyle: { color: '#ccc' } },
                    series: [{
                        name: 'Holder WaitingLaser', type: 'pie', 
                        radius: ['45%', '70%'], // 修改为双环图
                        center: ['50%', '55%'],
                        data: applyCustomColors(holderWaitingLaserData),
                        avoidLabelOverlap: true,
                        emphasis: { itemStyle: { shadowBlur: 10, shadowOffsetX: 0, shadowColor: 'rgba(0, 0, 0, 0.5)' } },
                        label: { show: true, formatter: '{b}: {c}\n({d}%)', color: '#ccc' },
                        labelLine: { lineStyle: { color: '#888' } }
                    }]
                };
                holderWaitingLaserChart.setOption(holderWaitingLaserOption);
            } else if (document.getElementById('holderWaitingLaserChart')) {
                document.getElementById('holderWaitingLaserChart').innerHTML = '<p style="text-align:center; padding-top: 40%; color: #ccc;">Holder WaitingLaser 状态无数据</p>';
            }

            // 初始化 Holder AfterLaser 图表
            if (holderAfterLaserData && holderAfterLaserData.length > 0) {
                holderAfterLaserChart = echarts.init(document.getElementById('holderAfterLaserChart'), 'dark');
                var holderAfterLaserTotal = holderAfterLaserData.reduce((sum, item) => sum + item.value, 0);
                var holderAfterLaserOption = {
                    backgroundColor: 'transparent',
                    title: [
                        {
                            text: 'Holder AfterLaser 状态颜色分布',
                            left: 'center',
                            top: '5%',
                            textStyle: { color: '#ccc', fontSize: 16 }
                        },
                        {
                            text: '总数\n' + holderAfterLaserTotal,
                            left: 'center',
                            top: '53%',
                            textAlign: 'center',
                            textStyle: { fontSize: 20, fontWeight: 'bold', color: '#fff' }
                        }
                    ],
                    tooltip: { trigger: 'item', formatter: '{a} <br/>{b} : {c} ({d}%)' },
                    legend: { orient: 'vertical', left: 'left', data: holderAfterLaserData.map(function (item) { return item.name; }), textStyle: { color: '#ccc' } },
                    series: [{
                        name: 'Holder AfterLaser', type: 'pie', 
                        radius: ['45%', '70%'], // 修改为双环图
                        center: ['50%', '55%'],
                        data: applyCustomColors(holderAfterLaserData),
                        avoidLabelOverlap: true,
                        emphasis: { itemStyle: { shadowBlur: 10, shadowOffsetX: 0, shadowColor: 'rgba(0, 0, 0, 0.5)' } },
                        label: { show: true, formatter: '{b}: {c}\n({d}%)', color: '#ccc' },
                        labelLine: { lineStyle: { color: '#888' } }
                    }]
                };
                holderAfterLaserChart.setOption(holderAfterLaserOption);
            } else if (document.getElementById('holderAfterLaserChart')) {
                document.getElementById('holderAfterLaserChart').innerHTML = '<p style="text-align:center; padding-top: 40%; color: #ccc;">Holder AfterLaser 状态无数据</p>';
            }

            // 响应式调整图表大小
            window.addEventListener('resize', function(){
                if (chargerWaitingLaserChart) chargerWaitingLaserChart.resize();
                if (chargerAfterLaserChart) chargerAfterLaserChart.resize();
                if (holderWaitingLaserChart) holderWaitingLaserChart.resize();
                if (holderAfterLaserChart) holderAfterLaserChart.resize();
            });
        });
    </script>
</body>
</html>
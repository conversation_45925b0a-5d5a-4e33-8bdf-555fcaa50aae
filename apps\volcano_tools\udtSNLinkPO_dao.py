from django.db import connections
from .tools import add_fs_language

class SNLinkPODao:
    def query_udtSNLinkPO(self, locations=None, ponumber=None, query_type="snlist"):
        conn = connections['VolcanoFFDB']
        with conn.cursor() as cursor:
            if query_type == "snlist" and locations:
                placeholders = ','.join(['%s'] * len(locations))
                sql = f"""
                    SELECT pf.Name, sn.Value, po.ProductionOrderNumber, a.RequestSNC, a.RequestBY, a.retcode, a.status, a.lastupdate
                    FROM dbo.udtSNLinkPO(nolock)a
                    JOIN dbo.ffSerialNumber sn(nolock) ON sn.UnitID = a.unitid
                    JOIN dbo.ffProductionOrder po(nolock) ON po.id = a.LinkPOid
                    JOIN dbo.luPartFamily pf(nolock) ON pf.id = a.unitpartfamilyid
                    WHERE sn.Value IN ({placeholders})
                    ORDER BY a.lastupdate DESC
                """
                cursor.execute(sql, locations)
            elif ponumber:
                sql = """
                    SELECT pf.Name, sn.Value, po.ProductionOrderNumber, a.RequestSNC, a.RequestBY, a.retcode, a.status, a.lastupdate
                    FROM dbo.udtSNLinkPO(nolock) a
                    JOIN dbo.ffSerialNumber sn(nolock) ON sn.UnitID = a.unitid
                    JOIN dbo.ffProductionOrder po(nolock) ON po.id = a.LinkPOid
                    JOIN dbo.luPartFamily pf(nolock) ON pf.id = a.unitpartfamilyid
                    WHERE po.ProductionOrderNumber = %s
                    ORDER BY a.lastupdate DESC
                """
                cursor.execute(sql, [ponumber])
            else:
                return []
            return cursor.fetchall()

    def query_ponumber(self, po_number):
        if not po_number:
            return 0
        conn = connections['VolcanoFFDB']
        with conn.cursor() as cursor:
            cursor.execute("SELECT ID FROM ffProductionOrder WHERE ProductionOrderNumber = %s", [po_number])
            result = cursor.fetchone()
            return result[0] if result else 0

    def insert_LinkPO(self, sn_list, poid, snc, requestBy, remark):
        if not sn_list:
            return
        conn = connections['VolcanoFFDB']
        with conn.cursor() as cursor:
            retcode = add_fs_language(remark, 'udtLinkPO')
            sn_values = ','.join(["'{}'".format(str(sn).replace("'", "''")) for sn in sn_list])
            sql = f"""
            INSERT INTO udtSNLinkPO (unitid, LinkPOid, RequestSNC, RequestBY, status, retcode, lastupdate, unitpartfamilyid)
            SELECT sn.UnitID, {poid}, '{snc}', '{requestBy}', 0, {retcode}, GETDATE(), p.PartFamilyID
            FROM dbo.ffSerialNumber(nolock) sn
            JOIN dbo.ffUnit(nolock) u ON u.id = sn.UnitID
            JOIN dbo.ffPart p ON p.id = u.PartID
            WHERE sn.Value IN ({sn_values})
            """
            #print(sql)
            cursor.execute(sql)
        conn.commit()

    def update_LinkPO(self, values):
        if not values:
            return
        conn = connections['VolcanoFFDB']
        with conn.cursor() as cursor:
            placeholders = ",".join(["%s" for _ in values])
            sql = f"""UPDATE udtSNLinkPO SET status=1,lastupdate=GETDATE()
         WHERE unitid IN(SELECT unitid FROM dbo.ffSerialNumber(nolock)  WHERE  Value IN ({placeholders}))"""
            cursor.execute(sql, values)
        conn.commit()
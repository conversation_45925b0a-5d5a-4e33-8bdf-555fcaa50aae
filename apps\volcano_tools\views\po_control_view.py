from django.shortcuts import render
from django.db import connections
from django.views.decorators.http import require_http_methods
from django.contrib import messages
from django.http import JsonResponse
from django.views.decorators.csrf import csrf_protect
import json

@require_http_methods(['GET', 'POST'])
def po_control(request):
    bbpo = request.GET.get('bbpo', '') or request.POST.get('bbpo', '')
    kitpo = request.GET.get('kitpo', '') or request.POST.get('kitpo', '')
    subpo = request.GET.get('subpo', '') or request.POST.get('subpo', '')
    
    data = []
    
    if request.method == 'POST':
        try:
            request_by = request.POST.get('request_by', '')
            request_snc = request.POST.get('request_snc', '')
            
            with connections['VolcanoFFDB'].cursor() as cursor:
                # 验证 BB PO
                cursor.execute("""
                    SELECT ID FROM ffProductionOrder 
                    WHERE ProductionOrderNumber = %s
                """, [bbpo])
                bb_po_result = cursor.fetchone()
                
                if not bb_po_result:
                    messages.error(request, f'BB PO Error: {bbpo}')
                    return render(request, 'po_control/index.html', {'data': data, 'bbpo': bbpo, 'kitpo': kitpo, 'subpo': subpo})
                
                bb_po_id = bb_po_result[0]
                success = False
                
                # 验证并处理 Kit PO
                if kitpo:
                    cursor.execute("""
                        SELECT ID FROM ffProductionOrder 
                        WHERE ProductionOrderNumber = %s
                    """, [kitpo])
                    kit_po_result = cursor.fetchone()
                    
                    if not kit_po_result:
                        messages.error(request, f'Kit PO工单号不存在: {kitpo}')
                        return render(request, 'po_control/index.html', {'data': data, 'bbpo': bbpo, 'kitpo': kitpo, 'subpo': subpo})
                    
                    kit_po_id = kit_po_result[0]
                    cursor.execute("""
                        INSERT INTO udtCheckPOControl 
                        (BBPOID, PackagePOID, Status, RequestBy, RequestSNC, Lastupdate)
                        VALUES (%s, %s, 0, %s, %s, GETDATE())
                    """, [bb_po_id, kit_po_id, request_by, request_snc])
                    success = True
                
                # 验证并处理 Sub PO
                if subpo:
                    cursor.execute("""
                        SELECT ID FROM ffProductionOrder 
                        WHERE ProductionOrderNumber = %s
                    """, [subpo])
                    sub_po_result = cursor.fetchone()
                    
                    if not sub_po_result:
                        messages.error(request, f'Sub PO工单号不存在: {subpo}')
                        return render(request, 'po_control/index.html', {'data': data, 'bbpo': bbpo, 'kitpo': kitpo, 'subpo': subpo})
                    
                    sub_po_id = sub_po_result[0]
                    cursor.execute("""
                        INSERT INTO udtCheckPOControl 
                        (BBPOID, SubPOID, Status, RequestBy, RequestSNC, Lastupdate)
                        VALUES (%s, %s, 0, %s, %s, GETDATE())
                    """, [bb_po_id, sub_po_id, request_by, request_snc])
                    success = True
                
                # 验证是否至少有一个 Kit PO 或 Sub PO
                if not kitpo and not subpo:
                    messages.error(request, '必须输入Kit PO或Sub PO中的至少一个')
                    return render(request, 'po_control/index.html', {'data': data, 'bbpo': bbpo, 'kitpo': kitpo, 'subpo': subpo})
                
                if success:
                    messages.success(request, '数据添加成功！')
                else:
                    messages.error(request, '无法插入记录，请检查输入的工单号')
                
        except Exception as e:
            messages.error(request, f'系统错误：{str(e)}')
    
    # 查询数据（GET请求或POST后的刷新）
    try:
        with connections['VolcanoFFDB'].cursor() as cursor:
            cursor.execute("SET NOCOUNT ON")
            cursor.execute("""
                EXEC [dbo].[UDPQueryCheckPOControl] 
                    @BBPO = %s,
                    @KitPO = %s,
                    @subPO = %s
            """, [bbpo, kitpo, subpo])
            
            if cursor.description:
                columns = [col[0] for col in cursor.description]
                rows = cursor.fetchall()
                
                for row in rows:
                    row_dict = {}
                    for i, value in enumerate(row):
                        if isinstance(value, str):
                            value = value.strip()
                        row_dict[columns[i]] = value
                    data.append(row_dict)
                    
    except Exception as e:
        messages.error(request, f'查询失败：{str(e)}')
    
    context = {
        'data': data,
        'bbpo': bbpo,
        'kitpo': kitpo,
        'subpo': subpo
    }
    return render(request, 'po_control/index.html', context)

@require_http_methods(['POST'])
@csrf_protect
def update_status(request):
    try:
        data = json.loads(request.body)
        id = data.get('id')
        status = data.get('status')
        
        with connections['VolcanoFFDB'].cursor() as cursor:
            cursor.execute("SET NOCOUNT ON")
            cursor.execute("""
                UPDATE udtCheckPOControl 
                SET Status = %s, 
                    Lastupdate = GETDATE() 
                WHERE ID = %s
            """, [status, id])
            
        return JsonResponse({'success': True})
    except Exception as e:
        return JsonResponse({'success': False, 'error': str(e)}) 
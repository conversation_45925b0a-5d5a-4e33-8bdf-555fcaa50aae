{% extends 'base1.html' %}
{% load static %}

{% block content %}
<div class="app-container">
    <h1 class="page-title">Unbind Not MRB Management</h1>

    <form method="post">
        {% csrf_token %}
        <div class="form-section">
            <div class="form-layout">
                <!-- 左侧区域 -->
                <div class="form-column">
                    <div class="form-group">
                        <label for="snList">SN List</label>
                        <div class="input-area">
                            <textarea id="snList" name="snList" 
                                    placeholder="Enter serial numbers here..."
                                    class="full-height-textarea">{{ sn_list }}</textarea>
                            <small class="form-text">Enter one serial number per line</small>
                        </div>
                    </div>
                </div>

                <!-- 右侧区域 -->
                <div class="form-column">
                    <div class="input-section">
                        <div class="form-group">
                            <label for="SNC">SNC</label>
                            <div class="input-area">
                                <input type="text" id="SNC" name="SNC" 
                                       value="{{ snc }}"
                                       placeholder="Enter SNC...">
                            </div>
                        </div>

                        <div class="form-group">
                            <label for="Remark">Remark</label>
                            <div class="input-area">
                                <input type="text" id="Remark" name="Remark" 
                                       value="{{ remark }}"
                                       placeholder="Enter remark...">
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="action-buttons">
            <button type="submit" name="query" class="btn btn-primary">Query</button>
            <button type="submit" name="insert" class="btn btn-success">Insert</button>
        </div>
    </form>

    {% if messages %}
        <div class="messages">
            {% for message in messages %}
                <div class="alert alert-{{ message.tags }}" role="alert">
                    {{ message }}
                </div>
            {% endfor %}
        </div>
    {% endif %}

    {% if results %}
        <div class="results-section">
            <h2 class="results-title">Search Results</h2>
            <div class="results-table">
                <table class="table" id="resultsTable">
                    <thead>
                        <tr>
                            <th>ID</th>
                            <th>Unit ID</th>
                            <th>Serial Number</th>
                            <th>SNC Number</th>
                            <th>Creation Time</th>
                            <th>Last Update</th>
                            <th>Remark</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for result in results %}
                            <tr>
                                <td>{{ result.id }}</td>
                                <td>{{ result.unit_id }}</td>
                                <td>{{ result.serial_number }}</td>
                                <td>{{ result.snc_number }}</td>
                                <td>{{ result.creation_time|date:"Y-m-d H:i:s" }}</td>
                                <td>{{ result.last_update|date:"Y-m-d H:i:s"|default_if_none:"-" }}</td>
                                <td>{{ result.remark }}</td>
                            </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
        </div>
    {% endif %}
</div>

{% endblock %}

{% block extra_js %}
<script>
$(document).ready(function() {
    // 初始化DataTable，添加更多配置选项来解决警告
    if ($('#resultsTable').length) {
        $('#resultsTable').DataTable({
            "order": [[ 0, "desc" ]],
            "pageLength": 25,
            "lengthMenu": [[10, 25, 50, -1], [10, 25, 50, "All"]],
            "dom": '<"top"fl>rt<"bottom"ip><"clear">',
            "language": {
                "search": "Filter:",
                "lengthMenu": "Show _MENU_ entries"
            },
            // 添加以下配置来解决排序警告
            "ordering": true,
            "deferRender": true,
            "processing": true,
            "serverSide": false,  // 如果是服务器端分页则设为 true
            "stateSave": false,
            // 优化排序性能
            "orderCellsTop": true,
            "fixedHeader": true,
            
            "initComplete": function() {
                // 调整DataTable的样式以匹配苹果风格
                $('.dataTables_wrapper .dataTables_length select').addClass('input-area');
                $('.dataTables_wrapper .dataTables_filter input').addClass('input-area');
                
                // 添加自定义样式
                $('<style>')
                    .prop('type', 'text/css')
                    .html(`
                        .dataTables_wrapper .dataTables_length select,
                        .dataTables_wrapper .dataTables_filter input {
                            border-radius: 8px;
                            border: 1px solid #d2d2d7;
                            padding: 0.5rem;
                            margin: 0 0.5rem;
                        }
                        .dataTables_wrapper .dataTables_paginate .paginate_button {
                            border-radius: 20px;
                            border: none;
                            padding: 0.5rem 1rem;
                            margin: 0 0.25rem;
                        }
                        .dataTables_wrapper .dataTables_paginate .paginate_button.current {
                            background: #0066cc;
                            color: white !important;
                        }
                        .dataTables_wrapper .dataTables_paginate .paginate_button:hover {
                            background: #f5f5f7;
                            border: none;
                        }
                        /* 添加加载状态样式 */
                        .dataTables_processing {
                            position: absolute;
                            top: 50%;
                            left: 50%;
                            width: 200px;
                            margin-left: -100px;
                            text-align: center;
                            padding: 1em 0;
                            background: rgba(255, 255, 255, 0.9);
                            border-radius: 8px;
                            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
                        }
                    `)
                    .appendTo('head');
            }
        });
    }
});
</script>
{% endblock %}

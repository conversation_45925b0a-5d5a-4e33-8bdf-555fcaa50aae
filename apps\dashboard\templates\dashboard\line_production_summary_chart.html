{% load static %}
<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>{{ page_title }}</title>
    <script src="{% static 'js/echarts.min.js' %}"></script>
    <style>
        body {
            font-family: 'Microsoft YaHei', <PERSON>l, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #121212;
            color: #e0e0e0;
        }
        .container {
            background-color: #1e1e1e;
            padding: 25px;
            border-radius: 12px;
            box-shadow: 0 4px 15px rgba(0,0,0,0.5);
        }
        h1 {
            color: #ffffff;
            text-align: center;
            font-weight: 600;
            margin-bottom: 30px;
            text-shadow: 0 2px 4px rgba(0,0,0,0.3);
        }
        .chart-container {
            width: 100%;
            height: 600px;
            margin-top: 20px;
        }
        .error-message {
            color: #ff6b6b;
            text-align: center;
            padding: 15px;
            border: 1px solid #ff6b6b;
            background-color: rgba(255, 107, 107, 0.1);
            border-radius: 8px;
            margin-bottom: 20px;
        }
        .no-data {
            text-align: center;
            padding: 30px;
            color: #a0a0a0;
            font-style: italic;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>Hermes BB Plan VS Actual By Line</h1>

        {% if error_message %}
            <div class="error-message">
                <p>错误：{{ error_message }}</p>
            </div>
        {% endif %}

        {% comment %} 图表 1: Hermes Holder BB {% endcomment %}
        {% if holder_categories and not error_message %}
            <h2>{{ holder_page_title|default:"Hermes Holder BB 生产数据" }}</h2>
            {{ holder_categories|json_script:"holder_categories_data" }}
            {{ holder_planned_data|json_script:"holder_planned_data_data" }}
            {{ holder_input_data|json_script:"holder_input_data_data" }}
            <div id="chartHolder" class="chart-container"></div>
        {% elif not error_message %}
            <p class="no-data">没有 Hermes Holder BB 的数据。</p>
        {% endif %}

        {% comment %} 图表 2: Hermes Charger BB {% endcomment %}
        {% if charger_categories and not error_message %}
            <h2>{{ charger_page_title|default:"Hermes Charger BB 生产数据" }}</h2>
            {{ charger_categories|json_script:"charger_categories_data" }}
            {{ charger_planned_data|json_script:"charger_planned_data_data" }}
            {{ charger_input_data|json_script:"charger_input_data_data" }}
            <div id="chartCharger" class="chart-container"></div>
        {% elif not error_message %}
            <p class="no-data">没有 Hermes Charger BB 的数据。</p>
        {% endif %}

        {% if holder_categories or charger_categories and not error_message %}
            <script type="text/javascript">
            function renderProductionChart(chartId, categoriesDataId, plannedDataId, inputDataId) {
                var chartElement = document.getElementById(chartId);
                if (!chartElement) {
                    console.error("Chart element not found:", chartId);
                    return;
                }
                // 初始化图表
                var myChart = echarts.init(chartElement, 'dark');

                // 获取数据
                var categoriesElem = document.getElementById(categoriesDataId);
                var plannedDataElem = document.getElementById(plannedDataId);
                var inputDataElem = document.getElementById(inputDataId);

                if (!categoriesElem || !plannedDataElem || !inputDataElem) {
                    console.error("Data elements not found for chart:", chartId);
                    chartElement.innerHTML = '<p class="no-data">图表数据加载失败。</p>';
                    return;
                }

                var categories = JSON.parse(categoriesElem.textContent);
                var plannedData = JSON.parse(plannedDataElem.textContent);
                var inputData = JSON.parse(inputDataElem.textContent);

                if (!categories || categories.length === 0) {
                     chartElement.innerHTML = '<p class="no-data">该图表没有可供显示的数据。</p>';
                     return;
                }

                // 计算显示值
                var s_plan_display = [];
                var s_actual_increment_display = [];
                var s_positive_gap_display = [];
                var s_negative_gap_display = [];
                
                var actual_values_for_label = [];
                var plan_values_for_label = [];
                var gap_values_for_label = [];

                for (var i = 0; i < categories.length; i++) {
                    var plan = plannedData[i];
                    var actual = inputData[i];
                    var gap = actual - plan;

                    plan_values_for_label.push(plan);
                    actual_values_for_label.push(actual);
                    gap_values_for_label.push(gap);

                    if (gap >= 0) {
                        s_plan_display.push(plan); // 蓝色柱代表 plan
                        s_actual_increment_display.push(gap); // 青色柱代表 actual - plan (increment)
                        s_positive_gap_display.push(gap); // 绿色柱代表 gap，恢复显示
                        s_negative_gap_display.push(0);
                    } else { // gap < 0
                        s_plan_display.push(actual); // 蓝色柱子高度为 actual
                        s_actual_increment_display.push(0); // 没有青色超出部分
                        s_positive_gap_display.push(0); // 没有正差异
                        s_negative_gap_display.push(gap); // 负差异，从 0 向下
                    }
                }
                // Calculate Y-axis max value
                var max_positive_stack_height = 0;
                for (var k = 0; k < categories.length; k++) {
                    var current_positive_stack = (s_plan_display[k] || 0) +
                                                 (s_actual_increment_display[k] || 0) +
                                                 (s_positive_gap_display[k] || 0);
                    if (current_positive_stack > max_positive_stack_height) {
                        max_positive_stack_height = current_positive_stack;
                    }
                }

                var max_original_plan = 0;
                if (plannedData && plannedData.length > 0) {
                    const numericPlannedData = plannedData.map(Number).filter(n => !isNaN(n));
                    if (numericPlannedData.length > 0) {
                        max_original_plan = Math.max(...numericPlannedData);
                    }
                }
                
                var y_axis_dynamic_max = Math.max(max_original_plan, max_positive_stack_height);
                
                if (y_axis_dynamic_max === 0) {
                    y_axis_dynamic_max = 100; // Default max if all data is zero
                } else {
                    // Add a small buffer (e.g., 5%) and round up to a nice number (e.g., nearest 50)
                    y_axis_dynamic_max = Math.ceil(y_axis_dynamic_max * 1.05 / 50) * 50;
                }

                // 深色主题配色方案
                var colors = {
                    plan: '#2563eb',       // 蓝色
                    actual: '#06b6d4',     // 青色
                    positive: '#22c55e',   // 绿色
                    negative: '#ef4444'    // 红色
                };

                // 图表配置
                var option = {
                    backgroundColor: '#1e1e1e',
                    tooltip: {
                        trigger: 'axis',
                        axisPointer: {
                            type: 'shadow',
                            shadowStyle: {
                                color: 'rgba(255, 255, 255, 0.1)'
                            }
                        },
                        formatter: function (params) {
                            var categoryName = params[0].name;
                            var categoryIndex = categories.indexOf(categoryName);
                            var originalPlan = 0;
                            var originalActual = 0;
                            if (categoryIndex !== -1) {
                                originalPlan = plannedData[categoryIndex];
                                originalActual = inputData[categoryIndex];
                            }
                            var originalGap = originalActual - originalPlan;

                            var tooltipText = '<div style="font-weight:bold;font-size:14px;margin-bottom:8px;">' + categoryName + '</div>';
                            tooltipText += '<div style="display:flex;justify-content:space-between;margin:5px 0;"><span>计划量:</span><span style="font-weight:bold;">' + originalPlan + '</span></div>';
                            tooltipText += '<div style="display:flex;justify-content:space-between;margin:5px 0;"><span>实际产出量:</span><span style="font-weight:bold;">' + originalActual + '</span></div>';
                            tooltipText += '<div style="display:flex;justify-content:space-between;margin:5px 0;"><span>差异:</span><span style="font-weight:bold;color:' + (originalGap >= 0 ? colors.positive : colors.negative) + ';">' + originalGap + '</span></div>';
                            return tooltipText;
                        },
                        backgroundColor: 'rgba(30, 30, 30, 0.9)',
                        borderColor: '#333',
                        textStyle: {
                            color: '#e0e0e0'
                        },
                        extraCssText: 'box-shadow: 0 0 10px rgba(0, 0, 0, 0.3);'
                    },
                    legend: {
                        data: ['Plan', 'Actual', 'Gap', '-Gap'],
                        textStyle: {
                            color: '#e0e0e0'
                        },
                        itemGap: 20,
                        itemWidth: 18,
                        itemHeight: 12,
                        top: 5
                    },
                    grid: {
                        left: '5%',
                        right: '5%',
                        bottom: '8%',
                        top: '15%',
                        containLabel: true
                    },
                    xAxis: [{
                        type: 'category',
                        data: categories,
                        axisTick: {
                            alignWithLabel: true,
                            lineStyle: {
                                color: 'rgba(255, 255, 255, 0.2)'
                            }
                        },
                        axisLine: {
                            lineStyle: {
                                color: 'rgba(255, 255, 255, 0.2)'
                            }
                        },
                        axisLabel: {
                            color: '#e0e0e0',
                            interval: 0,
                            rotate: 30,
                            margin: 16,
                            fontSize: 12
                        }
                    }],
                    yAxis: [{
                        type: 'value',
                        name: 'Qty',
                        max: y_axis_dynamic_max,
                        nameTextStyle: {
                            color: '#e0e0e0',
                            fontSize: 14,
                            padding: [0, 0, 0, 30]
                        },
                        splitLine: {
                            lineStyle: {
                                color: 'rgba(255, 255, 255, 0.1)',
                                type: 'dashed'
                            }
                        },
                        axisLine: {
                            lineStyle: {
                                color: 'rgba(255, 255, 255, 0.2)'
                            }
                        },
                        axisLabel: {
                            color: '#e0e0e0'
                        }
                    }],
                    series: [
                        {
                            name: 'Plan',
                            type: 'bar',
                            stack: 'summary',
                            itemStyle: {
                                color: colors.plan,
                                borderRadius: [4, 4, 0, 0]
                            },
                            label: {
                                show: true,
                                position: 'inside',
                                formatter: function(params) {
                                    var idx = params.dataIndex;
                                    var originalPlan = plan_values_for_label[idx];
                                    var originalActual = actual_values_for_label[idx];
                                    var originalGap = gap_values_for_label[idx];

                                    if (originalGap >= 0) { // 蓝色柱代表 plan, params.value 是 plan
                                        return originalPlan > 0 ? 'Plan: ' + originalPlan : '';
                                    } else { // 蓝色柱代表 actual, params.value 是 actual
                                        // 如果 actual 非零，或者 actual 为零但 plan 非零，则显示
                                        if (originalActual !== 0 || originalPlan !== 0) {
                                             return 'Actual: ' + originalActual + '\n(Plan: ' + originalPlan + ')';
                                        }
                                        return '';
                                    }
                                },
                                textStyle: {
                                    color: '#ffffff',
                                    fontSize: 12,
                                    fontWeight: 'bold'
                                }
                            },
                            data: s_plan_display,
                            emphasis: {
                                itemStyle: {
                                    color: '#3b82f6'
                                }
                            },
                            barWidth: '60%'
                        },
                        {
                            name: 'Actual',
                            type: 'bar',
                            stack: 'summary',
                            itemStyle: {
                                color: colors.actual,
                                borderRadius: [0, 0, 0, 0] // 中间堆叠条，无圆角
                            },
                            label: {
                                show: true,
                                position: 'inside',
                                formatter: function(params) {
                                    var idx = params.dataIndex;
                                    var originalActual = actual_values_for_label[idx];
                                    var originalGap = gap_values_for_label[idx];
                                    
                                    // params.value 是 increment (gap) 当 originalGap >= 0
                                    if (originalGap > 0 && params.value > 0) {
                                        // 显示总的 Actual 值
                                        return 'Actual: ' + originalActual;
                                    }
                                    return '';
                                },
                                textStyle: {
                                    color: '#ffffff',
                                    fontSize: 12,
                                    fontWeight: 'bold'
                                }
                            },
                            data: s_actual_increment_display,
                            emphasis: {
                                itemStyle: {
                                    color: '#0891b2'
                                }
                            }
                        },
                        {
                            name: 'Gap',
                            type: 'bar',
                            stack: 'summary',
                            itemStyle: {
                                color: colors.positive,
                                borderRadius: [4, 4, 0, 0] // 如果显示，应为顶部圆角 (但现在数据为 0)
                            },
                            label: {
                                show: true, // 恢复显示标签
                                position: 'top',
                                formatter: function(params) {
                                    var idx = params.dataIndex;
                                    var originalGap = gap_values_for_label[idx];
                                    // params.value 是 s_positive_gap_display[idx]，也就是 originalGap
                                    if (originalGap > 0 && params.value > 0) {
                                        return 'Gap:' + originalGap;
                                    }
                                    return '';
                                },
                                textStyle: {
                                    color: '#ffffff',
                                    fontSize: 12,
                                    fontWeight: 'bold',
                                    textBorderColor: '#1e1e1e',
                                    textBorderWidth: 2
                                },
                                distance: 5
                            },
                            data: s_positive_gap_display,
                            emphasis: {
                                itemStyle: {
                                    color: '#16a34a'
                                }
                            }
                        },
                        {
                            name: '-Gap',
                            type: 'bar',
                            stack: 'summary',
                            itemStyle: {
                                color: colors.negative,
                                borderRadius: [0, 0, 4, 4]
                            },
                            label: {
                                show: true,
                                position: 'bottom',
                                formatter: function(params) {
                                    var idx = params.dataIndex;
                                    var gapVal = gap_values_for_label[idx];
                                    // 当差异为负时，只显示差异值
                                    if (gapVal < 0) {
                                        return params.value !== 0 ? '-Gap:' + gapVal : '';
                                    }
                                    return '';
                                },
                                textStyle: {
                                    color: '#ffffff',
                                    fontSize: 12,
                                    fontWeight: 'bold',
                                    textBorderColor: '#1e1e1e',
                                    textBorderWidth: 2
                                },
                                distance: 5
                            },
                            data: s_negative_gap_display,
                            emphasis: {
                                itemStyle: {
                                    color: '#dc2626'
                                }
                            }
                        }
                    ],
                    animation: true,
                    animationDuration: 1500,
                    animationEasing: 'elasticOut'
                };

                myChart.setOption(option);
                
                // 图表大小自适应
                window.addEventListener('resize', function() {
                    myChart.resize();
                });
            }

            // 渲染 Holder 图表 (如果数据存在)
            if (document.getElementById('holder_categories_data')) {
                renderProductionChart('chartHolder', 'holder_categories_data', 'holder_planned_data_data', 'holder_input_data_data');
            }

            // 渲染 Charger 图表 (如果数据存在)
            if (document.getElementById('charger_categories_data')) {
                renderProductionChart('chartCharger', 'charger_categories_data', 'charger_planned_data_data', 'charger_input_data_data');
            }
        </script>
        {% endif %} {# Closes the if (holder_categories or charger_categories) and not error_message #}
    </div>
</body>
</html>
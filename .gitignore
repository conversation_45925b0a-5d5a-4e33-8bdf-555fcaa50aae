# Python缓存文件
__pycache__/
*.py[cod]
*$py.class

# 日志文件
*.log

# 本地数据库
db.sqlite3
db.sqlite3-journal

# IDE配置
.idea/
.vscode/
django_config/

# 环境文件
.env
.venv/
env/


# 系统文件
.DS_Store
Thumbs.db

# 静态文件目录
staticfiles/
static/admin/
static/simpleui-x/
static/rest_framework/
temp_excel/


# 编译和压缩文件
*.pyc
*.pyo
*.pyd
*.so
*.egg
*.egg-info/
dist/
build/
*.egg-info/

# 但保留必要的静态文件
!static/css/apple-style.css
!static/js/
!static/plugins/
!static/bootstrap/
!static/mycss/

# 缓存文件
.cache/
.pytest_cache/
.coverage
htmlcov/

# 数据库迁移文件
**/migrations/*.py
!**/migrations/__init__.py

# 本地配置文件
local_settings.py
django_cache
temp_excel

.idea/
.aide/
.aider.tags.cache.v4
.aider.input.history
bash.exe.stackdump

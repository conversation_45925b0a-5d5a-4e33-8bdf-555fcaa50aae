/*!
 AutoFill 1.2.1
 ©2008-2014 SpryMedia Ltd - datatables.net/license
*/
(function(o,j,m){var l=function(c,k){var h=function(d,b){if(!(this instanceof h))throw"Warning: AutoFill must be initialised with the keyword 'new'";if(!c.fn.dataTableExt.fnVersionCheck("1.7.0"))throw"Warning: AutoFill requires DataTables 1.7 or greater";this.c={};this.s={filler:{height:0,width:0},border:{width:2},drag:{startX:-1,startY:-1,startTd:null,endTd:null,dragging:!1},screen:{interval:null,y:0,height:0,scrollTop:0},scroller:{top:0,bottom:0},columns:[]};this.dom={table:null,filler:null,borderTop:null,
borderRight:null,borderBottom:null,borderLeft:null,currentTarget:null};this.fnSettings=function(){return this.s};this._fnInit(d,b);return this};h.prototype={_fnInit:function(d,b){var a=this;this.s.dt=k.Api?(new k.Api(d)).settings()[0]:d.fnSettings();this.s.init=b||{};this.dom.table=this.s.dt.nTable;c.extend(!0,this.c,h.defaults,b);this._initColumns();var e=c("<div/>",{"class":"AutoFill_filler"}).appendTo("body");this.dom.filler=e[0];this.s.filler.height=e.height();this.s.filler.width=e.width();e[0].style.display=
"none";var g,f=j.body;""!==a.s.dt.oScroll.sY&&(a.s.dt.nTable.parentNode.style.position="relative",f=a.s.dt.nTable.parentNode);g=c("<div/>",{"class":"AutoFill_border"});this.dom.borderTop=g.clone().appendTo(f)[0];this.dom.borderRight=g.clone().appendTo(f)[0];this.dom.borderBottom=g.clone().appendTo(f)[0];this.dom.borderLeft=g.clone().appendTo(f)[0];e.on("mousedown.DTAF",function(b){this.onselectstart=function(){return false};a._fnFillerDragStart.call(a,b);return false});c("tbody",this.dom.table).on("mouseover.DTAF mouseout.DTAF",
">tr>td, >tr>th",function(b){a._fnFillerDisplay.call(a,b)});c(this.dom.table).on("destroy.dt.DTAF",function(){e.off("mousedown.DTAF").remove();c("tbody",this.dom.table).off("mouseover.DTAF mouseout.DTAF")})},_initColumns:function(){var d=this,b,a,e=this.s.dt,g=this.s.init;b=0;for(a=e.aoColumns.length;b<a;b++)this.s.columns[b]=c.extend(!0,{},h.defaults.column);e.oApi._fnApplyColumnDefs(e,g.aoColumnDefs||g.columnDefs,g.aoColumns||g.columns,function(a,b){d._fnColumnOptions(a,b)});b=0;for(a=e.aoColumns.length;b<
a;b++)if(e=this.s.columns[b],e.read||(e.read=this._fnReadCell),e.write||(e.read=this._fnWriteCell),!e.step)e.read=this._fnStep},_fnColumnOptions:function(d,b){var a=this.s.columns[d],c=function(c,d){b[d[0]]!==m&&(a[c]=b[d[0]]);b[d[1]]!==m&&(a[c]=b[d[1]])};c("enable",["bEnable","enable"]);c("read",["fnRead","read"]);c("write",["fnWrite","write"]);c("step",["fnStep","step"]);c("increment",["bIncrement","increment"])},_fnTargetCoords:function(d){var b=c(d).parents("tr")[0],a=this.s.dt.oInstance.fnGetPosition(d);
return{x:c("td",b).index(d),y:c("tr",b.parentNode).index(b),row:a[0],column:a[2]}},_fnUpdateBorder:function(d,b){var a=this.s.border.width,e=c(d).offset(),g=c(b).offset(),f=e.left-a,i=g.left+c(b).outerWidth(),n=e.top-a,h=g.top+c(b).outerHeight(),j=g.left+c(b).outerWidth()-e.left+2*a,k=g.top+c(b).outerHeight()-e.top+2*a;e.left>g.left&&(f=g.left-a,i=e.left+c(d).outerWidth(),j=e.left+c(d).outerWidth()-g.left+2*a);""!==this.s.dt.oScroll.sY&&(a=c(this.s.dt.nTable.parentNode).offset(),e=c(this.s.dt.nTable.parentNode).scrollTop(),
g=c(this.s.dt.nTable.parentNode).scrollLeft(),f-=a.left-g,i-=a.left-g,n-=a.top-e,h-=a.top-e);a=this.dom.borderTop.style;a.top=n+"px";a.left=f+"px";a.height=this.s.border.width+"px";a.width=j+"px";a=this.dom.borderBottom.style;a.top=h+"px";a.left=f+"px";a.height=this.s.border.width+"px";a.width=j+"px";a=this.dom.borderLeft.style;a.top=n+"px";a.left=f+"px";a.height=k+"px";a.width=this.s.border.width+"px";a=this.dom.borderRight.style;a.top=n+"px";a.left=i+"px";a.height=k+"px";a.width=this.s.border.width+
"px"},_fnFillerDragStart:function(d){var b=this,a=this.dom.currentTarget;this.s.drag.dragging=!0;b.dom.borderTop.style.display="block";b.dom.borderRight.style.display="block";b.dom.borderBottom.style.display="block";b.dom.borderLeft.style.display="block";var e=this._fnTargetCoords(a);this.s.drag.startX=e.x;this.s.drag.startY=e.y;this.s.drag.startTd=a;this.s.drag.endTd=a;this._fnUpdateBorder(a,a);c(j).bind("mousemove.AutoFill",function(a){b._fnFillerDragMove.call(b,a)});c(j).bind("mouseup.AutoFill",
function(a){b._fnFillerFinish.call(b,a)});this.s.screen.y=d.pageY;this.s.screen.height=c(o).height();this.s.screen.scrollTop=c(j).scrollTop();""!==this.s.dt.oScroll.sY&&(this.s.scroller.top=c(this.s.dt.nTable.parentNode).offset().top,this.s.scroller.bottom=this.s.scroller.top+c(this.s.dt.nTable.parentNode).height());this.s.screen.interval=setInterval(function(){var a=c(j).scrollTop();b.s.screen.y=b.s.screen.y+(a-b.s.screen.scrollTop);b.s.screen.height-b.s.screen.y+a<50?c("html, body").animate({scrollTop:a+
50},240,"linear"):b.s.screen.y-a<50&&c("html, body").animate({scrollTop:a-50},240,"linear");b.s.dt.oScroll.sY!==""&&(b.s.screen.y>b.s.scroller.bottom-50?c(b.s.dt.nTable.parentNode).animate({scrollTop:c(b.s.dt.nTable.parentNode).scrollTop()+50},240,"linear"):b.s.screen.y<b.s.scroller.top+50&&c(b.s.dt.nTable.parentNode).animate({scrollTop:c(b.s.dt.nTable.parentNode).scrollTop()-50},240,"linear"))},250)},_fnFillerDragMove:function(d){if(d.target&&"TD"==d.target.nodeName.toUpperCase()&&d.target!=this.s.drag.endTd){var b=
this._fnTargetCoords(d.target);"y"==this.c.mode&&b.x!=this.s.drag.startX&&(d.target=c("tbody>tr:eq("+b.y+")>td:eq("+this.s.drag.startX+")",this.dom.table)[0]);"x"==this.c.mode&&b.y!=this.s.drag.startY&&(d.target=c("tbody>tr:eq("+this.s.drag.startY+")>td:eq("+b.x+")",this.dom.table)[0]);"either"==this.c.mode&&(b.x!=this.s.drag.startX?d.target=c("tbody>tr:eq("+this.s.drag.startY+")>td:eq("+b.x+")",this.dom.table)[0]:b.y!=this.s.drag.startY&&(d.target=c("tbody>tr:eq("+b.y+")>td:eq("+this.s.drag.startX+
")",this.dom.table)[0]));"both"!==this.c.mode&&(b=this._fnTargetCoords(d.target));var a=this.s.drag;a.endTd=d.target;b.y>=this.s.drag.startY?this._fnUpdateBorder(a.startTd,a.endTd):this._fnUpdateBorder(a.endTd,a.startTd);this._fnFillerPosition(d.target)}this.s.screen.y=d.pageY;this.s.screen.scrollTop=c(j).scrollTop();""!==this.s.dt.oScroll.sY&&(this.s.scroller.scrollTop=c(this.s.dt.nTable.parentNode).scrollTop(),this.s.scroller.top=c(this.s.dt.nTable.parentNode).offset().top,this.s.scroller.bottom=
this.s.scroller.top+c(this.s.dt.nTable.parentNode).height())},_fnFillerFinish:function(){var d=this,b,a;c(j).unbind("mousemove.AutoFill mouseup.AutoFill");this.dom.borderTop.style.display="none";this.dom.borderRight.style.display="none";this.dom.borderBottom.style.display="none";this.dom.borderLeft.style.display="none";this.s.drag.dragging=!1;clearInterval(this.s.screen.interval);var e=[],g=this.dom.table,f=this._fnTargetCoords(this.s.drag.startTd),i=this._fnTargetCoords(this.s.drag.endTd),h=function(a){return d.s.dt.oApi._fnVisibleToColumnIndex(d.s.dt,
a)};if(f.y<=i.y)for(b=f.y;b<=i.y;b++)if(f.x<=i.x)for(a=f.x;a<=i.x;a++)e.push({node:c("tbody>tr:eq("+b+")>td:eq("+a+")",g)[0],x:a-f.x,y:b-f.y,colIdx:h(a)});else for(a=f.x;a>=i.x;a--)e.push({node:c("tbody>tr:eq("+b+")>td:eq("+a+")",g)[0],x:a-f.x,y:b-f.y,colIdx:h(a)});else for(b=f.y;b>=i.y;b--)if(f.x<=i.x)for(a=f.x;a<=i.x;a++)e.push({node:c("tbody>tr:eq("+b+")>td:eq("+a+")",g)[0],x:a-f.x,y:b-f.y,colIdx:h(a)});else for(a=f.x;a>=i.x;a--)e.push({node:c("tbody>tr:eq("+b+")>td:eq("+a+")",g)[0],x:f.x-a,y:f.y-
b,colIdx:h(a)});if(!(1>=e.length)){var g=[],m;b=0;for(a=e.length;b<a;b++){var f=e[b],i=this.s.columns[f.colIdx],h=i.read.call(i,f.node),l=i.step.call(i,f.node,h,m,b,f.x,f.y);i.write.call(i,f.node,l);m=l;g.push({cell:f,colIdx:f.colIdx,newValue:l,oldValue:h})}null!==this.c.complete&&this.c.complete.call(this,g);k.Api?(new k.Api(this.s.dt)).draw(!1):this.s.dt.oInstance.fnDraw()}},_fnFillerDisplay:function(d){var b=this.dom.filler;if(!this.s.drag.dragging){var a="td"==d.target.nodeName.toLowerCase()?
d.target:c(d.target).parents("td")[0],e=this._fnTargetCoords(a).column;if(this.s.columns[e].enable)if("mouseover"==d.type)this.dom.currentTarget=a,this._fnFillerPosition(a),b.style.display="block";else{if(!d.relatedTarget||!d.relatedTarget.className.match(/AutoFill/))b.style.display="none"}else b.style.display="none"}},_fnFillerPosition:function(d){var b=c(d).offset(),a=this.dom.filler;a.style.top=b.top-this.s.filler.height/2-1+c(d).outerHeight()+"px";a.style.left=b.left-this.s.filler.width/2-1+c(d).outerWidth()+
"px"}};k.AutoFill=h;k.AutoFill=h;h.version="1.2.1";h.defaults={mode:"y",complete:null,column:{enable:!0,increment:!0,read:function(d){return c(d).html()},write:function(d,b){var a=c(d).parents("table");if(k.Api)a.DataTable().cell(d).data(b);else{var a=a.dataTable(),e=a.fnGetPosition(d);a.fnUpdate(b,e[0],e[2],!1)}},step:function(c,b,a,e,g,f){c=/(\-?\d+)/;return(e=this.increment&&a?a.match(c):null)?a.replace(c,parseInt(e[1],10)+(0>g||0>f?-1:1)):a===m?b:a}}};return h};"function"===typeof define&&define.amd?
define(["jquery","datatables"],l):"object"===typeof exports?l(require("jquery"),require("datatables")):jQuery&&!jQuery.fn.dataTable.AutoFill&&l(jQuery,jQuery.fn.dataTable)})(window,document);



import os

os.environ.setdefault("DJANGO_SETTINGS_MODULE", "myproject.settings")

import django

django.setup()

from django.db import connections


def custom_sql_query():
    sql ="""
      SELECT sn.Value,ud.lastupdate,ud.remark,ud.status,ud.isEngravingAssy,
            ud.isHoldCarton,ud.isHoldQAapproved,
            ud.retcode FROM ffSerialNumber(nolock) sn
            JOIN udtOnHoldDevice(nolock) ud ON ud.unitid=sn.UnitID
            WHERE sn.Value IN ('VH1752019040',
            'VH1752019042',
            'VH1752019044',
            'VH1752019045') AND ud.status=1
    """





    with connections['FFTestDB'].cursor() as cursor:
        # Use your SQL query...

        cursor.execute(sql)
        rows = cursor.fetchall()

    return rows

result = custom_sql_query()
print(result)
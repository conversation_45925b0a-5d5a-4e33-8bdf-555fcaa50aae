/* 通用容器样式 */
.app-container {
    max-width: 1200px;
    margin: 2rem auto;
    padding: 2rem;
    background: #ffffff;
    border-radius: 20px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.05);
}

/* 页面标题 */
.page-title {
    font-size: 2rem;
    font-weight: 500;
    color: #1d1d1f;
    margin-bottom: 2rem;
    text-align: center;
}

/* 表单区域 */
.form-section {
    background: #f5f5f7;
    border-radius: 16px;
    padding: 2rem;
    margin-bottom: 2rem;
}

/* 输入组 */
.input-group {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 1.5rem;
}

/* 表单组 */
.form-group {
    margin-bottom: 1.5rem;
}

/* 输入区域 */
.input-area {
    background: #ffffff;
    border-radius: 12px;
    border: 1px solid #d2d2d7;
    padding: 1rem;
    transition: all 0.3s ease;
}

.input-area:focus-within {
    border-color: #0066cc;
    box-shadow: 0 0 0 4px rgba(0,102,204,0.1);
}

/* 复选框组 */
.checkbox-group {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1rem;
    margin: 1.5rem 0;
}

.custom-checkbox {
    display: flex;
    align-items: center;
    padding: 0.75rem;
    margin-bottom: 0.5rem;
    border-radius: 8px;
    transition: all 0.2s ease;
    background: #ffffff;
    border: 1px solid #d2d2d7;
}

.custom-checkbox:hover {
    background: #f5f5f7;
}

.custom-checkbox input[type="checkbox"] {
    margin-right: 0.75rem;
}

/* 按钮样式 */
.action-buttons {
    display: flex;
    gap: 1rem;
    justify-content: center;
    margin: 2rem 0;
    position: relative;
    z-index: 1001;
}

.btn {
    padding: 0.75rem 1.5rem;
    border-radius: 20px;
    font-weight: 500;
    transition: all 0.2s ease;
    border: none;
    min-width: 120px;
}

.btn:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(0,0,0,0.15);
}

.btn-primary { background: #0066cc; }
.btn-secondary { background: #34c759; }
.btn-warning { background: #ff9f0a; }
.btn-info { background: #5856d6; }
.btn-danger { background: #ff3b30; }

/* 结果表格 */
.results-section {
    margin-top: 3rem;
}

.results-table {
    border-radius: 12px;
    overflow: hidden;
    box-shadow: 0 2px 10px rgba(0,0,0,0.05);
}

.table {
    margin-bottom: 0;
}

.table thead th {
    background: #f5f5f7;
    border-bottom: none;
    padding: 1rem;
    font-weight: 500;
    color: #1d1d1f;
}

.table tbody td {
    padding: 1rem;
    vertical-align: middle;
    border-top: 1px solid #d2d2d7;
}

/* 警告框 */
.alert {
    border-radius: 12px;
    margin-bottom: 1rem;
    padding: 1rem 1.5rem;
}

/* 表单文本 */
.form-text {
    color: #86868b;
    font-size: 0.875rem;
    margin-top: 0.5rem;
}

/* 标签 */
label {
    font-weight: 500;
    color: #1d1d1f;
    margin-bottom: 0.5rem;
    display: block;
}

/* 输入元素 */
textarea, input[type="text"] {
    width: 100%;
    border: none;
    resize: vertical;
    background: transparent;
}

textarea {
    min-height: 100px;
}

textarea:focus, input:focus {
    outline: none;
}

/* Select2 相关样式 */
.select2-container {
    width: 100% !important;
    z-index: 1000;
}

.select2-dropdown {
    border-radius: 12px;
    border: 1px solid #d2d2d7;
    box-shadow: 0 4px 12px rgba(0,0,0,0.1);
}

.select2-container--default .select2-selection--single {
    border: none;
    background: transparent;
    height: 38px;
}

.select2-container--default .select2-selection--single .select2-selection__rendered {
    line-height: 38px;
    padding-left: 0;
}

.select2-container--default .select2-search--dropdown .select2-search__field {
    border-radius: 6px;
    border: 1px solid #d2d2d7;
    padding: 6px 12px;
}

.select2-container--default .select2-results__option--highlighted[aria-selected] {
    background-color: #0066cc;
}

/* 表单布局 */
.form-layout {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 2rem;
    align-items: start;
}

.form-column {
    display: flex;
    flex-direction: column;
    gap: 1.5rem;
}

/* 全高文本框 */
.full-height-textarea {
    min-height: 300px !important;
}

/* 输入区域组 */
.input-section {
    display: flex;
    flex-direction: column;
    gap: 1rem;
    margin-bottom: 1.5rem;
}

/* 选项区域 */
.options-section {
    background: #ffffff;
    border-radius: 12px;
    padding: 1.5rem;
    border: 1px solid #d2d2d7;
}

.section-title {
    font-size: 1.1rem;
    font-weight: 500;
    color: #1d1d1f;
    margin-bottom: 1rem;
}

/* 复选框网格 */
.checkbox-grid {
    display: grid;
    gap: 0.75rem;
}

/* 结果表格容器 */
.results-container {
    margin-top: 2rem;
    background: #ffffff;
    border-radius: 12px;
    overflow: hidden;
    box-shadow: 0 2px 10px rgba(0,0,0,0.05);
}

/* 表格样式优化 */
.table-wrapper {
    overflow-x: auto;
    margin: 0 -1rem;
    padding: 0 1rem;
}

/* 响应式调整 */
@media (max-width: 992px) {
    .form-layout {
        grid-template-columns: 1fr;
    }

    .full-height-textarea {
        min-height: 200px !important;
    }
} 
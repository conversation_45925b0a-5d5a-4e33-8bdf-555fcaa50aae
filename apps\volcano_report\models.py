from django.db import models
from django.contrib.auth import get_user_model
from django.utils import timezone
from django.contrib.auth.models import User
from django.db.models import Sum, Case, When, Value, IntegerField, F, Func
from django.db.models.functions import Cast, Replace
from datetime import datetime, timedelta

class MyManager(models.Manager):
  def get_queryset(self):
    return super().get_queryset().using('VolcanoReporDB')
class UDTGetQueryCondition(models.Model):
    objects = MyManager()
    ID = models.IntegerField(primary_key=True)  # 注意大小写与数据库一致
    Reason = models.CharField(max_length=255)  # Adjust max_length according to your needs
    UDP = models.CharField(max_length=255)  # Adjust max_length according to your needs

    def __str__(self):
        return f"{self.ID} - {self.Reason}"

    class Meta:
        db_table = 'UDTGetQueryCondition'  # This is the name of your table
        managed = False  # If you want Django to not manage the table (e.g. no migrations)




class UDTGetQueryConditiondetail(models.Model):
    objects = MyManager()

    SNT_CHOICES = (
        (0, 'NO'),
        (1, 'Yes'),
    )
    ID = models.AutoField(primary_key=True)  # 注意大小写与数据库一致
    Reason = models.CharField(max_length=255)  # Adjust max_length according to your needs
    UDP = models.CharField(max_length=255)  # Adjust max_length according to your needs
    SNTextarea = models.IntegerField(choices=SNT_CHOICES,default=1)  # Adjust max_length according to your needitegerField()
    PartnumberTextarea = models.IntegerField(choices=SNT_CHOICES,default=1)  #
    Partfamily = models.IntegerField(choices=SNT_CHOICES,default=1)  #
    StationType = models.IntegerField(choices=SNT_CHOICES,default=1)  #
    LineName = models.IntegerField(choices=SNT_CHOICES,default=1)  #
    TimeInput = models.IntegerField(choices=SNT_CHOICES,default=1)  #
    MPN = models.IntegerField(choices=SNT_CHOICES,default=1)  #
    LotCode = models.IntegerField(choices=SNT_CHOICES,default=1)  #
    DateCode = models.IntegerField(choices=SNT_CHOICES,default=1)  #
    Supplier = models.IntegerField(choices=SNT_CHOICES,default=1)  #
    CreateTime = models.DateTimeField(auto_now=True)
    Remark = models.CharField(max_length=255)
    useCount = models.IntegerField(default=0)
    status = models.IntegerField(choices=SNT_CHOICES,default=1)

    def __str__(self):
        return f"{self.ID} - {self.Reason}"

    class Meta:
        db_table = 'udtGetQueryConditionDetail'  # This is the name of your table
        managed = False

    def increment_use_count(self, id):
        try:
            record = UDTGetQueryConditiondetail.objects.get(ID=id)
            record.useCount += 1
            record.save()
            return record.useCount
        except UDTGetQueryConditiondetail.DoesNotExist:
            return None



class rptReportSN(models.Model):
    ID = models.AutoField(primary_key=True)
    ReportID = models.BigIntegerField()
    Serialnumber = models.CharField(max_length=50)

    class Meta:
        db_table = 'rptReportSN'  # This is the name of your table
        managed = False


'''
ID
Name
Description
Status
'''
class luPartFamilyType(models.Model):
   ID = models.AutoField(primary_key=True)
   Name = models.CharField(max_length=50)
   Status  = models.IntegerField()
   Description = models.CharField(max_length=50)

   def __str__(self):
       return f" {self.Name}"

   class Meta:
       db_table = 'luPartFamilyType'  # This is the name of your table
       managed = False


class ffStationType(models.Model):
   ID = models.AutoField(primary_key=True)
   Description = models.CharField(max_length=50)

   def __str__(self):
       return f" {self.Description}"

   class Meta:
       db_table = 'ffStationType'  # This is the name of your table
       managed = False


class ffLine(models.Model):
   ID = models.AutoField(primary_key=True)
   Description = models.CharField(max_length=50)

   def __str__(self):
       return f" {self.Description}"

   class Meta:
       db_table = 'ffLine'  # This is the name of your table
       managed = False


class luPartFamily(models.Model):
    objects = MyManager()
    ID = models.AutoField(primary_key=True)
    Name = models.CharField(max_length=255)
    Status = models.IntegerField()
    
    class Meta:
        db_table = 'luPartFamily'
        managed = False

    def __str__(self):
        return self.Name

class FFPart(models.Model):
    objects = MyManager()
    ID = models.AutoField(primary_key=True)
    PartNumber = models.CharField(max_length=255)
    PartFamilyID = models.ForeignKey(luPartFamily, on_delete=models.DO_NOTHING, db_column='PartFamilyID', null=True, blank=True)
    Revision = models.CharField(max_length=255, null=True, blank=True)
    Description = models.TextField(null=True, blank=True)
    UOM = models.CharField(max_length=255, null=True, blank=True)
    IsUnit = models.SmallIntegerField(choices=[(0, 'No'), (1, 'Yes')], default=0)
    Status = models.SmallIntegerField(default=1, null=True, blank=True)

    class Meta:
        db_table = 'FFPart'
        managed = False

    def __str__(self):
        return self.PartNumber

#SMT李晓明的报表记录
class UdtSMTScrapRecord(models.Model):
    objects = MyManager()
    
    # 定义选项常量
    STATION_CHOICES = [
        ('MT1测试不良', 'MT1测试不良'),
        ('AOI不良', 'AOI不良'),
        ('CCD测试不良', 'CCD测试不良'),
        ('目检不良', '目检不良'),
        ('其它不良', '其它不良'),
        ('QC抽检', 'QC抽检'),
        ('PPS不良', 'PPS不良'),
    ]
    
    DEPARTMENT_CHOICES = [
        ('PE', 'PE'),
        ('EE', 'EE'),
        ('TE', 'TE'),
        ('MBU', 'MBU'),
        ('SQE', 'SQE'),
        ('QA', 'QA'),
    ]
    
    ID = models.AutoField(primary_key=True)
    date = models.DateField(verbose_name="日期")
    serialnumber = models.CharField(verbose_name="序列号", max_length=60)
    Partnumber = models.CharField(max_length=200, verbose_name="型号")
    reporting_unit_code = models.CharField(max_length=200, verbose_name="数量")
    PONumber = models.CharField(max_length=200, verbose_name="工单")
    reporting_reason = models.TextField(blank=True, null=True, verbose_name="报废原因")
    recorder = models.CharField(max_length=20, verbose_name="登记人")
    createtime = models.DateTimeField(auto_now_add=True, verbose_name="创建时间")
    creator = models.CharField(max_length=150, blank=True, null=True, verbose_name="创建人")
    
    # 新增字段
    Station = models.CharField(
        max_length=255, 
        choices=STATION_CHOICES,
        verbose_name="工位",
        null=True
    )
    # FailDescription = models.TextField(
    #     verbose_name="故障描述",
    #     null=True
    # )
    ResponsibleDepartment = models.CharField(
        max_length=255,
        choices=DEPARTMENT_CHOICES,
        verbose_name="责任部门",
        null=True
    )

    class Meta:
        db_table = 'udtSMTScrapRecord'
        managed = False
        verbose_name = "SMT报废记录"
        verbose_name_plural = "SMT报废记录"

    def __str__(self):
        return f"{self.serialnumber} - {self.Partnumber} - {self.date}"

    def save(self, *args, **kwargs):
        if not self.creator and hasattr(self, '_current_user'):
            self.creator = self._current_user.username if self._current_user else None
        super().save(*args, **kwargs)

class RdtProductionPlan(models.Model):
    objects = MyManager()
    
    SHIFT_CHOICES = (
        ('Day', '白班'),
        ('Night', '夜班'),
    )
    
    ID = models.AutoField(primary_key=True)
    Project = models.CharField(max_length=255, null=True)
    Partnumber = models.CharField(max_length=255)
    LineName = models.CharField(max_length=255)
    Date = models.DateField()
    Shift = models.CharField(max_length=255, choices=[('Day', '白班'), ('Night', '夜班')])
    UPH = models.IntegerField(null=True, blank=True)
    PlanQty = models.IntegerField()
    CreateTime = models.DateTimeField(auto_now_add=True, null=True)
    LastUpdateTime = models.DateTimeField(auto_now=True, null=True)
    CreateBy = models.CharField(max_length=255, null=True)
    UpdateBy = models.CharField(max_length=255, null=True)

    class Meta:
        db_table = 'rdtProductionPlan_rw'
        managed = False
        verbose_name = '生产计划'
        verbose_name_plural = verbose_name
        unique_together = ['Partnumber', 'Date', 'LineName', 'Shift']  # 添加联合唯一约束

    def save(self, *args, **kwargs):
        # 检查是否已存在相同的记录
        exists = RdtProductionPlan.objects.filter(
            Partnumber=self.Partnumber,
            Date=self.Date,
            LineName=self.LineName,
            Shift=self.Shift
        ).exclude(ID=self.ID).exists()
        
        if exists:
            raise ValueError(f'该型号({self.Partnumber})在{self.Date}的{self.LineName}{self.get_Shift_display()}已有计划')

        # 获取项目名称
        try:
            ffpart = FFPart.objects.get(
                PartNumber=self.Partnumber,
                Status=1,
                IsUnit=1,
                PartFamilyID__isnull=False
            )
            if ffpart.PartFamilyID:
                self.Project = ffpart.PartFamilyID.Name
        except FFPart.DoesNotExist:
            pass
            
        super().save(*args, **kwargs)

    def __str__(self):
        return f"{self.Project} - {self.Partnumber}"

class udtLineOutputConfirmation(models.Model):
    objects = MyManager()  # 使用 VolcanoReporDB 数据库
    
    ID = models.AutoField(primary_key=True)
    Line = models.CharField(max_length=100, verbose_name='线别')
    Date = models.DateField(verbose_name='日期')
    Shift = models.CharField(max_length=50, verbose_name='班别')
    Type = models.CharField(max_length=100, verbose_name='类型', null=True)
    PartNumber = models.CharField(max_length=100, verbose_name='料号', null=True)
    Station = models.CharField(max_length=100, verbose_name='站别')
    UPH = models.IntegerField(verbose_name='UPH')
    Output = models.IntegerField(verbose_name='产出')
    PlanQty = models.IntegerField(verbose_name='计划数量')
    Gap = models.IntegerField(verbose_name='差异', null=True)
    #支持小数点的DTGap  
    DTGap = models.DecimalField(verbose_name='UPH差异', max_digits=18, decimal_places=2, null=True)
    StopTime = models.CharField(max_length=50, verbose_name='停线时长')
    StopCount = models.IntegerField(verbose_name='停线次数')
    IncidentsDetail = models.TextField(verbose_name='停线详情', blank=True)
    Note = models.TextField(verbose_name='备注', null=True)
    ConfirmedBy = models.CharField(max_length=100, verbose_name='确认人')
    ConfirmedAt = models.DateTimeField(auto_now_add=True, verbose_name='确认时间')

    class Meta:
        db_table = 'udtLineOutputConfirmation'
        managed = False
        verbose_name = '产出确认记录'
        verbose_name_plural = verbose_name
        ordering = ['-ConfirmedAt']

    @classmethod
    def get_week_summary(cls, start_date=None, end_date=None):
        """获取指定周期的汇总数据"""
        queryset = cls.objects.all()
        
        if start_date and end_date:
            queryset = queryset.filter(Date__range=[start_date, end_date])
            
        # 添加调试信息
        print("Debug - SQL Query:", str(queryset.query))
        
        return queryset.values(
            'Line', 'Type', 'Station', 'PartNumber', 'Date'
        ).annotate(
            total_output=Sum('Output'),
            total_planqty=Sum('PlanQty'),
            gap=Sum('Gap'),
            StopCount=Sum('StopCount'),
            # 简化 StopTime 的处理
            StopTime=Sum(
                Cast(
                    Replace(
                        Replace(
                            F('StopTime'),
                            Value('分钟'),
                            Value('')
                        ),
                        Value(' '),
                        Value('')
                    ),
                    output_field=IntegerField()
                )
            )
        ).order_by('Line')

    @classmethod
    def get_week_choices(cls):
        """获取可选的周数据"""
        # 获取最早和最晚的记录日期
        date_range = cls.objects.aggregate(
            start=models.Min('Date'),
            end=models.Max('Date')
        )
        
        if not date_range['start'] or not date_range['end']:
            return []
            
        start_date = date_range['start']
        end_date = date_range['end']
        
        weeks = []
        current = start_date
        while current <= end_date:
            # 获取当前周的开始(周一)和结束(周日)
            week_start = current - timedelta(days=current.weekday())
            week_end = week_start + timedelta(days=6)
            
            # 格式化显示
            week_display = f"第{week_start.isocalendar()[1]}周 ({week_start.strftime('%Y-%m-%d')} ~ {week_end.strftime('%Y-%m-%d')})"
            weeks.append((
                f"{week_start.strftime('%Y-%m-%d')},{week_end.strftime('%Y-%m-%d')}", 
                week_display
            ))
            
            current += timedelta(days=7)
            
        return weeks
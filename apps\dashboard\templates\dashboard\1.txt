<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{{ page_title }}</title>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/echarts/5.4.3/echarts.min.js"></script>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .dashboard-container {
            max-width: 1200px;
            margin: 0 auto;
            background-color: white;
            border-radius: 8px;
            box-shadow: 0 0 10px rgba(0,0,0,0.1);
            padding: 20px;
        }
        .dashboard-header {
            text-align: center;
            margin-bottom: 30px;
            padding-bottom: 15px;
            border-bottom: 1px solid #eee;
        }
        .chart-container {
            margin-bottom: 40px;
        }
        .chart-row {
            display: flex;
            flex-wrap: wrap;
            gap: 20px;
            margin-bottom: 20px;
        }
        .chart-box {
            flex: 1;
            min-width: 300px;
            border: 1px solid #eaeaea;
            border-radius: 6px;
            padding: 15px;
            box-shadow: 0 2px 5px rgba(0,0,0,0.05);
        }
        .chart-title {
            text-align: center;
            font-size: 18px;
            margin-bottom: 15px;
            font-weight: bold;
            color: #333;
        }
        .chart {
            height: 400px;
            width: 100%;
        }
        .controls {
            display: flex;
            justify-content: center;
            margin-bottom: 20px;
            gap: 20px;
        }
        select {
            padding: 8px 12px;
            border-radius: 4px;
            border: 1px solid #ddd;
        }
        .error-message {
            color: #d9534f;
            text-align: center;
            padding: 20px;
            background-color: #f9f2f2;
            border-radius: 6px;
            margin: 20px 0;
        }
    </style>
</head>
<body>
    <div class="dashboard-container">
        <div class="dashboard-header">
            <h1>{{ page_title }}</h1>
        </div>
        
        {% if error_message %}
        <div class="error-message">
            {{ error_message }}
        </div>
        {% else %}
        
        <div class="controls">
            <div>
                <label for="project-filter">Project:</label>
                <select id="project-filter">
                    <option value="all">All Projects</option>
                </select>
            </div>
        </div>
        
        <div class="chart-row">
            <div class="chart-box">
                <div class="chart-title">ORT/DV Quantity by Month</div>
                <div id="qty-chart" class="chart"></div>
            </div>
            <div class="chart-box">
                <div class="chart-title">ORT/DV Unit Cost by Month</div>
                <div id="cost-chart" class="chart"></div>
            </div>
        </div>
        
        <div class="chart-row">
            <div class="chart-box">
                <div class="chart-title">ORT vs DV Quantity Comparison</div>
                <div id="qty-comparison-chart" class="chart"></div>
            </div>
            <div class="chart-box">
                <div class="chart-title">ORT vs DV Unit Cost Comparison</div>
                <div id="cost-comparison-chart" class="chart"></div>
            </div>
        </div>
        
        <script>
            // Initialize ECharts instances
            const qtyChart = echarts.init(document.getElementById('qty-chart'));
            const costChart = echarts.init(document.getElementById('cost-chart'));
            const qtyComparisonChart = echarts.init(document.getElementById('qty-comparison-chart'));
            const costComparisonChart = echarts.init(document.getElementById('cost-comparison-chart'));
            
            // Parse data from Django template
            const qtyChartData = JSON.parse('{{ qty_chart_data|escapejs }}');
            const costChartData = JSON.parse('{{ cost_chart_data|escapejs }}');
            
            // Get unique projects and months
            const allMonths = Object.keys(qtyChartData);
            const allProjects = new Set();
            
            for (const month in qtyChartData) {
                for (const project in qtyChartData[month]) {
                    allProjects.add(project);
                }
            }
            
            // Populate project filter dropdown
            const projectFilter = document.getElementById('project-filter');
            allProjects.forEach(project => {
                const option = document.createElement('option');
                option.value = project;
                option.textContent = project;
                projectFilter.appendChild(option);
            });
            
            // Function to update charts based on selected project
            function updateCharts(selectedProject) {
                // Prepare data for quantity chart
                const qtyORTData = [];
                const qtyDVData = [];
                
                // Prepare data for cost chart
                const costORTData = [];
                const costDVData = [];
                
                // Prepare comparison data
                let totalORTQty = 0;
                let totalDVQty = 0;
                let totalORTCost = 0;
                let totalDVCost = 0;
                
                allMonths.forEach(month => {
                    let monthORTQty = 0;
                    let monthDVQty = 0;
                    let monthORTCost = 0;
                    let monthDVCost = 0;
                    
                    if (selectedProject === 'all') {
                        // Sum up all projects for the month
                        for (const project in qtyChartData[month]) {
                            monthORTQty += qtyChartData[month][project].ORT || 0;
                            monthDVQty += qtyChartData[month][project].DV || 0;
                            monthORTCost += costChartData[month][project].ORT || 0;
                            monthDVCost += costChartData[month][project].DV || 0;
                        }
                    } else if (qtyChartData[month][selectedProject]) {
                        // Get data for the selected project
                        monthORTQty = qtyChartData[month][selectedProject].ORT || 0;
                        monthDVQty = qtyChartData[month][selectedProject].DV || 0;
                        monthORTCost = costChartData[month][selectedProject].ORT || 0;
                        monthDVCost = costChartData[month][selectedProject].DV || 0;
                    }
                    
                    qtyORTData.push(monthORTQty);
                    qtyDVData.push(monthDVQty);
                    costORTData.push(monthORTCost.toFixed(2));
                    costDVData.push(monthDVCost.toFixed(2));
                    
                    totalORTQty += monthORTQty;
                    totalDVQty += monthDVQty;
                    totalORTCost += monthORTCost;
                    totalDVCost += monthDVCost;
                });
                
                // Quantity Chart Configuration
                const qtyOption = {
                    title: {
                        text: selectedProject === 'all' ? 'All Projects' : selectedProject,
                        left: 'center'
                    },
                    tooltip: {
                        trigger: 'axis',
                        axisPointer: {
                            type: 'shadow'
                        }
                    },
                    legend: {
                        data: ['ORT', 'DV'],
                        bottom: 10
                    },
                    grid: {
                        left: '3%',
                        right: '4%',
                        bottom: '15%',
                        containLabel: true
                    },
                    xAxis: {
                        type: 'category',
                        data: allMonths
                    },
                    yAxis: {
                        type: 'value',
                        name: 'Quantity'
                    },
                    series: [
                        {
                            name: 'ORT',
                            type: 'bar',
                            stack: 'total',
                            emphasis: {
                                focus: 'series'
                            },
                            data: qtyORTData,
                            itemStyle: {
                                color: '#5470c6'
                            }
                        },
                        {
                            name: 'DV',
                            type: 'bar',
                            stack: 'total',
                            emphasis: {
                                focus: 'series'
                            },
                            data: qtyDVData,
                            itemStyle: {
                                color: '#91cc75'
                            }
                        }
                    ]
                };

                // Cost Chart Configuration
                const costOption = {
                    title: {
                        text: selectedProject === 'all' ? 'All Projects' : selectedProject,
                        left: 'center'
                    },
                    tooltip: {
                        trigger: 'axis',
                        axisPointer: {
                            type: 'shadow'
                        },
                        formatter: function(params) {
                            let tooltip = params[0].axisValueLabel + '<br/>';
                            params.forEach(param => {
                                tooltip += param.marker + ' ' + param.seriesName + ': $' + param.value + '<br/>';
                            });
                            return tooltip;
                        }
                    },
                    legend: {
                        data: ['ORT', 'DV'],
                        bottom: 10
                    },
                    grid: {
                        left: '3%',
                        right: '4%',
                        bottom: '15%',
                        containLabel: true
                    },
                    xAxis: {
                        type: 'category',
                        data: allMonths
                    },
                    yAxis: {
                        type: 'value',
                        name: 'Unit Cost ($)',
                        axisLabel: {
                            formatter: '${value}'
                        }
                    },
                    series: [
                        {
                            name: 'ORT',
                            type: 'bar',
                            stack: 'total',
                            emphasis: {
                                focus: 'series'
                            },
                            data: costORTData,
                            itemStyle: {
                                color: '#5470c6'
                            }
                        },
                        {
                            name: 'DV',
                            type: 'bar',
                            stack: 'total',
                            emphasis: {
                                focus: 'series'
                            },
                            data: costDVData,
                            itemStyle: {
                                color: '#91cc75'
                            }
                        }
                    ]
                };
                
                // Quantity Comparison Pie Chart
                const qtyComparisonOption = {
                    title: {
                        text: 'ORT vs DV Quantity',
                        left: 'center'
                    },
                    tooltip: {
                        trigger: 'item',
                        formatter: '{a} <br/>{b}: {c} ({d}%)'
                    },
                    legend: {
                        orient: 'vertical',
                        left: 'left',
                    },
                    series: [
                        {
                            name: 'Quantity',
                            type: 'pie',
                            radius: '60%',
                            center: ['50%', '60%'],
                            data: [
                                { value: totalORTQty, name: 'ORT', itemStyle: { color: '#5470c6' } },
                                { value: totalDVQty, name: 'DV', itemStyle: { color: '#91cc75' } }
                            ],
                            emphasis: {
                                itemStyle: {
                                    shadowBlur: 10,
                                    shadowOffsetX: 0,
                                    shadowColor: 'rgba(0, 0, 0, 0.5)'
                                }
                            },
                            label: {
                                formatter: '{b}: {c} ({d}%)'
                            }
                        }
                    ]
                };
                
                // Cost Comparison Pie Chart
                const costComparisonOption = {
                    title: {
                        text: 'ORT vs DV Unit Cost',
                        left: 'center'
                    },
                    tooltip: {
                        trigger: 'item',
                        formatter: '{a} <br/>{b}: ${c} ({d}%)'
                    },
                    legend: {
                        orient: 'vertical',
                        left: 'left',
                    },
                    series: [
                        {
                            name: 'Unit Cost',
                            type: 'pie',
                            radius: '60%',
                            center: ['50%', '60%'],
                            data: [
                                { value: totalORTCost.toFixed(2), name: 'ORT', itemStyle: { color: '#5470c6' } },
                                { value: totalDVCost.toFixed(2), name: 'DV', itemStyle: { color: '#91cc75' } }
                            ],
                            emphasis: {
                                itemStyle: {
                                    shadowBlur: 10,
                                    shadowOffsetX: 0,
                                    shadowColor: 'rgba(0, 0, 0, 0.5)'
                                }
                            },
                            label: {
                                formatter: '{b}: ${c} ({d}%)'
                            }
                        }
                    ]
                };
                
                // Apply options to charts
                qtyChart.setOption(qtyOption);
                costChart.setOption(costOption);
                qtyComparisonChart.setOption(qtyComparisonOption);
                costComparisonChart.setOption(costComparisonOption);
            }
            
            // Event listener for project filter
            projectFilter.addEventListener('change', function() {
                updateCharts(this.value);
            });
            
            // Initial chart update
            updateCharts('all');
            
            // Handle resize
            window.addEventListener('resize', function() {
                qtyChart.resize();
                costChart.resize();
                qtyComparisonChart.resize();
                costComparisonChart.resize();
            });
        </script>
        {% endif %}
    </div>
</body>
</html>
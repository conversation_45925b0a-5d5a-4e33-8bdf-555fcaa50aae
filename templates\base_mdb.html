{% load static %}
<!DOCTYPE html>
<html lang="en" x-data="{ darkMode: localStorage.getItem('theme') === 'dark' }" :data-mdb-theme="darkMode ? 'dark' : 'light'">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{% block title %}B15 MES Volcano Report{% endblock %}</title>
    <!-- MDB5 CSS -->
    <link href="{% static 'mdb/css/mdb.min.css' %}" rel="stylesheet" />
    <!-- Font Awesome -->
    <link rel="stylesheet" href="{% static 'css/all.min.css' %}">

    {% block extra_css %}{% endblock %}
</head>
<body>
   


    {% block content %}
    {% endblock content %}


    
    <!-- MDB5 JS -->
    <script type="text/javascript" src="{% static 'mdb/js/mdb.umd.min.js' %}"></script>
    <!-- Custom JS -->
    <script type="text/javascript" src="{% static 'htmx/alpine.min.js' %}" defer></script>
    <script type="text/javascript" src="{% static 'htmx/htmx.min.js' %}"></script>

    {% block extra_js %}{% endblock %}
</body>
</html>

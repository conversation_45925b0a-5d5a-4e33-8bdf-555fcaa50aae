{% load static %}
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{{ page_title }} - Enhanced</title>
    <!-- Tailwind CSS CDN 脚本已移除 -->
    <!-- <script src="https://cdn.tailwindcss.com"></script>  -->
    <!-- Tailwind CSS 链接已替换为 Bootstrap CSS -->
    <link rel="stylesheet" href="{% static 'bootstrap/css/bootstrap.min.css' %}">
    <!-- <script src="https://cdnjs.cloudflare.com/ajax/libs/echarts/5.4.3/echarts.min.js"></script> -->
    <script src="{% static 'js/echarts.min.js' %}"></script>
    <style>
        /* Custom styles for ECharts */
        body {
            background-color: #000; /* 黑色背景 */
            color: #fdfefe; /* 浅灰色文本 */
        }
        .chart {
            min-height: 450px; /* 确保图表有足够的空间 */
        }
        /* Style for the select dropdown */
        select {
            appearance: none; /* 移除默认箭头 */
            background-image: url('data:image/svg+xml;charset=US-ASCII,%3Csvg%20xmlns%3D%22http%3A//www.w3.org/2000/svg%22%20width%3D%22292.4%22%20height%3D%22292.4%22%3E%3Cpath%20fill%3D%22%23CBD5E0%22%20d%3D%22M287%2069.4a17.6%2017.6%200%200%200-13-5.4H18.4c-5%200-9.3%201.8-12.9%205.4A17.6%2017.6%200%200%200%200%2082.2c0%205%201.8%209.3%205.4%2012.9l128%20127.9c3.6%203.6%207.8%205.4%2012.8%205.4s9.2-1.8%2012.8-5.4L287%2095c3.5-3.5%205.4-7.8%205.4-12.8%200-5-1.9-9.4-5.4-12.8z%22/%3E%3C/svg%3E');
            background-repeat: no-repeat;
            background-position: right 0.7rem center;
            background-size: 0.65em auto;
            padding-right: 2.5rem; /* 为自定义箭头留出空间 */
        }
    </style>
</head>
<body style="background-color: black; color: #fdfefe;">
    <div class="dashboard-container" style="max-width: 7xl; margin-left: auto; margin-right: auto; background-color: #1f2937; box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04); border-radius: 0.5rem; padding: 1.5rem 2rem;">
        <div class="dashboard-header" style="text-align: center; margin-bottom: 2rem; padding-bottom: 1rem; border-bottom: 1px solid #475569;">
            <h1 style="font-size: 2.25rem; line-height: 2.5rem; font-weight: 700; color: #38bdf8;">{{ page_title }}</h1>
        </div>

        {% if error_message %}
        <div class="error-message" style="background-color: #b91c1c; border: 1px solid #ef4444; color: white; padding: 1rem 1rem; border-radius: 0.375rem; position: relative; margin-bottom: 1.5rem; text-align: center;" role="alert">
            <strong style="font-weight: 700;">Error:</strong>
            <span style="display: block;">{{ error_message }}</span>
        </div>
        {% else %}


        <div class="controls" style="display: flex; flex-direction: column; justify-content: center; align-items: center; margin-bottom: 2rem; gap: 1rem; padding: 1rem; background-color: rgba(71, 85, 105, 0.5); border-radius: 0.375rem;">
            <div>
                <label for="project-filter" style="display: block; font-size: 0.875rem; line-height: 1.25rem; font-weight: 500; color: #cbd5e1; margin-bottom: 0.25rem;">Filter by Project:</label>
                <select id="project-filter" style="background-color: #475569; border: 1px solid #64748b; color: #e2e8f0; font-size: 0.875rem; line-height: 1.25rem; border-radius: 0.375rem; outline: 2px solid transparent; outline-offset: 2px; display: block; width: 100%; padding: 0.625rem;">
                    <option value="all">All Projects</option>
                    </select>
            </div>
        </div>

        <div class="chart-row" style="display: grid; grid-template-columns: repeat(1, minmax(0, 1fr)); gap: 2rem; margin-bottom: 2rem;">
            <div class="chart-box" style="background-color: #1f2937; padding: 1.5rem; border-radius: 0.5rem; box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04); border: 1px solid #475569;">
                <!-- ORT/DV Quantity by Month 标题已移除，由 ECharts 动态生成 -->
                <div id="qty-chart" class="chart" style="width: 100%; height: 40vh;"></div>
            </div>
        </div>

        <div class="chart-row" style="display: grid; grid-template-columns: repeat(1, minmax(0, 1fr)); gap: 2rem;">
            <div class="chart-box" style="background-color: #1f2937; padding: 1.5rem; border-radius: 0.5rem; box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04); border: 1px solid #475569;">
                <!-- ORT/DV Unit Cost by Month 标题已移除，由 ECharts 动态生成 -->
                <div id="cost-chart" class="chart" style="width: 100%; height: 40vh;"></div>
            </div>
        </div>

        <div id="qty-chart-data" style="display: none;">{{ qty_chart_data|safe }}</div>
        <div id="cost-chart-data" style="display: none;">{{ cost_chart_data|safe }}</div>

        <script>
            document.addEventListener('DOMContentLoaded', function() {
                // Default ECharts theme settings for dark mode
                const defaultTheme = {
                    darkMode: true,
                    color: [ // A more modern and distinct color palette
                        '#5470c6', '#91cc75', '#fac858', '#ee6666', '#73c0de',
                        '#3ba272', '#fc8452', '#9a60b4', '#ea7ccc'
                    ],
                    backgroundColor: 'transparent', // Use container background
                    textStyle: {
                        fontFamily: 'Inter, sans-serif',
                        color: '#fdfefe' // Light gray for text
                    },
                    title: {
                        textStyle: {
                            color: '#cbd5e1', // Lighter title text (slate-300)
                            fontSize: 18,
                            fontWeight: 'bold'
                        },
                        subtextStyle: {
                            color: '#94a3b8' // (slate-400)
                        }
                    },
                    legend: {
                        textStyle: {
                            color: '#cbd5e1'
                        },
                        inactiveColor: '#64748b' // (slate-500)
                    },
                    xAxis: {
                        axisLine: {
                            lineStyle: {
                                color: '#475569' // (slate-600)
                            }
                        },
                        axisLabel: {
                            color: '#94a3b8' // (slate-400)
                        },
                        splitLine: {
                            show: false // Hide vertical grid lines for cleaner look
                        }
                    },
                    yAxis: {
                        axisLine: {
                            lineStyle: {
                                color: '#475569'
                            }
                        },
                        axisLabel: {
                            color: '#94a3b8'
                        },
                        splitLine: {
                            lineStyle: {
                                color: '#334155', // (slate-700) - subtle grid lines
                                type: 'dashed'
                            }
                        }
                    },
                    tooltip: {
                        backgroundColor: 'rgba(30, 41, 59, 0.9)', // slate-800 with opacity
                        borderColor: '#475569', // slate-600
                        textStyle: {
                            color: '#e2e8f0' // slate-200
                        },
                        axisPointer: {
                            lineStyle: {
                                color: '#64748b' // (slate-500)
                            },
                            crossStyle: {
                                color: '#64748b'
                            },
                            shadowStyle: { // For shadow type axisPointer
                                color: 'rgba(100, 116, 139, 0.2)' // (slate-500 with opacity)
                            }
                        }
                    },
                    dataZoom: {
                        textStyle: { color: '#94a3b8' },
                        borderColor: '#475569',
                        handleIcon: 'path://M10.7,11.9v-1.3H9.3v1.3c-4.9,0.3-8.8,4.4-8.8,9.4c0,5,3.9,9.1,8.8,9.4v1.3h1.3v-1.3c4.9-0.3,8.8-4.4,8.8-9.4C19.5,16.3,15.6,12.2,10.7,11.9z M13.3,24.4H6.7V23h6.6V24.4z M13.3,19.6H6.7v-1.4h6.6V19.6z',
                        handleStyle: {
                            color: '#64748b',
                            shadowBlur: 3,
                            shadowColor: 'rgba(0, 0, 0, 0.6)',
                            shadowOffsetX: 2,
                            shadowOffsetY: 2
                        },
                        brushStyle: {
                            color: 'rgba(100, 116, 139, 0.3)' // (slate-500 with opacity)
                        }
                    },
                    toolbox: {
                        iconStyle: {
                            borderColor: '#94a3b8'
                        },
                        emphasis: {
                            iconStyle: {
                                borderColor: '#cbd5e1'
                            }
                        }
                    },
                    bar: {
                        itemStyle: {
                            borderRadius: [4, 4, 0, 0] // Rounded top corners for bars
                        }
                    }
                };

                // Initialize ECharts instances with the default theme
                const qtyChart = echarts.init(document.getElementById('qty-chart'), defaultTheme);
                const costChart = echarts.init(document.getElementById('cost-chart'), defaultTheme);

                let qtyChartDataRaw = {};
                let costChartDataRaw = {};

                try {
                    const qtyDataElement = document.getElementById('qty-chart-data');
                    const costDataElement = document.getElementById('cost-chart-data');

                    if (qtyDataElement && qtyDataElement.textContent) {
                        qtyChartDataRaw = JSON.parse(qtyDataElement.textContent || '{}');
                    }
                    if (costDataElement && costDataElement.textContent) {
                        costChartDataRaw = JSON.parse(costDataElement.textContent || '{}');
                    }
                } catch (error) {
                    console.error("Error parsing chart data:", error);
                    const errorDiv = document.createElement('div');
                    errorDiv.className = 'bg-red-700 border border-red-500 text-white px-4 py-3 rounded-md relative mb-6 text-center';
                    errorDiv.textContent = 'Fatal Error: Could not load chart data. ' + error.message;
                    document.querySelector('.dashboard-container').insertBefore(errorDiv, document.querySelector('.controls').nextSibling);
                    // Hide chart containers if data parsing fails
                    document.querySelectorAll('.chart-row').forEach(el => el.style.display = 'none');
                    return; // Stop further execution
                }

                function prepareChartData(rawData, valueType = 'qty') {
                    if (Object.keys(rawData).length === 0) {
                        return { dates: [], projects: [], series: [], legendData: [], xAxisData: [] };
                    }

                    const dates = Object.keys(rawData).sort((a, b) => {
                        const parseDate = (dateStr) => {
                            const [monthStr, yearStr] = dateStr.split(' ');
                            const monthMap = { 'Jan': '01', 'Feb': '02', 'Mar': '03', 'Apr': '04', 'May': '05', 'Jun': '06', 'Jul': '07', 'Aug': '08', 'Sep': '09', 'Oct': '10', 'Nov': '11', 'Dec': '12' };
                            return `${yearStr}-${monthMap[monthStr]}`;
                        };
                        return parseDate(a).localeCompare(parseDate(b));
                    });

                    const allProjects = Array.from(new Set(Object.values(rawData).flatMap(dataOnDate => Object.keys(dataOnDate).filter(key => typeof dataOnDate[key] === 'object')))).sort();
                    const legendData = []; // Will be populated with "Project - ORT" and "Project - DV"

                    const series = [];
                    const xAxisData = dates; // X-axis will now just be the dates

                    const projectColors = defaultTheme.color; // Use default theme colors

                    ['ORT', 'DV'].forEach(type => {
                        allProjects.forEach((project, projectIndex) => {
                            const data = dates.map(date => {
                                return rawData[date]?.[project]?.[type] || 0;
                            });

                            const seriesName = `${project} - ${type}`;
                            legendData.push(seriesName); // Add to legend data

                            series.push({
                                name: seriesName,
                                type: 'bar',
                                stack: type, // Stack by type (ORT or DV)
                                data: data,
                                itemStyle: {
                                    // Assign colors based on project index, cycling through the theme colors
                                    color: projectColors[projectIndex % projectColors.length]
                                },
                                emphasis: {
                                    focus: 'series',
                                    itemStyle: {
                                        shadowBlur: 10,
                                        shadowOffsetX: 0,
                                        shadowColor: 'rgba(0, 0, 0, 0.5)'
                                    }
                                },
                                label: {
                                    show: true,
                                    position: 'inside',
                                    formatter: (params) => (params.value > 0 ? (valueType === 'cost' ? `$${params.value.toLocaleString()}` : params.value.toLocaleString()) : ''),
                                    color: '#fff',
                                    fontSize: 10,
                                    textShadowBlur: 2,
                                    textShadowColor: 'rgba(0,0,0,0.7)'
                                }
                            });
                        });
                    });
                    
                    // Filter out series that are entirely zero for the current filter
                    const activeSeries = series.filter(s => s.data.some(val => val > 0));
                    const activeLegendData = Array.from(new Set(activeSeries.map(s => s.name))); // Update legend data to include ORT/DV types

                    return {
                        dates: dates, // Original dates for reference
                        projects: allProjects,
                        series: activeSeries,
                        legendData: activeLegendData,
                        xAxisData: xAxisData
                    };
                }


                function getChartOptions(chartTitle, yAxisName, yAxisFormatter, preparedData) {
                    return {
                        title: {
                            text: chartTitle,
                            left: 'center',
                        },
                        tooltip: {
                            trigger: 'axis',
                            axisPointer: { type: 'shadow' },
                            formatter: function(params) {
                                let tooltipContent = `${params[0].name}<br/>`; // Month name
                                let totalORT = 0;
                                let totalDV = 0;
                                let projectValues = {}; // To store values per project for ORT and DV

                                params.forEach(param => {
                                    const [project, type] = param.seriesName.split(' - ');
                                    if (!projectValues[project]) {
                                        projectValues[project] = { ORT: 0, DV: 0 };
                                    }
                                    if (type === 'ORT') {
                                        projectValues[project].ORT += param.value;
                                        totalORT += param.value;
                                    } else if (type === 'DV') {
                                        projectValues[project].DV += param.value;
                                        totalDV += param.value;
                                    }
                                });

                                // Display ORT values
                                tooltipContent += `<b>ORT:</b><br/>`;
                                Object.keys(projectValues).sort().forEach(project => {
                                    if (projectValues[project].ORT > 0) {
                                        tooltipContent += `${params.find(p => p.seriesName === `${project} - ORT`)?.marker || ''} ${project}: ${yAxisFormatter === '${value}' ? '$' : ''}${projectValues[project].ORT.toLocaleString()}<br/>`;
                                    }
                                });
                                tooltipContent += `Total ORT: ${yAxisFormatter === '${value}' ? '$' : ''}${totalORT.toLocaleString()}<br/><br/>`;

                                // Display DV values
                                tooltipContent += `<b>DV:</b><br/>`;
                                Object.keys(projectValues).sort().forEach(project => {
                                    if (projectValues[project].DV > 0) {
                                        tooltipContent += `${params.find(p => p.seriesName === `${project} - DV`)?.marker || ''} ${project}: ${yAxisFormatter === '${value}' ? '$' : ''}${projectValues[project].DV.toLocaleString()}<br/>`;
                                    }
                                });
                                tooltipContent += `Total DV: ${yAxisFormatter === '${value}' ? '$' : ''}${totalDV.toLocaleString()}`;

                                return tooltipContent;
                            }
                        },
                        legend: {
                            data: preparedData.legendData,
                            bottom: 10,
                            type: 'scroll', // In case of many projects
                        },
                        grid: {
                            left: '1%',
                            right: '1%',
                            bottom: '15%', // Adjust for legend and x-axis labels
                            top: '15%', // Adjust for title
                            containLabel: true
                        },
                        xAxis: { // Only one X-axis now
                            type: 'category',
                            data: preparedData.xAxisData,
                            axisLabel: {
                                interval: 0,
                                fontSize: 14, // 增大字体
                                fontWeight: 'bold', // 加粗字体
                            },
                            axisTick: { alignWithLabel: true },
                        },
                        yAxis: {
                            type: 'value',
                            name: yAxisName,
                            nameTextStyle: { fontSize: 12, padding: [0, 0, 0, yAxisName.length > 10 ? 50 : 30] }, // Adjust padding for long names
                            axisLabel: {
                                formatter: function(value) {
                                    // Add ORT/DV labels to Y-axis if applicable
                                    if (yAxisName === 'Quantity' || yAxisName === 'Unit Cost ($)') {
                                        return value.toLocaleString(); // Keep original formatting for numbers
                                    }
                                    return value;
                                },
                                fontSize: 10
                            },
                            axisPointer: {
                                show: true,
                                type: 'shadow',
                                label: {
                                    formatter: function (params) {
                                        // Display ORT/DV on Y-axis tooltip
                                        if (yAxisName === 'Quantity') {
                                            return `ORT/DV: ${params.value.toLocaleString()}`;
                                        } else if (yAxisName === 'Unit Cost ($)') {
                                            return `ORT/DV: $${params.value.toLocaleString()}`;
                                        }
                                        return params.value;
                                    }
                                }
                            }
                        },
                        series: preparedData.series,
                        dataZoom: [ // Add dataZoom for better navigation with many dates
                            {
                                type: 'slider',
                                xAxisIndex: 0, // Only one x-axis now
                                startValue: preparedData.xAxisData.length > 10 ? preparedData.xAxisData.length - 10 : 0, // Show last 10 months
                                endValue: preparedData.xAxisData.length -1,
                                bottom: 50, // Position above legend
                                height: 20,
                                filterMode: 'filter' // 'filter' is better for bar charts
                            },
                            {
                                type: 'inside',
                                xAxisIndex: 0, // Only one x-axis now
                                filterMode: 'filter'
                            }
                        ],
                        toolbox: { // Add toolbox for utility
                            show: true,
                            orient: 'vertical',
                            left: 'right',
                            top: 'center',
                            feature: {
                                mark: { show: true },
                                dataView: { show: true, readOnly: false, title: 'Data View', lang: ['Data View', 'Close', 'Refresh'] },
                                magicType: { show: true, type: ['line', 'bar', 'stack'], title: {line: 'Line', bar: 'Bar', stack: 'Stack'} },
                                restore: { show: true, title: 'Restore' },
                                saveAsImage: { show: true, title: 'Save Image', backgroundColor: '#1f2937' } // bg for saved image
                            }
                        },
                    };
                }

                const projectFilter = document.getElementById('project-filter');
                
                // Populate project filter
                const allQtyProjects = Array.from(new Set(Object.values(qtyChartDataRaw).flatMap(dataOnDate => Object.keys(dataOnDate).filter(key => typeof dataOnDate[key] === 'object')))).sort();
                projectFilter.innerHTML = '<option value="all">All Projects</option>'; // Clear existing
                allQtyProjects.forEach(project => {
                    const option = document.createElement('option');
                    option.value = project;
                    option.textContent = project;
                    projectFilter.appendChild(option);
                });


                function updateChartsByProject(selectedProject) {
                    let filteredQtyDataRaw = {};
                    let filteredCostDataRaw = {};

                    if (selectedProject === 'all') {
                        filteredQtyDataRaw = JSON.parse(JSON.stringify(qtyChartDataRaw)); // Deep copy
                        filteredCostDataRaw = JSON.parse(JSON.stringify(costChartDataRaw));
                    } else {
                        for (const date in qtyChartDataRaw) {
                            if (qtyChartDataRaw[date][selectedProject]) {
                                filteredQtyDataRaw[date] = { [selectedProject]: qtyChartDataRaw[date][selectedProject] };
                            }
                        }
                        for (const date in costChartDataRaw) {
                            if (costChartDataRaw[date][selectedProject]) {
                                filteredCostDataRaw[date] = { [selectedProject]: costChartDataRaw[date][selectedProject] };
                            }
                        }
                    }
                    
                    const preparedQtyData = prepareChartData(filteredQtyDataRaw, 'qty');
                    const preparedCostData = prepareChartData(filteredCostDataRaw, 'cost');

                    const qtyChartTitle = `ORT/DV Quantity`;
                    const costChartTitle = `ORT/DV Unit Cost ($)`;

                    qtyChart.setOption(getChartOptions(qtyChartTitle, 'Quantity', '{value}', preparedQtyData), true);
                    costChart.setOption(getChartOptions(costChartTitle, 'Unit Cost ($)', '${value}', preparedCostData), true);
                }

                projectFilter.addEventListener('change', function() {
                    updateChartsByProject(this.value);
                });

                // Initial chart render
                updateChartsByProject('all');

                // Handle window resize
                window.addEventListener('resize', function() {
                    if (qtyChart && typeof qtyChart.resize === 'function') {
                        qtyChart.resize();
                    }
                    if (costChart && typeof costChart.resize === 'function') {
                        costChart.resize();
                    }
                });
            });
        </script>
        {% endif %}
    </div>
</body>
</html>

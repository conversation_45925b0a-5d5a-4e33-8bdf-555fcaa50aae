{% extends 'base1.html' %}
{% load static %}

{% block content %}
<div class="app-container">
    <h1 class="page-title">
        <span class="query-id">ID: {{ id }}</span>
        {{ Reason }}
    </h1>

    <div class="breadcrumb-section">
        <nav aria-label="breadcrumb">
            <ol class="breadcrumb">
                <li class="breadcrumb-item"><a href="/volcanoreport/query/">返回首页</a></li>
                <li class="breadcrumb-item active">ID: {{ id }} - {{ Reason }}</li>
            </ol>
        </nav>
    </div>

    <form method="post" id="queryForm">
        {% csrf_token %}
        <div class="form-section">
            <!-- SN输入框总是显示 -->
            <div class="form-layout">
                <div class="form-column">
                    <div class="form-group">
                        {{ form.SNTextarea.label_tag }}
                        <div class="input-area">
                            {{ form.SNTextarea }}
                        </div>
                    </div>
                </div>

                <!-- Part Number 根据需要显示 -->
                {% if visible_fields.part_number %}
                <div class="form-column">
                    <div class="form-group">
                        {{ form.PartNumber.label_tag }}
                        <div class="input-area">
                            {{ form.PartNumber }}
                        </div>
                    </div>
                </div>
                {% endif %}
            </div>

            <!-- Part Family Type 根据需要显示 -->
            {% if visible_fields.part_family_type %}
            <div class="single-row">
                <div class="form-group">
                    {{ form.part_family_type.label_tag }}
                    <div class="input-area">
                        {{ form.part_family_type }}
                    </div>
                </div>
            </div>
            {% endif %}

            <!-- Station Type List 和 Line Name 根据需要显示 -->
            {% if visible_fields.station_type_list or visible_fields.line_name %}
            <div class="form-layout">
                {% if visible_fields.station_type_list %}
                <div class="form-column">
                    <div class="form-group" id="station_type_container">
                        {{ form.station_type_list.label_tag }}
                        <div class="checkbox-card">
                            {{ form.station_type_list }}
                        </div>
                    </div>
                </div>
                {% endif %}
                
                {% if visible_fields.line_name %}
                <div class="form-column">
                    <div class="form-group" id="line_name_container">
                        {{ form.LineName.label_tag }}
                        <div class="checkbox-card">
                            {{ form.LineName }}
                        </div>
                    </div>
                </div>
                {% endif %}
            </div>
            {% endif %}

            <!-- 其他字段的两列布局 -->
            <div class="form-layout">
                <!-- 左侧列 -->
                <div class="form-column">
                    {% if visible_fields.start_time %}
                    <div class="form-group">
                        {{ form.StartTime.label_tag }}
                        <div class="input-area">
                            {{ form.StartTime }}
                        </div>
                    </div>
                    {% endif %}

                    {% if visible_fields.mpn %}
                    <div class="form-group">
                        {{ form.MPN.label_tag }}
                        <div class="input-area">
                            {{ form.MPN }}
                        </div>
                    </div>
                    {% endif %}

                    {% if visible_fields.date_code %}
                    <div class="form-group">
                        {{ form.DateCode.label_tag }}
                        <div class="input-area">
                            {{ form.DateCode }}
                        </div>
                    </div>
                    {% endif %}
                </div>

                <!-- 右侧列 -->
                <div class="form-column">
                    {% if visible_fields.end_time %}
                    <div class="form-group">
                        {{ form.EndTime.label_tag }}
                        <div class="input-area">
                            {{ form.EndTime }}
                        </div>
                    </div>
                    {% endif %}

                    {% if visible_fields.lot_code %}
                    <div class="form-group">
                        {{ form.LotCode.label_tag }}
                        <div class="input-area">
                            {{ form.LotCode }}
                        </div>
                    </div>
                    {% endif %}

                    {% if visible_fields.supplier %}
                    <div class="form-group">
                        {{ form.Supplier.label_tag }}
                        <div class="input-area">
                            {{ form.Supplier }}
                        </div>
                    </div>
                    {% endif %}
                </div>
            </div>
        </div>

        <div class="action-buttons">
            <button type="submit" class="btn btn-primary">Submit</button>
            <button type="submit" name="action" value="export" class="btn btn-success">Export to Excel</button>
        </div>
    </form>

    {% if error_message %}
        <div class="alert alert-danger">{{ error_message }}</div>
    {% endif %}

    {% if form.errors %}
        <div class="alert alert-danger">
            <ul>
                {% for field, errors in form.errors.items %}
                    {% for error in errors %}
                        <li>{{ error }}</li>
                    {% endfor %}
                {% endfor %}
            </ul>
        </div>
    {% endif %}

    {% if query_results %}
        <div class="results-section">
            <div class="results-header">
                <h2 class="results-title">查询结果</h2>
                <p class="results-count">{{ query_counts }} 条记录，此页面最多显示前100条，要全部数据，请导出为Excel。</p>
            </div>
            
            <div class="results-table">
                <table class="table">
                    <thead>
                        <tr>
                            {% for column in columns %}
                                <th>{{ column }}</th>
                            {% endfor %}
                        </tr>
                    </thead>
                    <tbody>
                        {% for row in query_results %}
                            <tr>
                                {% for cell in row %}
                                    <td>{{ cell }}</td>
                                {% endfor %}
                            </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
        </div>
    {% endif %}

    <div id="loadingSpinner" class="loading-spinner" style="display: none;">
        <div class="spinner"></div>
        <span>Loading...</span>
    </div>
</div>

<style>
/* 补充特定样式 */
.query-id {
    color: #0066cc;
    font-weight: normal;
    margin-right: 1rem;
}

.breadcrumb-section {
    margin-bottom: 2rem;
}

.breadcrumb {
    background: transparent;
    padding: 0;
}

.breadcrumb-item a {
    color: #0066cc;
    text-decoration: none;
}

.checkbox-card {
    background: #ffffff;
    border-radius: 12px;
    border: 1px solid #d2d2d7;
    padding: 1rem;
    max-height: 200px;
    overflow-y: auto;
}

.checkbox-card .form-check {
    margin-bottom: 0.5rem;
    padding: 0.5rem;
    border-radius: 6px;
    transition: all 0.2s ease;
}

.checkbox-card .form-check:hover {
    background: #f5f5f7;
}

.results-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1rem;
}

.results-count {
    color: #86868b;
    font-size: 0.9rem;
}

.loading-spinner {
    position: fixed;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    background: rgba(255, 255, 255, 0.9);
    padding: 2rem;
    border-radius: 12px;
    box-shadow: 0 4px 12px rgba(0,0,0,0.1);
    text-align: center;
    z-index: 1000;
}

.spinner {
    width: 40px;
    height: 40px;
    border: 3px solid #f5f5f7;
    border-top: 3px solid #0066cc;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin: 0 auto 1rem;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* 单行布局样式 */
.single-row {
    margin-bottom: 1.5rem;
}

.single-row .form-group {
    max-width: 100%;
}

/* 调整复选框卡片的高度 */
.checkbox-card {
    max-height: 200px;
    overflow-y: auto;
}

/* 确保两列布局的间距一致 */
.form-layout {
    margin-bottom: 1.5rem;
}

/* 调整表单组的间距 */
.form-group:last-child {
    margin-bottom: 0;
}
</style>

{% endblock %}

{% block extra_js %}
<script>
$(document).ready(function() {
    // Part Family Type 变化时的AJAX处理
    $('#id_part_family_type').change(function() {
        var partFamilyTypeId = $(this).val();
        if (partFamilyTypeId) {
            $.ajax({
                url: '{% url "volcanoreport:get_station_line_data" %}',
                data: {
                    'partfamilytypeid': partFamilyTypeId
                },
                success: function(data) {
                    updateCheckboxes('station_type_container', data.station_types, 'station_type_list');
                    updateCheckboxes('line_name_container', data.line_names, 'line_name');
                },
                error: function(xhr, status, error) {
                    console.error("AJAX Error: ", status, error);
                }
            });
        }
    });

    // 更新复选框函数
    function updateCheckboxes(containerId, items, name) {
        var container = $('#' + containerId);
        container.find('.checkbox-card').empty();
        
        items.forEach(function(item) {
            var checkboxDiv = $('<div>').addClass('form-check');
            var checkbox = $('<input>')
                .attr('type', 'checkbox')
                .attr('name', name)
                .val(item)
                .addClass('form-check-input');
            var label = $('<label>')
                .addClass('form-check-label')
                .text(item);
            
            checkboxDiv.append(checkbox, label);
            container.find('.checkbox-card').append(checkboxDiv);
        });
    }

    // 表单提交处理
    $('#queryForm').on('submit', function() {
        $('#loadingSpinner').show();
    });

    // 输入框焦点效果
    $('.input-area').on('focus', 'input, textarea, select', function() {
        $(this).closest('.input-area').addClass('focused');
    }).on('blur', 'input, textarea, select', function() {
        $(this).closest('.input-area').removeClass('focused');
    });
});
</script>
{% endblock %}

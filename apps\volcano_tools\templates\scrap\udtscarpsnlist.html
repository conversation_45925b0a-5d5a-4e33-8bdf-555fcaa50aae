{% extends 'daisyui_base.html' %}
{% load static %}

{% block content %}
<div class="container mx-auto p-4">
    <div class="card bg-base-100 shadow-xl">
        <div class="card-body">
            <h2 class="card-title">准报废清单[UdtScarpSNList]</h2>
            
            <!-- 输入区域 -->
            <div class="form-control">
                <label class="label">
                    <span class="label-text">序列号列表 (每行一个)</span>
                </label>
                <textarea id="serialNumbers" class="textarea textarea-bordered h-24" 
                          placeholder="请输入序列号，每行一个..."></textarea>
            </div>

            <!-- RITM号输入 -->
            <div class="form-control">
                <label class="label">
                    <span class="label-text">SNC号</span>
                </label>
                <input type="text" id="sncNumber" class="input input-bordered" 
                       placeholder="请输入SNC号">
            </div>

            <!-- 按钮区域 -->
            <div class="flex gap-2 mt-4">
                <button onclick="queryScrapSN()" class="btn btn-secondary">查询</button>
                
                <button onclick="insertScrapSN()" class="btn btn-primary">插入</button>
                
            </div>

            <!-- 结果表格 -->
            <div class="overflow-x-auto mt-4">
                <table class="table table-compact w-full">
                    <thead>
                        <tr>
                            <th>ID</th>
                            <th>UnitID</th>
                            <th>序列号</th>
                            <th>SNC号</th>
                            <th>创建时间</th>
                            <th>更新时间</th>
                            <th>状态</th>
                        </tr>
                    </thead>
                    <tbody id="resultTable">
                    </tbody>
                </table>
            </div>
        </div>
    </div>
</div>

<script>
async function insertScrapSN() {
    const serialNumbers = document.getElementById('serialNumbers').value;
    const sncNumber = document.getElementById('sncNumber').value;
    
    if (!serialNumbers) {
        alert('请输入序列号');
        return;
    }
    
    if (!sncNumber) {
        alert('请输入SNC号');
        return;
    }

    try {
        const response = await fetch('{% url "volcano_tools:insert_scrap_sn" %}', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRFToken': getCookie('csrftoken')
            },
            body: JSON.stringify({
                serial_numbers: serialNumbers,
                snc_number: sncNumber
            })
        });

        const data = await response.json();
        if (data.error) {
            alert(data.error);
        } else {
            alert(data.message);
            updateTable(data.query_result);
        }
    } catch (error) {
        console.error('Error:', error);
        alert('操作失败');
    }
}

async function queryScrapSN() {
    const serialNumbers = document.getElementById('serialNumbers').value;
    
    if (!serialNumbers) {
        alert('请输入要查询的序列号');
        return;
    }

    try {
        const response = await fetch('{% url "volcano_tools:query_scrap_sn" %}', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRFToken': getCookie('csrftoken')
            },
            body: JSON.stringify({
                serial_numbers: serialNumbers
            })
        });

        const data = await response.json();
        if (data.error) {
            alert(data.error);
        } else {
            updateTable(data.data);
        }
    } catch (error) {
        console.error('Error:', error);
        alert('查询失败');
    }
}

function updateTable(data) {
    const tbody = document.getElementById('resultTable');
    tbody.innerHTML = '';
    
    data.forEach(row => {
        const tr = document.createElement('tr');
        tr.innerHTML = `
            <td>${row.id}</td>
            <td>${row.unitid}</td>
            <td>${row.serial_number}</td>
            <td>${row.snc_number}</td>
            <td>${row.creation_time}</td>
            <td>${row.last_update}</td>
            <td>${row.status}</td>
        `;
        tbody.appendChild(tr);
    });
}

function getCookie(name) {
    let cookieValue = null;
    if (document.cookie && document.cookie !== '') {
        const cookies = document.cookie.split(';');
        for (let i = 0; i < cookies.length; i++) {
            const cookie = cookies[i].trim();
            if (cookie.substring(0, name.length + 1) === (name + '=')) {
                cookieValue = decodeURIComponent(cookie.substring(name.length + 1));
                break;
            }
        }
    }
    return cookieValue;
}
</script>
{% endblock %} 
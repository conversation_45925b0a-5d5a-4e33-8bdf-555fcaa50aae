from django.db import models
from django.contrib.auth.models import User,AbstractUser

from django.conf import settings

# Create your models here.

class Status(models.TextChoices):
     UNSTARTED = 'u', "Not started yet"
     ONGOING = 'o', "Ongoing"
     FINISHED = 'f', "Finished"

class Task(models.Model):
    title = models.CharField(max_length=100)
    description = models.TextField()
    status = models.CharField(verbose_name="Task status", max_length=1, choices=Status.choices)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
         verbose_name = "任务"
         verbose_name_plural = "任务"

    def __str__(self):
        return self.title   


class Article(models.Model):
    pub_date = models.DateField()
    headline = models.CharField(max_length=200)
    content = models.TextField()
    #引入自带的User,UserID 做外键
    #author = models.ForeignKey(settings.AUTH_USER_MODE, on_delete=models.CASCADE)
    #editor = models.ForeignKey(settings.AUTH_USER_MODE, on_delete=models.CASCADE, related_name='edited')
    class Meta:
        ordering = ('headline',)
    def __str__(self):
        return self.headline
    









   
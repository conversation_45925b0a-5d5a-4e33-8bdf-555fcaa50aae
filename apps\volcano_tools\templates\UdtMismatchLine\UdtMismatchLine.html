{% extends 'base1.html' %}
{% load static %}

{% block content %}
<div class="container mt-4">
    <h2>测试SN跳过线别检查[UdtMismatchLine]</h2>
    
    <!-- 操作面板 -->
    <div class="card mb-4">
        <div class="card-header">
            <i class="fas fa-edit"></i> 批量操作
        </div>
        <div class="card-body">
            <div class="form-group">
                <label for="serialNumbers">输入序列号(每行一个，最多10000个):</label>
                <small class="form-text text-muted">支持以下格式：</small>
                <small class="form-text text-muted">1. TZYM 3KB P7V SUG7</small>
                <small class="form-text text-muted">2. 002D00490004003DDCA6</small>
                <textarea class="form-control" id="serialNumbers" rows="8" 
                        placeholder="请输入序列号，每行一个..."></textarea>
            </div>
            <div class="btn-group">
                <button class="btn btn-primary" onclick="handleBatchQuery()">
                    <i class="fas fa-search"></i> 查询
                </button>
                <button class="btn btn-success" onclick="handleBatchInsert()">
                    <i class="fas fa-plus"></i> 插入
                </button>
                <button class="btn btn-info" onclick="clearQuery()">
                    <i class="fas fa-times"></i> 清除
                </button>
            </div>
        </div>
    </div>

    <!-- 数据表格 -->
    <div class="card">
        <div class="card-header">
            <i class="fas fa-table"></i> 查询结果
        </div>
        <div class="card-body">
            <div class="table-responsive">
                <table id="mismatchTable" class="table table-striped table-bordered">
                    <thead>
                        <tr>
                            <th>ID</th>
                            <th>Unit ID</th>
                            <th>Serial Number</th>
                        </tr>
                    </thead>
                </table>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
$(document).ready(function() {
    // 初始化DataTable
    const table = $('#mismatchTable').DataTable({
        processing: true,
        serverSide: true,
        deferRender: true,        // 添加延迟渲染
        orderCellsTop: true,      // 优化排序性能
        fixedHeader: true,        // 固定表头
        stateSave: false,         // 禁用状态保存
        ajax: {
            url: "{% url 'volcano_tools:mismatch_data' %}",
            type: "POST",
            contentType: "application/json",
            data: function(d) {
                // 添加防止CSRF的处理
                d.csrfmiddlewaretoken = '{{ csrf_token }}';
                return JSON.stringify(d);
            },
            error: function (xhr, error, thrown) {
                layer.msg('数据加载出错: ' + error);
            }
        },
        columns: [
            {data: "ID", name: "ID"},
            {data: "UnitID", name: "UnitID"},
            {data: "Serialnumber", name: "Serialnumber"}
        ],
        order: [[0, "desc"]],
        pageLength: 10,
        lengthMenu: [[10, 25, 50, 100], [10, 25, 50, 100]],
        
        // 优化DOM结构
        dom: '<"top"fl>rt<"bottom"ip><"clear">',
        
        language: {
            "processing": "<div class='loading-spinner'>处理中...</div>",
            "lengthMenu": "显示 _MENU_ 条记录",
            "zeroRecords": "没有找到记录",
            "info": "显示第 _START_ 至 _END_ 项记录，共 _TOTAL_ 项",
            "infoEmpty": "显示第 0 至 0 项记录，共 0 项",
            "infoFiltered": "(由 _MAX_ 项记录过滤)",
            "search": "搜索:",
            "paginate": {
                "first": "首页",
                "previous": "上页",
                "next": "下页",
                "last": "末页"
            }
        },
        
        // 初始化完成后的回调
        initComplete: function() {
            // 添加样式优化
            $('<style>')
                .prop('type', 'text/css')
                .html(`
                    .loading-spinner {
                        padding: 1em;
                        background: rgba(255, 255, 255, 0.9);
                        border-radius: 8px;
                        box-shadow: 0 2px 10px rgba(0,0,0,0.1);
                    }
                    .dataTables_wrapper .dataTables_length select,
                    .dataTables_wrapper .dataTables_filter input {
                        border-radius: 4px;
                        border: 1px solid #ddd;
                        padding: 5px;
                    }
                    .dataTables_wrapper .dataTables_paginate .paginate_button {
                        border-radius: 4px;
                        margin: 0 2px;
                    }
                    .dataTables_wrapper .dataTables_paginate .paginate_button.current {
                        background: #007bff;
                        border-color: #007bff;
                        color: white !important;
                    }
                `)
                .appendTo('head');
        },
        
        // 添加绘制回调
        drawCallback: function() {
            // 表格重绘后的处理
            $('.dataTables_wrapper .dataTables_filter input').attr('placeholder', '输入关键字搜索...');
        }
    });

    // 添加错误处理
    $.fn.dataTable.ext.errMode = 'none';
    $('#mismatchTable').on('error.dt', function(e, settings, techNote, message) {
        layer.msg('数据表格错误: ' + message);
    });
});

function handleBatchQuery() {
    const serialNumbers = $('#serialNumbers').val().trim();
    if (!serialNumbers) {
        layer.msg('请输入序列号');
        return;
    }

    $.ajax({
        url: "{% url 'volcano_tools:batch_query' %}",
        type: 'POST',
        contentType: 'application/json',
        data: JSON.stringify({
            serial_numbers: serialNumbers
        }),
        success: function(response) {
            if (response.status === 'success') {
                $('#mismatchTable').DataTable().ajax.reload();
                layer.msg('查询成功');
            } else {
                layer.msg(response.message || '查询失败');
            }
        },
        error: function(xhr) {
            layer.msg(xhr.responseJSON?.message || '服务器错误');
        }
    });
}

function handleBatchInsert() {
    const serialNumbers = $('#serialNumbers').val().trim();
    if (!serialNumbers) {
        layer.msg('请输入序列号');
        return;
    }

    layer.confirm('确定要插入这些序列号吗？', {
        btn: ['确定', '取消']
    }, function() {
        layer.load(2); // 显示加载动画
        $.ajax({
            url: "{% url 'volcano_tools:batch_insert' %}",
            type: 'POST',
            contentType: 'application/json',
            data: JSON.stringify({
                serial_numbers: serialNumbers
            }),
            success: function(response) {
                layer.closeAll('loading'); // 关闭加载动画
                if (response.status === 'success') {
                    if (response.error_count > 0) {
                        // 如果有部分失败，使用更醒目的提示
                        layer.alert(response.message, {
                            title: '部分插入成功',
                            icon: 1
                        });
                    } else {
                        layer.msg(response.message, {icon: 1});
                    }
                    $('#mismatchTable').DataTable().ajax.reload();
                } else {
                    layer.alert(response.message, {
                        title: '插入失败',
                        icon: 2
                    });
                }
            },
            error: function(xhr) {
                layer.closeAll('loading');
                layer.alert(xhr.responseJSON?.message || '服务器错误', {
                    title: '错误',
                    icon: 2
                });
            }
        });
    });
}

function clearQuery() {
    $('#serialNumbers').val('');
    $.ajax({
        url: "{% url 'volcano_tools:batch_query' %}",
        type: 'POST',
        contentType: 'application/json',
        data: JSON.stringify({
            serial_numbers: ''
        }),
        success: function() {
            $('#mismatchTable').DataTable().ajax.reload();
            layer.msg('已清除查询条件');
        }
    });
}
</script>
{% endblock %}
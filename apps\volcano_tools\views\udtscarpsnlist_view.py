from django.shortcuts import render
from django.http import JsonResponse
from django.views.decorators.http import require_POST
from django.db import connections
import json
from datetime import datetime
import logging

logger = logging.getLogger(__name__)

def udtscarpsnlist_view(request):
    """
    渲染报废序列号管理页面
    """
    return render(request, 'scrap/udtscarpsnlist.html')

@require_POST
def insert_scrap_sn(request):
    try:
        data = json.loads(request.body)
        serial_numbers_input = data.get('serial_numbers', '')
        snc_number = data.get('snc_number', '')

        if not serial_numbers_input or not snc_number:
            return JsonResponse({'error': '序列号和SNC号都是必填项'}, status=400)

        # 处理序列号列表
        serial_numbers = [f"'{sn.strip()}'" for sn in serial_numbers_input.splitlines() if sn.strip()]
        serial_numbers_str = ','.join(serial_numbers)

        with connections['VolcanoFFDB'].cursor() as cursor:
            # 插入数据
            insert_query = f"""
            INSERT INTO UdtScarpSNList (unitid, SerialNumber, SNCNumber, CreationTime, LastUpdate,StatusID)
            SELECT unitid, value, '{snc_number}', GETDATE(), GETDATE(),0
            FROM ffSerialNumber
            WHERE value IN ({serial_numbers_str})
            """
            
            cursor.execute(insert_query)
            affected_rows = cursor.rowcount
            
            # 立即查询插入的结果
            query_result = query_scrap_sn_internal(serial_numbers_str)
            
            return JsonResponse({
                'message': f'成功插入 {affected_rows} 条记录',
                'query_result': query_result
            })

    except Exception as e:
        logger.exception("Insert operation failed")
        return JsonResponse({'error': str(e)}, status=500)

@require_POST
def query_scrap_sn(request):
    try:
        data = json.loads(request.body)
        serial_numbers_input = data.get('serial_numbers', '')

        if not serial_numbers_input:
            return JsonResponse({'error': '请输入要查询的序列号'}, status=400)

        # 处理序列号列表
        serial_numbers = [f"'{sn.strip()}'" for sn in serial_numbers_input.splitlines() if sn.strip()]
        serial_numbers_str = ','.join(serial_numbers)

        print(serial_numbers_str)

        result = query_scrap_sn_internal(serial_numbers_str)
        return JsonResponse({'data': result})

    except Exception as e:
        logger.exception("Query operation failed")
        return JsonResponse({'error': str(e)}, status=500)

def query_scrap_sn_internal(serial_numbers_str):
    with connections['VolcanoFFDB'].cursor() as cursor:
        query = f"""
        SELECT id, unitid, SerialNumber, SNCNumber, CreationTime, LastUpdate,StatusID
        FROM UdtScarpSNList
        WHERE SerialNumber IN ({serial_numbers_str})
        """
        cursor.execute(query)
        results = cursor.fetchall()

        return [
            {
                'id': row[0],
                'unitid': row[1],
                'serial_number': row[2],
                'snc_number': row[3],
                'creation_time': row[4].strftime('%Y-%m-%d %H:%M:%S') if row[4] else '',
                'last_update': row[5].strftime('%Y-%m-%d %H:%M:%S') if row[5] else '',
                'status': row[6]
            }
            for row in results
        ] 
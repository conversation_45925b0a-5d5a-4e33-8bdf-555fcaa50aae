from  .models import rptReportSN
from django.db.models import Max
from django.db import transaction

def insertrptReportSN(SNS):
    with transaction.atomic():
        # Find the current maximum ReportID
        max_report_id = rptReportSN.objects.aggregate(Max('ReportID'))['ReportID__max'] or 0
        new_report_id = max_report_id + 1

        # Create a list to hold new records
        new_records = [rptReportSN(ReportID=new_report_id, Serialnumber=sn) for sn in SNS]

        # Bulk create new records
        rptReportSN.objects.bulk_create(new_records)
        return new_report_id


from django.db import connections

from django.db import connection


def get_Linelist(partfamilyID):
    try:
        with connections['VolcanoReporDB'].cursor() as cursor:
            query = f"""
            select  Description FROM ffLine with(nolock) where location like '%Kosmos%'
            """

            cursor.execute(query)

            stationtype_data = cursor.fetchall()
            print(stationtype_data)
            return stationtype_data

    except Exception as e:
        print("Error executing query:", e)
        return None



from sqlalchemy import create_engine, text

'''

from sqlalchemy import create_engine, text

def get_complex_station_types(partfamilyID):
    # 创建SQLAlchemy引擎
    engine = create_engine(
        'mssql+pymssql://Report_Volcano:V!2017CANL@*************:1437/FF_Volcano_Report'
    )

    connection = engine.connect()
    print("connection")

    try:
        query = text("EXEC [dbo].UDPGetPartFamilyTypeStations @PartfamilyTypeID=6")
        #query = text("SELECT *FROM dbo.ffLine")
        result = connection.execute(query)
        rows = result.fetchall()
        if not rows:
            raise ValueError("Stored procedure did not return any data")

        columns = result.keys()
        print('Columns:', columns)
        print('Data fetched:', rows)
        return columns, rows
    except Exception as e:
        print("Error executing stored procedure:", e)
        return None, None
    finally:
        connection.close()


'''


def get_StationTypelist(partfamilyID):
    try:
        with connections['VolcanoReporDB'].cursor() as cursor:
            query = f"""
            [UDPGetPartFamilyAndStationType] 'StationType',6
            """

            cursor.execute(query)

            stationtype_data = cursor.fetchall()
            print(stationtype_data)
            return stationtype_data

    except Exception as e:
        print("Error executing query:", e)
        return None


#get_StationTypelist(6)


def format_parameters(input_string):
    # Remove any leading/trailing whitespace and split into lines
    lines = input_string.strip().split('\n')

    # Add comma to all lines except the last one
    formatted_lines = [line.strip() + ',' for line in lines[:-1]]
    formatted_lines.append(lines[-1].strip())

    # Join the lines back together, preserving the newline characters
    formatted_string = '\n'.join(formatted_lines)

    # Wrap the entire string in single quotes
    return f"'{formatted_string}'"



# 调用函数执行存储过程


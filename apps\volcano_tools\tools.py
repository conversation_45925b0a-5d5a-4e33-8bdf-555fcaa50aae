from .models import FsLanguage
from django.db import connection
from django.db.models import Max

def add_fs_language(description,table_name):
    # Find the maximum code that starts with '8899'
    max_code = FsLanguage.objects.filter(code__startswith='8899').aggregate(Max('code'))['code__max']
    
    if max_code is None:
        new_code = 88990001
    else:
        new_code = max_code + 1
    
    # 确保 description 是 Unicode 字符串
    description = str(description)
    table_name = str(table_name)
    
    # Create and save the new FsLanguage instance
    new_language = FsLanguage(
        language='ENG',
        type='Error',
        code=new_code,
        description=description + ' ' + table_name,
        status=1
    )
    new_language.save()

    # Return the newly created instance
    return new_code

def get_fs_languages():
    # Fetch all FsLanguage records where code starts with '8899'
    return FsLanguage.objects.filter(code__startswith='8899')

def test_chinese_language():
    # 测试用的中文描述
    test_description = "测试错误信息"
    test_table = "测试表格"
    
    # 添加中文记录
    new_code = add_fs_language(test_description, test_table)
    
    # 查询刚才添加的记录
    saved_language = FsLanguage.objects.get(code=new_code)
    
    # 打印保存的内容进行验证
    print("保存的代码:", new_code)
    print("保存的描述:", saved_language.description)
    
    # 返回是否保存成功的验证结果
    expected_description = test_description + " " + test_table
    is_success = (saved_language.description == expected_description)
    
    print("保存是否成功:", "成功" if is_success else "失败")
    print("期望的描述:", expected_description)
    
    return is_success



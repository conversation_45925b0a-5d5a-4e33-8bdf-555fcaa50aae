
# forms.py
from django.forms import ModelForm
from .models import Article, Task
from django import forms
# class ArticleForm(ModelForm):
#     class Meta:
#         model = Article
#         fields = ['pub_date', 'headline', 'content', 'reporter']


class TaskForm(ModelForm):
    class Meta:
        model = Task
        fields = "__all__"

class Valueform(forms.Form):
    user = forms.CharField(max_length = 100)
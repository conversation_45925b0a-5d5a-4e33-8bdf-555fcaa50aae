from django.urls import path
from . import views
from . import LineProductionSummary_Views # 新增导入
from . import PackingProductionSummary_Views # 导入新的 Packing 视图
from . import VolcanoProductionSummary_Views as vo # 导入新的 Volcano 视图
from .views_new.ORTInDashBoard_view import ort_dashboard_view
# from . import VolcanoProductionSummary_Views_New as vo_new # 导入新的 Volcano 视图

app_name = 'dashboard'  # 定义 app_name 以便在模板中使用命名空间

urlpatterns = [
    path('mid-wip-charts/', views.get_hermes_wip_data, name='mid_wip_charts'), # 原有 Mid Supermarket 图表
    path('prime-wip-charts/', views.get_hermes_prime_wip_data, name='prime_wip_charts'), # 新增 Prime Supermarket 图表
    path('prime-detail/', views.get_prime_detail, name='prime_detail'), # 新增 Prime 详细数据页面
    path('mid-detail/', views.get_mid_detail, name='mid_detail'), 
    # 新增每日产线生产总结图表 URL
    path('line-production-summary-chart/', LineProductionSummary_Views.get_daily_line_production_summary_chart, name='line_production_summary_chart'),
    # 新增每日包装生产总结图表 URL
    path('packing-production-summary-chart/', PackingProductionSummary_Views.get_daily_packing_production_summary_chart, name='packing_production_summary_chart'),
    ##新修改的
     path('volcano-production-summary-chart/', vo.get_daily_packing_production_summary_chart, name='volcano_production_summary_chart'),
     path('ort-dashboard/', ort_dashboard_view, name='ort_dashboard'),

    ]
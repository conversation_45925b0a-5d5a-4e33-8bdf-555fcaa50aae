{% load static %}
<!-- base_dashoard.html -->
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta http-equiv="refresh" content="600"> {# 10 分钟自动刷新，可根据需要移除或修改 #}
    <title>{% block title %}Production Dashboard{% endblock %}</title>
    <!-- ECharts -->
    <script src="{% static 'js/echarts.min.js' %}"></script>
    <!-- Font Awesome (Local) -->
    <link rel="stylesheet" href="{% static 'css/all.min.css' %}">
    <style>
        :root {
            --bg-primary: #0d1117;
            --bg-secondary: #161b22;
            --bg-card: #21262d;
            --text-primary: #f0f6fc;
            --text-secondary: #c9d1d9;
            --text-muted: #8b949e;
            --border-color: #30363d;
            --green-primary: #39d353;
            --green-secondary: #2ea043;
            --green-dark: #238636;
            --red-primary: #f85149;
            --red-secondary: #da3633;
            --blue-primary: #58a6ff;
            --blue-secondary: #388bfd;
            --yellow-primary: #e3b341;
            --gray-light: rgba(201, 209, 217, 0.3);
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Helvetica, Arial, sans-serif;
            background-color: var(--bg-primary);
            color: var(--text-primary);
            line-height: 1.6;
            padding: 0;
            margin: 0;
        }

        .top-bar {
            background-color: var(--bg-secondary);
            padding: 1rem 2rem;
            border-bottom: 1px solid var(--border-color);
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .top-bar-title {
            font-size: 1.25rem;
            font-weight: 600;
            color: var(--text-primary);
        }

        .date-display {
            color: var(--text-secondary);
            font-size: 0.9rem;
        }

        .container {
            width: 100%;
            max-width: 98vw;
            margin: 0 auto;
            padding: 2rem 1.5rem;
        }
        @media (min-width: 1200px) {
            .container {
                max-width: 1800px;
            }
        }
        @media (max-width: 900px) {
            .container {
                padding: 1rem 0.5rem;
            }
        }
        
        @media (max-width: 768px) {
            .container {
                padding: 1rem;
            }
        }

        .dashboard-header {
            margin-bottom: 2rem;
            text-align: center;
        }

        .dashboard-title {
            font-size: 2rem;
            font-weight: 600;
            margin-bottom: 0.5rem;
            color: var(--text-primary);
            letter-spacing: -0.5px;
        }
        
        @media (max-width: 768px) {
            .dashboard-title {
                font-size: 1.75rem;
            }
        }

        .dashboard-subtitle {
            color: var(--text-secondary);
            font-size: 1rem;
        }

        .card {
            background-color: var(--bg-card);
            border-radius: 12px;
            border: 1px solid var(--border-color);
            overflow: hidden;
            margin-bottom: 2rem;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            transition: transform 0.2s, box-shadow 0.2s;
        }

        .card:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 12px rgba(0, 0, 0, 0.15);
        }

        .card-header {
            padding: 1.25rem 1.5rem;
            border-bottom: 1px solid var(--border-color);
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .card-title {
            font-size: 1.25rem;
            font-weight: 600;
            color: var(--text-primary);
        }

        .card-body {
            padding: 1rem;
        }

        .chart-container { /* Common chart container style */
            width: 100%;
            height: 500px; 
        }

        @media (max-width: 768px) {
            .chart-container {
                height: 400px;
            }
        }

        .progress-cards {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
            gap: 1.5rem;
            margin-bottom: 2rem;
        }
        
        @media (max-width: 768px) {
            .progress-cards {
                grid-template-columns: 1fr;
            }
        }

        .progress-card {
            background-color: var(--bg-card);
            border-radius: 8px;
            border: 1px solid var(--border-color);
            padding: 1.25rem;
            transition: transform 0.2s;
        }

        .progress-card:hover {
            transform: translateY(-2px);
        }

        .progress-card-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 1rem;
        }

        .progress-card-title {
            font-size: 1rem;
            font-weight: 600;
            color: var(--text-primary);
        }

        .progress-card-icon {
            font-size: 1.25rem;
            color: var(--text-secondary);
            width: 32px;
            height: 32px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 50%;
            background-color: rgba(88, 166, 255, 0.1);
        }

        .progress-stats {
            display: flex;
            justify-content: space-between;
            margin-bottom: 0.75rem;
        }

        .progress-value {
            font-size: 1.75rem;
            font-weight: 600;
            color: var(--text-primary);
        }

        .progress-label {
            font-size: 0.875rem;
            color: var(--text-secondary);
        }

        .progress-percentage {
            font-size: 1rem;
            font-weight: 500;
        }

        .progress-percentage.positive {
            color: var(--green-primary);
        }

        .progress-percentage.negative {
            color: var(--red-primary);
        }

        .progress-bar-container {
            width: 100%;
            height: 8px;
            background-color: rgba(201, 209, 217, 0.1);
            border-radius: 4px;
            overflow: hidden;
        }

        .progress-bar {
            height: 100%;
            border-radius: 4px;
            transition: width 1s ease-in-out;
        }

        .progress-bar.success {
            background: linear-gradient(90deg, var(--green-dark) 0%, var(--green-primary) 100%);
        }

        .progress-bar.warning {
            background: linear-gradient(90deg, var(--yellow-primary) 0%, #ffd33d 100%);
        }

        .progress-bar.danger {
            background: linear-gradient(90deg, var(--red-secondary) 0%, var(--red-primary) 100%);
        }

        .legend {
            display: flex;
            justify-content: center;
            gap: 1.5rem;
            margin-bottom: 1rem;
        }

        .legend-item {
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .legend-color {
            width: 16px;
            height: 16px;
            border-radius: 4px;
        }

        .legend-color.actual {
            background: linear-gradient(180deg, #34D399 0%, #10B981 100%);
        }

        .legend-color.gap {
            background: linear-gradient(180deg, #60A5FA 0%, #3B82F6 100%);
        }

        .legend-color.gap-neg {
            background: linear-gradient(180deg, #F87171 0%, #EF4444 100%);
        }

        .legend-color.unmet {
            background-color: rgba(229, 231, 235, 0.6);
        }

        .legend-text {
            font-size: 0.875rem;
            color: var(--text-secondary);
        }

        .no-data {
            text-align: center;
            padding: 3rem;
            color: var(--text-muted);
            font-style: italic;
            background-color: rgba(0, 0, 0, 0.1);
            border-radius: 8px;
        }

        .error-message {
            color: var(--red-primary);
            background-color: rgba(248, 81, 73, 0.1);
            border: 1px solid var(--red-secondary);
            border-radius: 8px;
            padding: 1rem;
            margin-bottom: 1.5rem;
            text-align: center;
        }

        footer {
            text-align: center;
            padding: 1.5rem;
            background-color: var(--bg-secondary);
            color: var(--text-muted);
            font-size: 0.875rem;
            border-top: 1px solid var(--border-color);
            margin-top: 2rem;
        }

        /* Animation for loading charts */
        @keyframes pulse {
            0% { opacity: 0.6; }
            50% { opacity: 0.8; }
            100% { opacity: 0.6; }
        }

        .loading {
            animation: pulse 1.5s infinite;
            background-color: var(--bg-secondary);
            height: 100%;
            border-radius: 8px;
        }
    </style>
    {% block page_styles %}
    {# 子模板可以在这里添加或覆盖 CSS #}
    {% endblock %}
</head>
<body>
    <div class="top-bar">
        <div class="top-bar-title">{% block top_bar_title %}Production Analytics{% endblock %}</div>
        <div class="date-display" id="currentDate">Loading date...</div>
    </div>

    <div class="container">
        {% block content %}
        {# 主要页面内容会在这里 #}
        {% endblock %}
    </div>

    <footer>
        <p>Production Dashboard &copy; {% now "Y" %} | {% block footer_text %}B15 MES{% endblock %}</p>
    </footer>

    <script>
        // Set current date
        document.getElementById('currentDate').textContent = new Date().toLocaleDateString('en-US', {
            weekday: 'long', 
            year: 'numeric', 
            month: 'long', 
            day: 'numeric'
        });

        // Function to safely parse JSON from script tag
        function getJsonData(id) {
            const element = document.getElementById(id);
            if (element) {
                try {
                    const data = JSON.parse(element.textContent);
                    // 保证返回数组或对象，防止 undefined/null
                    if (Array.isArray(data)) return data;
                    if (typeof data === 'object' && data !== null) return data;
                    return [];
                } catch (e) {
                    console.error(`Error parsing JSON from ${id}:`, e);
                    return [];
                }
            }
            return [];
        }
    </script>
    {% block page_scripts %}
    {# 子模板可以在这里添加 JavaScript #}
    {% endblock %}
</body>
</html>
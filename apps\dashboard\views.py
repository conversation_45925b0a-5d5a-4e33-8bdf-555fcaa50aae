from django.shortcuts import render
from django.db import connections
from django.http import JsonResponse
import pandas as pd
import re

def get_hermes_wip_data(request):
    """
    获取 Hermes Charger 和 Holder Mid Supermarket WIP 数据并准备图表所需数据。
    """
    # Charger 数据获取
    charger_raw_data = []
    try:
        with connections['VolcanoReporDB'].cursor() as cursor:
            cursor.execute("EXEC usp_GetHermesChargerMidSupermarketInWIPQty")
            columns = [col[0] for col in cursor.description]
            for row in cursor.fetchall():
                charger_raw_data.append(dict(zip(columns, row)))
    except Exception as e:
        print(f"Database error for Charger (Mid): {e}")
        return JsonResponse({'error': f'Error fetching Charger (Mid) data: {str(e)}'}, status=500)

    # Holder 数据获取
    holder_raw_data = []
    try:
        with connections['VolcanoReporDB'].cursor() as cursor:
            cursor.execute("EXEC usp_GetHermesHolderMidSupermarketInWIPQty")
            columns = [col[0] for col in cursor.description]
            for row in cursor.fetchall():
                holder_raw_data.append(dict(zip(columns, row)))
    except Exception as e:
        print(f"Database error for Holder (Mid): {e}")
        return JsonResponse({'error': f'Error fetching Holder (Mid) data: {str(e)}'}, status=500)

    charger_waiting_laser_data_list = []
    charger_after_laser_data_list = []
    holder_waiting_laser_data_list = []
    holder_after_laser_data_list = []
    error_message = None

    if charger_raw_data:
        df_charger = pd.DataFrame(charger_raw_data)
        charger_waiting_laser_df = df_charger[df_charger['StatusDescription'] == 'WaitingLaser'].copy()
        if not charger_waiting_laser_df.empty:
            charger_waiting_laser_df.loc[:, 'Qty'] = pd.to_numeric(charger_waiting_laser_df['Qty'], errors='coerce')
            charger_waiting_laser_data = charger_waiting_laser_df.groupby('Color')['Qty'].sum().reset_index()
            charger_waiting_laser_data_list = [{'name': row['Color'], 'value': row['Qty']} for index, row in charger_waiting_laser_data.iterrows()]

        charger_after_laser_df = df_charger[df_charger['StatusDescription'] == 'AfterLaser'].copy()
        if not charger_after_laser_df.empty:
            charger_after_laser_df.loc[:, 'Qty'] = pd.to_numeric(charger_after_laser_df['Qty'], errors='coerce')
            charger_after_laser_data = charger_after_laser_df.groupby('Color')['Qty'].sum().reset_index()
            charger_after_laser_data_list = [{'name': row['Color'], 'value': row['Qty']} for index, row in charger_after_laser_data.iterrows()]
    else:
        print("No data returned from stored procedure for Charger (Mid).")

    if holder_raw_data:
        df_holder = pd.DataFrame(holder_raw_data)
        holder_waiting_laser_df = df_holder[df_holder['StatusDescription'] == 'WaitingLaser'].copy()
        if not holder_waiting_laser_df.empty:
            holder_waiting_laser_df.loc[:, 'Qty'] = pd.to_numeric(holder_waiting_laser_df['Qty'], errors='coerce')
            holder_waiting_laser_data = holder_waiting_laser_df.groupby('Color')['Qty'].sum().reset_index()
            holder_waiting_laser_data_list = [{'name': row['Color'], 'value': row['Qty']} for index, row in holder_waiting_laser_data.iterrows()]

        holder_after_laser_df = df_holder[df_holder['StatusDescription'] == 'AfterLaser'].copy()
        if not holder_after_laser_df.empty:
            holder_after_laser_df.loc[:, 'Qty'] = pd.to_numeric(holder_after_laser_df['Qty'], errors='coerce')
            holder_after_laser_data = holder_after_laser_df.groupby('Color')['Qty'].sum().reset_index()
            holder_after_laser_data_list = [{'name': row['Color'], 'value': row['Qty']} for index, row in holder_after_laser_data.iterrows()]
    else:
        print("No data returned from stored procedure for Holder (Mid).")

    if not charger_raw_data and not holder_raw_data:
        error_message = 'No data returned from Mid Supermarket stored procedures.'

    context = {
        'charger_waiting_laser_data': charger_waiting_laser_data_list,
        'charger_after_laser_data': charger_after_laser_data_list,
        'holder_waiting_laser_data': holder_waiting_laser_data_list,
        'holder_after_laser_data': holder_after_laser_data_list,
        'page_title': 'Mid Supermarket Inventory Dashboard', # 添加页面标题变量
        'error_message': error_message
    }
    return render(request, 'dashboard/mid_wip_charts.html', context) # 使用一个基础模板


def get_hermes_prime_wip_data(request):
    """
    获取 Hermes Charger 和 Holder Prime Supermarket WIP 数据并准备图表所需数据。
    """
    # Prime Charger 数据获取
    prime_charger_raw_data = []
    try:
        with connections['VolcanoReporDB'].cursor() as cursor:
            cursor.execute("EXEC usp_GetHermesChargerPrimeSupermarketInWIPQty")
            columns = [col[0] for col in cursor.description]
            for row in cursor.fetchall():
                prime_charger_raw_data.append(dict(zip(columns, row)))
    except Exception as e:
        print(f"Database error for Charger (Prime): {e}")
        return JsonResponse({'error': f'Error fetching Charger (Prime) data: {str(e)}'}, status=500)

    # Prime Holder 数据获取
    prime_holder_raw_data = []
    try:
        with connections['VolcanoReporDB'].cursor() as cursor:
            cursor.execute("EXEC usp_GetHermesHolderPrimeSupermarketInWIPQty")
            columns = [col[0] for col in cursor.description]
            for row in cursor.fetchall():
                prime_holder_raw_data.append(dict(zip(columns, row)))
    except Exception as e:
        print(f"Database error for Holder (Prime): {e}")
        return JsonResponse({'error': f'Error fetching Holder (Prime) data: {str(e)}'}, status=500)

    prime_charger_waiting_laser_data_list = []
    prime_charger_after_laser_data_list = []
    prime_holder_waiting_laser_data_list = []
    prime_holder_after_laser_data_list = []
    error_message = None

    if prime_charger_raw_data:
        df_prime_charger = pd.DataFrame(prime_charger_raw_data)
        prime_charger_waiting_laser_df = df_prime_charger[df_prime_charger['StatusDescription'] == 'WaitingLaser'].copy()
        if not prime_charger_waiting_laser_df.empty:
            prime_charger_waiting_laser_df.loc[:, 'Qty'] = pd.to_numeric(prime_charger_waiting_laser_df['Qty'], errors='coerce')
            prime_charger_waiting_laser_data = prime_charger_waiting_laser_df.groupby('Color')['Qty'].sum().reset_index()
            prime_charger_waiting_laser_data_list = [{'name': row['Color'], 'value': row['Qty']} for index, row in prime_charger_waiting_laser_data.iterrows()]

        prime_charger_after_laser_df = df_prime_charger[df_prime_charger['StatusDescription'] == 'AfterLaser'].copy()
        if not prime_charger_after_laser_df.empty:
            prime_charger_after_laser_df.loc[:, 'Qty'] = pd.to_numeric(prime_charger_after_laser_df['Qty'], errors='coerce')
            prime_charger_after_laser_data = prime_charger_after_laser_df.groupby('Color')['Qty'].sum().reset_index()
            prime_charger_after_laser_data_list = [{'name': row['Color'], 'value': row['Qty']} for index, row in prime_charger_after_laser_data.iterrows()]
    else:
        print("No data returned from stored procedure for Charger (Prime).")

    if prime_holder_raw_data:
        df_prime_holder = pd.DataFrame(prime_holder_raw_data)
        prime_holder_waiting_laser_df = df_prime_holder[df_prime_holder['StatusDescription'] == 'WaitingLaser'].copy()
        if not prime_holder_waiting_laser_df.empty:
            prime_holder_waiting_laser_df.loc[:, 'Qty'] = pd.to_numeric(prime_holder_waiting_laser_df['Qty'], errors='coerce')
            prime_holder_waiting_laser_data = prime_holder_waiting_laser_df.groupby('Color')['Qty'].sum().reset_index()
            prime_holder_waiting_laser_data_list = [{'name': row['Color'], 'value': row['Qty']} for index, row in prime_holder_waiting_laser_data.iterrows()]

        prime_holder_after_laser_df = df_prime_holder[df_prime_holder['StatusDescription'] == 'AfterLaser'].copy()
        if not prime_holder_after_laser_df.empty:
            prime_holder_after_laser_df.loc[:, 'Qty'] = pd.to_numeric(prime_holder_after_laser_df['Qty'], errors='coerce')
            prime_holder_after_laser_data = prime_holder_after_laser_df.groupby('Color')['Qty'].sum().reset_index()
            prime_holder_after_laser_data_list = [{'name': row['Color'], 'value': row['Qty']} for index, row in prime_holder_after_laser_data.iterrows()]
    else:
        print("No data returned from stored procedure for Holder (Prime).")

    if not prime_charger_raw_data and not prime_holder_raw_data:
        error_message = 'No data returned from Prime Supermarket stored procedures.'

    context = {
        'charger_waiting_laser_data': prime_charger_waiting_laser_data_list, # Note: Reusing context keys for simplicity in template
        'charger_after_laser_data': prime_charger_after_laser_data_list,   # This means the template needs to be generic
        'holder_waiting_laser_data': prime_holder_waiting_laser_data_list,   # or we pass a 'series_type' (e.g. 'Prime')
        'holder_after_laser_data': prime_holder_after_laser_data_list,
        'page_title': 'Prime Supermarket Inventory Dashboard', # 添加页面标题变量
        'error_message': error_message
    }
    return render(request, 'dashboard/prime_charts_base.html', context) # 使用同一个基础模板


def get_prime_detail(request):
    """
    获取 Hermes Prime Charger BB 详细数据，根据颜色和状态过滤。
    支持按照料号模式过滤，例如 PMMAH-DAC% 或 PMMBH-DVC%。
    """
    color = request.GET.get('color', '')
    chart_type = request.GET.get('chart_type', '')  # 'charger_waiting', 'charger_after', etc.  charger_waiting
    # print(f"Debug - Received color: {color}, chart_type: {chart_type}")

    if not color or not chart_type:
        return JsonResponse({'error': 'Missing required parameters'}, status=400)

    # 确定查询和过滤条件
    part_family = None
    part_number_pattern = None

    if chart_type == 'charger_waiting':
        part_family = ['Hermes Prime Charger BB', 'SmallDemand Prime Charger BB']
        # 对应 Waiting Laser 的料号模式 - 根据实际数据调整
        part_number_pattern = r'^PMMAH-DAC|^PMMBH-DAC'
    elif chart_type == 'charger_after':
        part_family = ['Hermes Prime Charger BB', 'SmallDemand Prime Charger BB']
        # 对应 After Laser 的料号模式 - 根据实际数据调整
        part_number_pattern = r'^PMMAH-DVC|^PMMBH-DVC'
    elif chart_type == 'holder_waiting':
        part_family = ['Hermes Holder Prime BB', 'SmallDemand Holder Prime BB']
        part_number_pattern = r'^PMMAH-DV|^PMMBH-DV'
    elif chart_type == 'holder_after':
        part_family = ['Hermes Holder Prime BB', 'SmallDemand Holder Prime BB']
        part_number_pattern = r'^PMMAH-DVC|^PMMBH-DVC'
    else:
        return JsonResponse({'error': 'Invalid chart type'}, status=400)

    # 打印调试信息
    # print(f"Debug - Received color: {color}, chart_type: {chart_type}")
    # print(f"Debug - Using part_number_pattern: {part_number_pattern}")

    # 获取数据
    raw_data = []
    try:
        with connections['VolcanoReporDB'].cursor() as cursor:
            if 'charger' in chart_type:
                cursor.execute("EXEC rudpGetKosmosChargerSupermarketWIPReport")
            else:
                cursor.execute("EXEC rudpGetKosmosHolderSupermarketWIPReport")

            columns = [col[0] for col in cursor.description]
            for row in cursor.fetchall():
                raw_data.append(dict(zip(columns, row)))
    except Exception as e:
        print(f"Database error: {e}")
        return JsonResponse({'error': f'Error fetching data: {str(e)}'}, status=500)

    # print(f"Debug - Raw data columns: {columns}")

    if not raw_data:
        return render(request, 'dashboard/prime_detail.html', {
            'error_message': 'No data returned from database',
            'page_title': f'{part_family} Detail - {color}',
            'chart_title': chart_type.replace('_', ' ').title(),
            'color': color,
            'total_qty': 0,
            'detail_data': []
        })

    # 使用 pandas 处理数据
    df = pd.DataFrame(raw_data)

    # 打印列名以便调试
    # print("Debug - Raw data columns:", df.columns.tolist())
    # print("DataFrame columns:", df.columns.tolist())
    # print("DataFrame sample:", df.head(1).to_dict('records'))

    # 检查必要的列是否存在
    required_columns = ['Color']  # 只检查颜色列，不再检查 StatusDescription
    missing_columns = [col for col in required_columns if col not in df.columns]

    if missing_columns:
        error_msg = f"Missing required columns in data: {', '.join(missing_columns)}"
        print(error_msg)
        return render(request, 'dashboard/prime_detail.html', {
            'error_message': error_msg,
            'page_title': f'{part_family} Detail - {color}',
            'chart_title': chart_type.replace('_', ' ').title(),
            'color': color,
            'total_qty': 0,
            'detail_data': []
        })

    # 只按颜色过滤，不再使用 StatusDescription
    if color == 'all':
        filtered_df = df.copy()
    else:
        filtered_df = df[df['Color'] == color]

    # 根据 chart_type 确定应该使用的 PartNumber 模式进行过滤
    if 'PartNumber' in df.columns and part_number_pattern and not filtered_df.empty:
        filtered_df = filtered_df[filtered_df['PartNumber'].str.contains(part_number_pattern, regex=True)]

    # 如果有 PartFamily 列，则过滤出 Hermes Holder Prime BB 和 SmallDemand Holder Mid BB
    if 'PartFamily' in df.columns and not filtered_df.empty:
        if isinstance(part_family, list):
            filtered_df = filtered_df[filtered_df['PartFamily'].isin(part_family)]
        else:
            filtered_df = filtered_df[filtered_df['PartFamily'].str.contains(part_family, regex=True, case=False)]

    if filtered_df.empty:
        return render(request, 'dashboard/prime_detail.html', {
            'error_message': 'No matching data found with the specified filters',
            'page_title': f'{part_family} Detail - {color}',
            'chart_title': chart_type.replace('_', ' ').title(),
            'color': color,
            'total_qty': 0,
            'detail_data': []
        })

    # 确定数量列名
    qty_column = None
    for col in ['Qty', 'QTY', 'Quantity']:
        if col in filtered_df.columns:
            qty_column = col
            break

    if not qty_column:
        print("Warning: No quantity column found in data")
        qty_column = 'QTY'  # 默认使用 QTY

    # 转换数量为数值类型
    filtered_df.loc[:, qty_column] = pd.to_numeric(filtered_df[qty_column], errors='coerce')

    # 计算总数量
    total_qty = filtered_df[qty_column].sum()

    # 为每种颜色分配颜色代码
    color_codes = {
        'Breeze Blue': '#87CEEB',
        'Digital Violet': '#8A2BE2',
        'Leaf Green': '#90EE90',
        'Midnight Black': '#777777',
        'Vivid Terracotta': '#E2725B',
        'Aspen Green': '#A2D1A0',
        'Garnet Red': '#981E32'
    }

    # 检查列名映射 - 根据实际数据调整
    column_mapping = {
        'PartFamily': ['PartFamily', 'Part_Family', 'Part Family'],
        'PartNumber': ['PartNumber', 'Part_Number', 'Part Number', 'Partnumber'],
        'Color': ['Color'],
        'FWVersion': ['FWVersion', 'FW_Version', 'FW Version'],
        'BatteryType': ['BatteryType', 'Battery_Type', 'Battery Type'],
        'Qty': ['Qty', 'QTY', 'Quantity'],
        'Supplier': ['Supplier'],
        'LC': ['LC'],
        'EngineFW': ['EngineFW', 'Engine_FW', 'Engine FW'],
        'ControlFW': ['ControlFW', 'Control_FW', 'Control FW'],
        'CMSupplier': ['CMSupplier', 'CMS_Supplier', 'CMS Supplier'],
        'CMLC': ['CMLC', 'CM_LC', 'CM LC'],
    
       

    }

    # 确定实际的列名
    actual_columns = {}
    for target_col, possible_cols in column_mapping.items():
        for col in possible_cols:
            if col in df.columns:
                actual_columns[target_col] = col
                break
        if target_col not in actual_columns:
            actual_columns[target_col] = None

    # print("Actual columns mapping:", actual_columns)

    # 准备详细数据列表
    detail_data = []
    for _, row in filtered_df.iterrows():
        item = {}

        # 使用映射获取数据
        for target_col, actual_col in actual_columns.items():
            if actual_col:
                item[target_col] = row.get(actual_col, '')
            elif target_col == 'PartFamily':
                item[target_col] = part_family
            else:
                item[target_col] = ''

        # 添加颜色代码
        item['color_code'] = color_codes.get(row.get('Color', ''), '#CCCCCC')

        # 确保 Qty 是数值，使用正确的数量列
        if qty_column in row:
            item['Qty'] = row[qty_column]
        elif 'Qty' not in item or not item['Qty']:
            item['Qty'] = 0

        detail_data.append(item)

    # 按数量降序排序
    detail_data = sorted(detail_data, key=lambda x: x['Qty'], reverse=True)
    # print("Detail data sample:", detail_data[:1] if detail_data else [])

    context = {
        'page_title': f"{(' / '.join(part_family) if isinstance(part_family, list) else part_family)} Detail - {'全部颜色' if color == 'all' else color}",
        'chart_title': (chart_type.replace('_', ' ').title() + (" (全部颜色)" if color == 'all' else '')),
        'color': '全部颜色' if color == 'all' else color,
        'total_qty': total_qty,
        'detail_data': detail_data
    }

    # 选择模板
    if chart_type in ['holder_waiting', 'holder_after']:
        template_name = 'dashboard/prime_holder_detail.html'
    else:
        template_name = 'dashboard/prime_detail.html'

    return render(request, template_name, context)


def get_mid_detail(request):
    """
    获取 Hermes mid Charger BB 详细数据，根据颜色和状态过滤。
    支持按照料号模式过滤，例如 PMMAH-DAC% 或 PMMBH-DVC%。
    """
    color = request.GET.get('color', '')
    chart_type = request.GET.get('chart_type', '')  # 'charger_waiting', 'charger_after', etc.  charger_waiting
    # print(f"Debug - Received color: {color}, chart_type: {chart_type}")

    if not color or not chart_type:
        return JsonResponse({'error': 'Missing required parameters'}, status=400)

    # 确定查询和过滤条件
    part_family = None
    part_number_pattern = None

    if chart_type == 'charger_waiting':
        part_family = ['Hermes Mid Charger BB', 'SmallDemand Mid Charger BB']
        # 对应 Waiting Laser 的料号模式 - 根据实际数据调整
        part_number_pattern = r'^PMMAH-DAC|^PMMBH-DAC'
    elif chart_type == 'charger_after':
        part_family = ['Hermes Mid Charger BB', 'SmallDemand Mid Charger BB']
        # 对应 After Laser 的料号模式 - 根据实际数据调整
        part_number_pattern = r'^PMMAH-DVC|^PMMBH-DVC'
    elif chart_type == 'holder_waiting':
        part_family = ['Hermes Holder Mid BB', 'SmallDemand Holder Mid BB']
        part_number_pattern = r'^PMMAH-DV|^PMMBH-DV'
    elif chart_type == 'holder_after':
        part_family = ['Hermes Holder Mid BB', 'SmallDemand Holder Mid BB']
        part_number_pattern = r'^PMMAH-DVC|^PMMBH-DVC'
    else:
        return JsonResponse({'error': 'Invalid chart type'}, status=400)

    # 打印调试信息
    # print(f"Debug - Received color: {color}, chart_type: {chart_type}")
    # print(f"Debug - Using part_number_pattern: {part_number_pattern}")

    # 获取数据
    raw_data = []
    try:
        with connections['VolcanoReporDB'].cursor() as cursor:
            if 'charger' in chart_type:
                cursor.execute("EXEC rudpGetKosmosChargerSupermarketWIPReport")
            else:
                cursor.execute("EXEC rudpGetKosmosHolderSupermarketWIPReport")

            columns = [col[0] for col in cursor.description]
            for row in cursor.fetchall():
                raw_data.append(dict(zip(columns, row)))
    except Exception as e:
        print(f"Database error: {e}")
        return JsonResponse({'error': f'Error fetching data: {str(e)}'}, status=500)

    # print(f"Debug - Raw data columns: {columns}")

    if not raw_data:
        return render(request, 'dashboard/prime_detail.html', {
            'error_message': 'No data returned from database',
            'page_title': f'{part_family} Detail - {color}',
            'chart_title': chart_type.replace('_', ' ').title(),
            'color': color,
            'total_qty': 0,
            'detail_data': []
        })

    # 使用 pandas 处理数据
    df = pd.DataFrame(raw_data)

    # 打印列名以便调试
    # print("Debug - Raw data columns:", df.columns.tolist())
    # print("DataFrame columns:", df.columns.tolist())
    # print("DataFrame sample:", df.head(1).to_dict('records'))

    # 检查必要的列是否存在
    required_columns = ['Color']  # 只检查颜色列，不再检查 StatusDescription
    missing_columns = [col for col in required_columns if col not in df.columns]

    if missing_columns:
        error_msg = f"Missing required columns in data: {', '.join(missing_columns)}"
        print(error_msg)
        return render(request, 'dashboard/prime_detail.html', {
            'error_message': error_msg,
            'page_title': f'{part_family} Detail - {color}',
            'chart_title': chart_type.replace('_', ' ').title(),
            'color': color,
            'total_qty': 0,
            'detail_data': []
        })

    # 只按颜色过滤，不再使用 StatusDescription
    if color == 'all':
        filtered_df = df.copy()
    else:
        filtered_df = df[df['Color'] == color]

    # 根据 chart_type 确定应该使用的 PartNumber 模式进行过滤
    if 'PartNumber' in df.columns and part_number_pattern and not filtered_df.empty:
        filtered_df = filtered_df[filtered_df['PartNumber'].str.contains(part_number_pattern, regex=True)]

    # 如果有 PartFamily 列，则过滤出 Hermes Holder Prime BB 和 SmallDemand Holder Mid BB
    if 'PartFamily' in df.columns and not filtered_df.empty:
        if isinstance(part_family, list):
            filtered_df = filtered_df[filtered_df['PartFamily'].isin(part_family)]
        else:
            filtered_df = filtered_df[filtered_df['PartFamily'].str.contains(part_family, regex=True, case=False)]

    if filtered_df.empty:
        return render(request, 'dashboard/prime_detail.html', {
            'error_message': 'No matching data found with the specified filters',
            'page_title': f'{part_family} Detail - {color}',
            'chart_title': chart_type.replace('_', ' ').title(),
            'color': color,
            'total_qty': 0,
            'detail_data': []
        })

    # 确定数量列名
    qty_column = None
    for col in ['Qty', 'QTY', 'Quantity']:
        if col in filtered_df.columns:
            qty_column = col
            break

    if not qty_column:
        print("Warning: No quantity column found in data")
        qty_column = 'QTY'  # 默认使用 QTY

    # 转换数量为数值类型
    filtered_df.loc[:, qty_column] = pd.to_numeric(filtered_df[qty_column], errors='coerce')

    # 计算总数量
    total_qty = filtered_df[qty_column].sum()

    # 为每种颜色分配颜色代码
    color_codes = {
        'Breeze Blue': '#87CEEB',
        'Digital Violet': '#8A2BE2',
        'Leaf Green': '#90EE90',
        'Midnight Black': '#777777',
        'Vivid Terracotta': '#E2725B',
        'Aspen Green': '#A2D1A0',
        'Garnet Red': '#981E32'
    }

    # 检查列名映射 - 根据实际数据调整
    column_mapping = {
        'PartFamily': ['PartFamily', 'Part_Family', 'Part Family'],
        'PartNumber': ['PartNumber', 'Part_Number', 'Part Number', 'Partnumber'],
        'Color': ['Color'],
        'FWVersion': ['FWVersion', 'FW_Version', 'FW Version'],
        'BatteryType': ['BatteryType', 'Battery_Type', 'Battery Type'],
        'Qty': ['Qty', 'QTY', 'Quantity'],
        'Supplier': ['Supplier'],
        'LC': ['LC'],
        'EngineFW': ['EngineFW', 'Engine_FW', 'Engine FW'],
        'ControlFW': ['ControlFW', 'Control_FW', 'Control FW'],
        'CMSupplier': ['CMSupplier', 'CMS_Supplier', 'CMS Supplier'],
        'CMLC': ['CMLC', 'CM_LC', 'CM LC'],
    
       

    }

    # 确定实际的列名
    actual_columns = {}
    for target_col, possible_cols in column_mapping.items():
        for col in possible_cols:
            if col in df.columns:
                actual_columns[target_col] = col
                break
        if target_col not in actual_columns:
            actual_columns[target_col] = None

    # print("Actual columns mapping:", actual_columns)

    # 准备详细数据列表
    detail_data = []
    for _, row in filtered_df.iterrows():
        item = {}

        # 使用映射获取数据
        for target_col, actual_col in actual_columns.items():
            if actual_col:
                item[target_col] = row.get(actual_col, '')
            elif target_col == 'PartFamily':
                item[target_col] = part_family
            else:
                item[target_col] = ''

        # 添加颜色代码
        item['color_code'] = color_codes.get(row.get('Color', ''), '#CCCCCC')

        # 确保 Qty 是数值，使用正确的数量列
        if qty_column in row:
            item['Qty'] = row[qty_column]
        elif 'Qty' not in item or not item['Qty']:
            item['Qty'] = 0

        detail_data.append(item)

    # 按数量降序排序
    detail_data = sorted(detail_data, key=lambda x: x['Qty'], reverse=True)
    # print("Detail data sample:", detail_data[:1] if detail_data else [])

    context = {
        'page_title': f"{(' / '.join(part_family) if isinstance(part_family, list) else part_family)} Detail - {'全部颜色' if color == 'all' else color}",
        'chart_title': (chart_type.replace('_', ' ').title() + (" (全部颜色)" if color == 'all' else '')),
        'color': '全部颜色' if color == 'all' else color,
        'total_qty': total_qty,
        'detail_data': detail_data
    }

    # 选择模板
    if chart_type in ['holder_waiting', 'holder_after']:
        template_name = 'dashboard/prime_holder_detail.html'
    else:
        template_name = 'dashboard/prime_detail.html'

    return render(request, template_name, context)
import pyodbc

conn_str = (
    'DRIVER={ODBC Driver 17 for SQL Server};'
    'SERVER=172.30.30.217,1437;'
    'DATABASE=FF_Volcano_Report;'
    'UID=Report_Volcano;'
    'PWD=V!2017CANL;'
    'TrustServerCertificate=yes;'
    'MARS_Connection=yes;'
)

try:
    conn = pyodbc.connect(conn_str)
    print("Connection successful")
    conn.close()
except Exception as e:
    print(f"Error connecting to database: {e}")

from django.shortcuts import render, get_object_or_404
from django.http import JsonResponse, HttpResponse
from django.views.decorators.http import require_POST
from django.contrib import messages
from django.db import transaction
from .forms import PartDetailForm
from ..models import FFPartdetail, FFPart, luPartDetailDef
from operation_logs.models import OperationLog
from django.contrib.auth.decorators import login_required
import json
import pandas as pd
from openpyxl import Workbook
from openpyxl.styles import Font, PatternFill
from django.urls import path

import logging

logger = logging.getLogger('django.db.backends')
logger.setLevel(logging.DEBUG)
logger.addHandler(logging.StreamHandler())

def ffpartdetail(request):
    form = PartDetailForm(request.session.get('search_form_data') if request.method == 'GET' else None)

    if request.method == 'POST':
        form = PartDetailForm(request.POST)
        action = request.POST.get('action')

        if action == 'reset':
            form = PartDetailForm()
        elif form.is_valid():
            if action == 'search':
                return search_action(request, form)
            elif action == 'insert':
                return insert_action(request, form)
            elif action == 'update':
                return update_action(request, form)

        request.session['search_form_data'] = request.POST

    return render(request, 'partdetail/partdetail.html', {'form': form})


def insert_action(request, form):
    try:
        part_numbers = [pn.strip() for pn in form.cleaned_data['part_numbers'].split('\r\n')]
        contents = [c.strip() for c in form.cleaned_data['content'].split('\r\n')]
        part_detail_def = form.cleaned_data['part_detail_def_id']
        part_detail_def_id = part_detail_def.id if part_detail_def else None

        logger.info(f"Part numbers count: {len(part_numbers)}")
        logger.info(f"Contents count: {len(contents)}")

        # 内容验证逻辑
        if len(contents) != 1 and len(contents) != len(part_numbers):
            messages.error(
                request, 
                f'Content count mismatch: you have {len(part_numbers)} part numbers but {len(contents)} contents. '
                'Either provide one content for all parts, or match the number of contents with part numbers.'
            )
            return render(request, 'partdetail/partdetail.html', {'form': form})

        # 如果只有一个content值，则所有料号都使用这个值
        if len(contents) == 1:
            content_value = contents[0]
            part_content_map = {pn: content_value for pn in part_numbers}
        else:
            # 此时已确保content数量与料号数量相等
            part_content_map = dict(zip(part_numbers, contents))

        logger.info(f"Part to content mapping: {part_content_map}")
        
        inserted_records = []
        with transaction.atomic():
            parts = FFPart.objects.filter(PartNumber__in=part_numbers)
            
            # 创建零件号到Part对象的映射
            part_map = {part.PartNumber: part for part in parts}
            
            for part_number in part_numbers:
                if part_number not in part_map:
                    messages.warning(request, f'Part not found: {part_number}')
                    continue
                    
                part = part_map[part_number]
                content = part_content_map[part_number]
                
                # 检查是否已存在记录
                existing_record = FFPartdetail.objects.filter(
                    PartID=part.id,
                    PartDetailDefID=part_detail_def_id
                ).first()
                
                if existing_record:
                    messages.warning(request, f'Record already exists for part: {part_number}')
                    continue

                ffpartdetail = FFPartdetail.objects.create(
                    PartID=part.id,
                    Content=content,
                    PartDetailDefID=part_detail_def_id
                )
                
                inserted_records.append({
                    'PartNumber': part_number,
                    'Content': content,
                    'PartDetailDefID': part_detail_def_id
                })
                
                logger.info(f"Inserted record for PartNumber: {part_number} with content: {content}")

            if inserted_records:
                messages.success(request, 'Insert [FFPartdetail] successfully.')
                
                # 添加更详细的日志记录
                log_details = {
                    'PartDetailDefID': part_detail_def_id,
                    'InsertedRecords': inserted_records
                }
                
                OperationLog.objects.create(
                    username=request.user.username,
                    operation='INSERT',
                    table_name='FFPartdetail',
                    details=json.dumps(log_details, ensure_ascii=False)
                )
                logger.info(f"Insert operation completed. Log details: {log_details}")
            else:
                messages.warning(request, 'No records were inserted.')

        return search_action(request, form)
        
    except Exception as e:
        logger.error(f'Error during insert: {str(e)}')
        messages.error(request, str(e))
        return render(request, 'partdetail/partdetail.html', {'form': form})


@require_POST
def delete_ffpartdetail(request, ffpartdetail_id):
    logger.info(f'Attempting to delete FFPartdetail with id: {ffpartdetail_id}')
    try:
        ffpartdetail = get_object_or_404(FFPartdetail.objects, id=ffpartdetail_id)

        # 获取要删除的记录的详细信息
        deleted_record_info = {
            'id': ffpartdetail.id,
            'PartID': ffpartdetail.PartID,
            'Content': ffpartdetail.Content,
            'PartDetailDefID': ffpartdetail.PartDetailDefID
        }

        # 获取关联的 FFPart 的 PartNumber（如果存在）
        part_number = FFPart.objects.filter(id=ffpartdetail.PartID).values_list('PartNumber', flat=True).first()
        if part_number:
            deleted_record_info['PartNumber'] = part_number

        # 删除记录
        ffpartdetail.delete()

        # 添加更详细的日志记录
        log_details = {
            'operation': 'Delete',
            'deleted_record': deleted_record_info
        }

        OperationLog.objects.create(
            username=request.user.username,
            operation='DELETE',
            table_name='FFPartdetail',
            record_id=ffpartdetail_id,
            details=json.dumps(log_details, ensure_ascii=False)
        )

        logger.info(f'Successfully deleted FFPartdetail with id: {ffpartdetail_id}')
        return JsonResponse({'result': 'success', 'message': '删除成功'})
    except Exception as e:
        logger.exception(f'Error deleting FFPartdetail: {str(e)}')
        return JsonResponse({'result': 'error', 'message': str(e)})


def search_action(request, form):
    #print('search_action now')
    part_numbers = form.cleaned_data['part_numbers'].split('\r\n')
    part_detail_def_id = form.cleaned_data['part_detail_def_id']

    #print(part_numbers)

    if isinstance(part_detail_def_id, str):
        part_detail_def_id = int(part_detail_def_id.split(' - ')[0])
    elif part_detail_def_id:
        part_detail_def_id = part_detail_def_id.id

    #print(part_detail_def_id)

    parts = FFPart.objects.filter(PartNumber__in=part_numbers)
    #print(parts)
    part_ids = parts.values_list('id', flat=True)

    details = FFPartdetail.objects.filter(PartID__in=part_ids)
    if part_detail_def_id:
        details = details.filter(PartDetailDefID=part_detail_def_id)

    details = details.values('id', 'PartID', 'Content', 'PartDetailDefID')
    details = [{**detail, 'PartNumber': parts.get(id=detail['PartID']).PartNumber} for detail in details]

    #print(details)

    return render(request, 'partdetail/partdetail.html', {'results': details, 'form': form})


import logging
logger = logging.getLogger(__name__)

@transaction.atomic
def update_action(request, form):
    logger.info("Update action started")
    part_numbers = [pn.strip() for pn in form.cleaned_data['part_numbers'].split('\r\n')]
    contents = [c.strip() for c in form.cleaned_data['content'].split('\r\n')]
    part_detail_def = form.cleaned_data['part_detail_def_id']
    part_detail_def_id = part_detail_def.id if part_detail_def else None

    logger.info(f"Part numbers count: {len(part_numbers)}")
    logger.info(f"Contents count: {len(contents)}")

    # 内容验证逻辑
    if len(contents) != 1 and len(contents) != len(part_numbers):
        messages.error(
            request, 
            f'Content count mismatch: you have {len(part_numbers)} part numbers but {len(contents)} contents. '
            'Either provide one content for all parts, or match the number of contents with part numbers.'
        )
        return render(request, 'partdetail/partdetail.html', {'form': form})

    # 如果只有一个content值，则所有料号都使用这个值
    if len(contents) == 1:
        content_value = contents[0]
        part_content_map = {pn: content_value for pn in part_numbers}
    else:
        # 此时已确保content数量与料号数量相等
        part_content_map = dict(zip(part_numbers, contents))

    logger.info(f"Part to content mapping: {part_content_map}")

    parts = FFPart.objects.filter(PartNumber__in=part_numbers)
    logger.info(f"Found {parts.count()} parts")

    log_details = []
    updated_records = []
    try:
        for part in parts:
            if not part.PartNumber.strip():
                logger.warning(f'Part Number must not be empty')
                continue

            # 从映射中获取对应的内容
            content_item = part_content_map.get(part.PartNumber.strip())
            if content_item is None:
                logger.warning(f'No content found for part number: {part.PartNumber}')
                continue

            old_record = FFPartdetail.objects.filter(
                PartID=part.id, 
                PartDetailDefID=part_detail_def_id
            ).first()

            if old_record:
                old_content = old_record.Content
                updated_records.append(FFPartdetail(
                    id=old_record.id,
                    Content=content_item,
                    PartID=part.id,
                    PartDetailDefID=part_detail_def_id
                ))
                log_details.append({
                    'PartNumber': part.PartNumber,
                    'OldContent': old_content,
                    'NewContent': content_item,
                    'PartDetailDefID': part_detail_def_id
                })
                logger.info(f"Updating record for PartNumber: {part.PartNumber} with content: {content_item}")
            else:
                logger.warning(f'No existing record found for Part Number: {part.PartNumber}')
                messages.warning(request, f'No existing record found for Part Number: {part.PartNumber}')

        if updated_records:
            FFPartdetail.objects.bulk_update(updated_records, ['Content'])
            
            OperationLog.objects.create(
                username=request.user.username,
                operation='UPDATE',
                table_name='FFPartdetail',
                details=json.dumps({
                    'UpdatedRecords': log_details
                }, ensure_ascii=False)
            )
            messages.success(request, 'Update operation completed successfully')
        else:
            messages.warning(request, 'No records were updated')

    except Exception as e:
        logger.error(f'Error during update: {str(e)}')
        messages.error(request, f'Error during update: {str(e)}')

    return search_action(request, form)


def clean_cell_value(value):
    """清理单元格数据"""
    if pd.isna(value):  # 处理空值
        return ''
    
    # 转换为字符串并清理
    value = str(value)
    # 去除首尾空格、制表符、换行符
    value = value.strip(' \t\n\r')
    # 替换内部的多个空格为单个空格
    value = ' '.join(value.split())
    return value

def download_template(request):
    """下载Excel模板"""
    wb = Workbook()
    ws = wb.active
    ws.title = "Part Details"
    
    # 获取所有可用的 PartDetailDef 描述 - 注意这里不需要using('FFTestDB')
    part_detail_defs = luPartDetailDef.objects.values_list('Description', flat=True).distinct()
    
    # 设置表头
    headers = ['PartNumber'] + list(part_detail_defs)
    for col, header in enumerate(headers, 1):
        cell = ws.cell(row=1, column=col)
        cell.value = header
        cell.font = Font(bold=True)
        cell.fill = PatternFill(start_color="CCCCCC", end_color="CCCCCC", fill_type="solid")

    response = HttpResponse(content_type='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet')
    response['Content-Disposition'] = 'attachment; filename=part_details_template.xlsx'
    wb.save(response)
    return response

@require_POST
def preview_excel(request):
    try:
        excel_file = request.FILES.get('excel_file')
        if not excel_file:
            return JsonResponse({'status': 'error', 'message': 'No file uploaded'})

        df = pd.read_excel(excel_file)
        if 'PartNumber' not in df.columns:
            return JsonResponse({
                'status': 'error', 
                'message': 'Missing required column: PartNumber'
            })

        preview_data = []
        for index, row in df.iterrows():
            part_number = clean_cell_value(row['PartNumber'])
            
            # 验证数据 - 使用正式数据库
            status = 'OK'
            if not part_number:
                status = 'Error: Empty Part Number'
            elif not FFPart.objects.filter(PartNumber=part_number).exists():
                status = 'Error: Part Not Found'
            
            contents = {}
            for col in df.columns:
                if col != 'PartNumber':
                    content = clean_cell_value(row[col])
                    if content:  # 只保存非空值
                        contents[col] = content
            
            preview_data.append({
                'part_number': part_number,
                'contents': contents,
                'status': status
            })

        return JsonResponse({
            'status': 'success',
            'data': preview_data
        })

    except Exception as e:
        return JsonResponse({
            'status': 'error',
            'message': f'Preview failed: {str(e)}'
        })

@require_POST
@transaction.atomic
def import_excel(request):
    try:
        excel_file = request.FILES.get('excel_file')
        if not excel_file:
            return JsonResponse({'status': 'error', 'message': 'No file uploaded'})

        df = pd.read_excel(excel_file)
        if 'PartNumber' not in df.columns:
            return JsonResponse({
                'status': 'error', 
                'message': 'Missing required column: PartNumber'
            })

        success_count = 0
        error_count = 0
        error_messages = []
        log_details = []  # 用于记录操作日志

        for index, row in df.iterrows():
            try:
                part_number = clean_cell_value(row['PartNumber'])
                
                if not part_number:
                    error_messages.append(f'Row {index + 2}: Empty Part Number')
                    error_count += 1
                    continue

                # 获取Part记录
                part = FFPart.objects.filter(PartNumber=part_number).first()
                if not part:
                    error_messages.append(f'Part not found: {part_number}')
                    error_count += 1
                    continue

                # 处理每个Description列
                for col in df.columns:
                    if col == 'PartNumber':
                        continue
                        
                    content = clean_cell_value(row[col])
                    if not content:  # 跳过空值
                        continue

                    # 获取或创建PartDetailDef
                    part_detail_def, created = luPartDetailDef.objects.get_or_create(
                        Description=col,
                        defaults={'Description': col}
                    )

                    # 记录旧值（用于日志）
                    old_record = FFPartdetail.objects.filter(
                        PartID=part.id,
                        PartDetailDefID=part_detail_def.id
                    ).first()
                    old_content = old_record.Content if old_record else None

                    # 更新或创建PartDetail
                    part_detail, created = FFPartdetail.objects.update_or_create(
                        PartID=part.id,
                        PartDetailDefID=part_detail_def.id,
                        defaults={'Content': content}
                    )

                    # 记录变更
                    log_details.append({
                        'PartNumber': part_number,
                        'Description': col,
                        'OldContent': old_content,
                        'NewContent': content,
                        'Action': 'CREATE' if created else 'UPDATE'
                    })

                success_count += 1

            except Exception as e:
                error_count += 1
                error_messages.append(f'Error processing row {index + 2}: {str(e)}')

        # 记录操作日志
        if log_details:
            OperationLog.objects.create(
                username=request.user.username,
                operation='IMPORT_EXCEL',
                table_name='FFPartdetail',
                details=json.dumps(log_details, ensure_ascii=False)
            )

        message = f'Import completed. Success: {success_count}, Errors: {error_count}'
        if error_messages:
            message += f'\nErrors:\n' + '\n'.join(error_messages[:10])
            if len(error_messages) > 10:
                message += f'\n... and {len(error_messages) - 10} more errors'

        return JsonResponse({
            'status': 'success' if success_count > 0 else 'error',
            'message': message
        })

    except Exception as e:
        return JsonResponse({
            'status': 'error',
            'message': f'Import failed: {str(e)}'
        })

# 添加到urls.py
urlpatterns = [
    # ... 现有的URL patterns ...
    path('partdetail/import-excel/', import_excel, name='import_excel'),
    path('partdetail/download-template/', download_template, name='download_template'),
    path('ffpartdetail/preview-excel/', preview_excel, name='preview_excel'),
]


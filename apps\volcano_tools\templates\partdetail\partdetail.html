{% extends "base2.html" %}
{% load static %}
{% load crispy_forms_tags %}

{% block extra_css %}
<!-- 确保 CSS 文件按正确顺序加载 -->
<!-- <link rel="stylesheet" href="{% static 'plugins/select2/select2.min.css' %}">
<link rel="stylesheet" href="{% static 'plugins/datatables/jquery.dataTables.min.css' %}">
<link rel="stylesheet" href="{% static 'js/plugins/layer/skin/layer.css' %}"> -->

<style>
    .part-detail-container {
        max-width: 1200px;
        margin: 2rem auto;
        padding: 2rem;
        background: #ffffff;
        border-radius: 20px;
        box-shadow: 0 2px 10px rgba(0,0,0,0.05);
    }

    .page-title {
        font-size: 2rem;
        font-weight: 500;
        color: #1d1d1f;
        margin-bottom: 2rem;
        text-align: center;
    }

    .form-section {
        background: #f5f5f7;
        border-radius: 16px;
        padding: 2rem;
        margin-bottom: 2rem;
    }

    .input-group {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
        gap: 1.5rem;
        margin-bottom: 1.5rem;
    }

    .form-group {
        margin-bottom: 1.5rem;
    }

    .input-area {
        background: #ffffff;
        border-radius: 12px;
        border: 1px solid #d2d2d7;
        padding: 1rem;
        transition: all 0.3s ease;
    }

    .input-area:focus-within {
        border-color: #0066cc;
        box-shadow: 0 0 0 4px rgba(0,102,204,0.1);
    }

    textarea, select {
        width: 100%;
        border: none;
        resize: vertical;
        min-height: 100px;
        background: transparent;
    }

    select {
        min-height: auto;
        height: 38px;
    }

    textarea:focus, select:focus {
        outline: none;
    }

    .select2-container {
        width: 100% !important;
        z-index: 1000;
    }

    .select2-dropdown {
        border-radius: 12px;
        border: 1px solid #d2d2d7;
        box-shadow: 0 4px 12px rgba(0,0,0,0.1);
    }

    .select2-container--default .select2-selection--single {
        border: none;
        background: transparent;
        height: 38px;
    }

    .select2-container--default .select2-selection--single .select2-selection__rendered {
        line-height: 38px;
        padding-left: 0;
    }

    .select2-container--default .select2-search--dropdown .select2-search__field {
        border-radius: 6px;
        border: 1px solid #d2d2d7;
        padding: 6px 12px;
    }

    .select2-container--default .select2-results__option--highlighted[aria-selected] {
        background-color: #0066cc;
    }

    .action-buttons {
        display: flex;
        gap: 1rem;
        justify-content: center;
        margin: 2rem 0;
        position: relative;
        z-index: 1001;
    }

    .btn {
        padding: 0.75rem 1.5rem;
        border-radius: 20px;
        font-weight: 500;
        transition: all 0.2s ease;
        border: none;
        min-width: 120px;
    }

    .btn:hover {
        transform: translateY(-1px);
        box-shadow: 0 4px 12px rgba(0,0,0,0.15);
    }

    .btn-primary { background: #0066cc; }
    .btn-secondary { background: #34c759; }
    .btn-warning { background: #ff9f0a; }
    .btn-info { background: #5856d6; }
    .btn-danger { background: #ff3b30; }

    .results-section {
        margin-top: 3rem;
    }

    .results-title {
        font-size: 1.5rem;
        font-weight: 500;
        color: #1d1d1f;
        margin-bottom: 1.5rem;
    }

    .results-table {
        border-radius: 12px;
        overflow: hidden;
        box-shadow: 0 2px 10px rgba(0,0,0,0.05);
    }

    .table {
        margin-bottom: 0;
    }

    .table thead th {
        background: #f5f5f7;
        border-bottom: none;
        padding: 1rem;
        font-weight: 500;
        color: #1d1d1f;
    }

    .table tbody td {
        padding: 1rem;
        vertical-align: middle;
        border-top: 1px solid #d2d2d7;
    }

    .alert {
        border-radius: 12px;
        margin-bottom: 1rem;
        padding: 1rem 1.5rem;
    }

    .form-text {
        color: #86868b;
        font-size: 0.875rem;
        margin-top: 0.5rem;
    }

    label {
        font-weight: 500;
        color: #1d1d1f;
        margin-bottom: 0.5rem;
        display: block;
    }

    .delete-btn {
        padding: 0.5rem 1rem;
        font-size: 0.875rem;
    }

    .excel-section {
        background: #ffffff;
        border-radius: 20px;
        padding: 2rem;
        margin-top: 2rem;
        box-shadow: 0 2px 10px rgba(0,0,0,0.05);
    }

    .section-title {
        font-size: 1.5rem;
        color: #1d1d1f;
        margin-bottom: 1.5rem;
    }

    .excel-import-container {
        background: #f5f5f7;
        border-radius: 16px;
        padding: 2rem;
    }

    .excel-controls {
        display: grid;
        gap: 1.5rem;
    }

    .excel-buttons {
        display: flex;
        gap: 1rem;
        align-items: center;
    }

    .preview-section {
        margin-top: 2rem;
        background: #ffffff;
        border-radius: 12px;
        padding: 1.5rem;
    }

    .table-responsive {
        margin-top: 1rem;
    }

    .status-pending {
        color: #ff9500;
    }

    .status-error {
        color: #ff3b30;
    }

    .status-ok {
        color: #34c759;
    }
</style>
{% endblock %}

{% block content %}
<div class="part-detail-container">
    <h1 class="page-title">Part Detail Management</h1>


    <!-- Excel导入部分 -->
    <div class="excel-section">
        <h2 class="section-title">Excel Import</h2>
        <div class="excel-import-container">
            <div class="excel-controls">
                <div class="form-group">
                    <label for="excelFile">Select Excel File</label>
                    <div class="input-area">
                        <input type="file" id="excelFile" class="form-control" accept=".xlsx,.xls">
                    </div>
                    <small class="form-text text-muted">Support .xlsx/.xls files</small>
                </div>
                <div class="excel-buttons">
                    <button type="button" id="previewExcel" class="btn btn-primary">
                        <i class="fas fa-eye"></i> Preview Data
                    </button>
                    <button type="button" id="importExcel" class="btn btn-success" disabled>
                        <i class="fas fa-file-import"></i> Confirm Import
                    </button>
                    <a href="{% url 'volcano_tools:download_template' %}" class="btn btn-outline-secondary">
                        <i class="fas fa-download"></i> Download Template
                    </a>
                </div>
            </div>

            <!-- 预览数据表格 -->
            <div id="previewTable" class="preview-section" style="display: none;">
                <h3>Preview Data</h3>
                <div class="table-responsive">
                    <table class="table table-bordered table-hover">
                        <thead>
                            <tr>
                                <th>Part Number</th>
                                <th>Status</th>
                                <th>Contents</th>
                            </tr>
                        </thead>
                        <tbody id="previewBody">
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>

    <form method="post" novalidate>
        {% csrf_token %}
        <div class="form-section">
            <div class="input-group">
                <div class="form-group">
                    <label for="{{ form.part_numbers.id_for_label }}">{{ form.part_numbers.label }}</label>
                    <div class="input-area">
                        {{ form.part_numbers }}
                    </div>
                </div>
                <div class="form-group">
                    <label for="{{ form.content.id_for_label }}">{{ form.content.label }}</label>
                    <div class="input-area">
                        {{ form.content }}
                    </div>
                </div>
            </div>

            <div class="form-group">
                <label for="{{ form.part_detail_def_id.id_for_label }}">{{ form.part_detail_def_id.label }}</label>
                <div class="input-area">
                    {{ form.part_detail_def_id }}
                </div>
                {% if form.part_detail_def_id.errors %}
                    <div class="alert alert-danger">
                        {{ form.part_detail_def_id.errors }}
                    </div>
                {% endif %}
            </div>
        </div>

        <div class="action-buttons">
            <button type="submit" class="btn btn-primary" name="action" value="search">Search</button>
            <button type="submit" class="btn btn-secondary" name="action" value="insert">Insert</button>
            <button type="submit" class="btn btn-warning" name="action" value="update">Update</button>
            <button type="submit" class="btn btn-info" name="action" value="reset">Reset</button>
        </div>
    </form>

    <!-- 分隔线 -->

    {% if messages %}
        {% for message in messages %}
            <div class="alert alert-{{ message.tags }}" role="alert">
                {{ message }}
            </div>
        {% endfor %}
    {% endif %}

    {% if results %}
        <div class="results-section">
            <h2 class="results-title">Search Results</h2>
            <div class="results-table">
                <table class="table">
                    <thead>
                        <tr>
                            <th>ID</th>
                            <th>Part Number</th>
                            <th>Content</th>
                            <th>PartDetailDefID</th>
                            <th>Action</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for result in results %}
                            <tr>
                                <td>{{ result.id }}</td>
                                <td>{{ result.PartNumber }}</td>
                                <td>{{ result.Content }}</td>
                                <td>{{ result.PartDetailDefID }}</td>
                                <td>
                                    <button class="btn btn-danger btn-sm delete-btn" data-id="{{ result.id }}">
                                        Delete
                                    </button>
                                </td>
                            </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
        </div>
    {% elif request.method == 'POST' %}
        <div class="alert alert-info">No results found.</div>
    {% endif %}
    <hr class="my-5">

    

    
</div>

<script src="{% static 'js/moment.js' %}"></script>
<script>
jQuery(function($) {
    // 修改Select2初始化
    $('#id_part_detail_def_id').select2({
        placeholder: "Select an option",
        allowClear: true,
        dropdownParent: $('body'),
        width: '100%',
        // 添加搜索相关配置
        minimumInputLength: 0,  // 允许空输入就显示所有选项
        matcher: function(params, data) {
            // 如果没有搜索词，返回所有数据
            if ($.trim(params.term) === '') {
                return data;
            }

            // 如果没有数据，返回null
            if (typeof data.text === 'undefined') {
                return null;
            }

            // 执行搜索匹配
            if (data.text.toLowerCase().indexOf(params.term.toLowerCase()) > -1) {
                return data;
            }

            // 如果没有匹配，返回null
            return null;
        }
    });

    // 当Select2打开时，确保它显示在其他元素之上
    $('#id_part_detail_def_id').on('select2:open', function() {
        $('.select2-dropdown').css('z-index', 9999);
    });

    $('#previewExcel').click(function() {
        var fileInput = $('#excelFile')[0];
        if (fileInput.files.length === 0) {
            layer.alert('Please select an Excel file', {icon: 2});
            return;
        }

        var formData = new FormData();
        formData.append('excel_file', fileInput.files[0]);
        formData.append('csrfmiddlewaretoken', '{{ csrf_token }}');

        layer.load(2);
        $.ajax({
            url: "{% url 'volcano_tools:preview_excel' %}",
            type: 'POST',
            data: formData,
            processData: false,
            contentType: false,
            success: function(response) {
                layer.closeAll('loading');
                if (response.status === 'success') {
                    displayPreviewData(response.data);
                    $('#importExcel').prop('disabled', false);
                } else {
                    layer.alert(response.message, {icon: 2});
                }
            },
            error: function(xhr, errmsg, err) {
                layer.closeAll('loading');
                layer.alert('Preview failed: ' + errmsg, {icon: 2});
            }
        });
    });

    function displayPreviewData(data) {
        var tbody = $('#previewBody');
        tbody.empty();
        
        data.forEach(function(row) {
            var statusClass = row.status === 'OK' ? 'status-ok' : 
                            row.status.startsWith('Error') ? 'status-error' : 'status-pending';
            
            var contentsHtml = '<ul style="list-style: none; padding: 0;">';
            for (var desc in row.contents) {
                if (row.contents[desc]) {
                    contentsHtml += `<li><strong>${desc}:</strong> ${row.contents[desc]}</li>`;
                }
            }
            contentsHtml += '</ul>';
            
            tbody.append(`
                <tr>
                    <td>${row.part_number}</td>
                    <td class="${statusClass}">${row.status}</td>
                    <td>${contentsHtml}</td>
                </tr>
            `);
        });

        $('#previewTable').show();
    }

    $('#importExcel').click(function() {
        if (!confirm('确认导入数据?')) {
            return;
        }

        var fileInput = $('#excelFile')[0];
        var formData = new FormData();
        formData.append('excel_file', fileInput.files[0]);
        formData.append('csrfmiddlewaretoken', '{{ csrf_token }}');

        layer.load(2);
        $.ajax({
            url: "{% url 'volcano_tools:import_excel' %}",
            type: 'POST',
            data: formData,
            processData: false,
            contentType: false,
            success: function(response) {
                layer.closeAll('loading');
                if (response.status === 'success') {
                    layer.alert(response.message, {icon: 1}, function() {
                        $('#excelFile').val('');
                        $('#previewTable').hide();
                        $('#importExcel').prop('disabled', true);
                        // 刷新搜索结果
                        $("button[name='action'][value='search']").click();
                    });
                } else {
                    layer.alert(response.message, {icon: 2});
                }
            },
            error: function(xhr, errmsg, err) {
                layer.closeAll('loading');
                layer.alert('Import failed: ' + errmsg, {icon: 2});
            }
        });
    });
});
</script>

{% endblock %}

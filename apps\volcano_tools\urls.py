from django.urls import path, include
from django.contrib import admin
from django.conf import settings
from django.conf.urls.static import static
from django.contrib.admin.sites import site

from .views.udtunbindnotmrb_view import unbind_not_mrb
from .views.partdetail_view import (
    ffpartdetail, 
    delete_ffpartdetail, 
    download_template, 
    import_excel,
    preview_excel
)
from .views.udtonholddevice_view import (
    udtonholddevice_view,
    insert_on_hold_devices,
    query_on_hold_devices,
    update_on_hold_devices
)
from .views.ffunit_view import (
    ffunit_query_view,
    query_unit_states,
    get_unit_states,
    update_unit_states,
    get_client_info
)
from .views.ffProductionOrder_view import production_order_query
from .views.udtMismatchLine import UdtMismatchLineView
from .views.po_control_view import po_control, update_status
from .views.udtscarpsnlist_view import udtscarpsnlist_view, insert_scrap_sn, query_scrap_sn
from . import views

app_name = 'volcano_tools'

urlpatterns = [
    path('unbind_not_mrb/', unbind_not_mrb, name='unbind_not_mrb'),
    path('ffpartdetail/', site.admin_view(ffpartdetail), name='ffpartdetail'),
    path('ffpartdetail/delete/<int:ffpartdetail_id>/', delete_ffpartdetail, name='ffpartdetail_delete'),
    path('ffpartdetail/download-template/', download_template, name='download_template'),
    path('ffpartdetail/import-excel/', import_excel, name='import_excel'),
    path('ffpartdetail/preview-excel/', preview_excel, name='preview_excel'),
    path('udtonholddevice/', udtonholddevice_view, name='udtonholddevice'),
    path('insert_on_hold_devices/', insert_on_hold_devices, name='insert_on_hold_devices'),
    path('query_on_hold_devices/', query_on_hold_devices, name='query_on_hold_devices'),
    path('update_on_hold_devices/', update_on_hold_devices, name='update_on_hold_devices'),
    path('ffunit/query/', ffunit_query_view, name='ffunit_query_view'),
    path('ffunit/query/states/', query_unit_states, name='query_unit_states'),
    path('production-order/', production_order_query, name='production_order_query'),
    path('get-unit-states/', get_unit_states, name='get_unit_states'),
    path('update-unit-states/', update_unit_states, name='update_unit_states'),
    path('get-client-info/', get_client_info, name='get_client_info'),
    
    # MismatchLine URLs
    path('mismatch-line/', UdtMismatchLineView.as_view(), name='mismatch_line_list'),
    path('mismatch-line/data/', UdtMismatchLineView.as_view(http_method_names=['post']), name='mismatch_data'),
    path('mismatch-line/batch-query/', UdtMismatchLineView.as_view(http_method_names=['post']), name='batch_query'),
    path('mismatch-line/batch-insert/', UdtMismatchLineView.as_view(http_method_names=['post']), name='batch_insert'),
    path('po-control/', po_control, name='po_control'),
    path('po-control/update-status/', update_status, name='update_status'),
    path('udtscarpsnlist/', udtscarpsnlist_view, name='udtscarpsnlist'),
    path('udtscarpsnlist/insert/', insert_scrap_sn, name='insert_scrap_sn'),
    path('udtscarpsnlist/query/', query_scrap_sn, name='query_scrap_sn'),
    
]

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{{ page_title }} - Enhanced</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/echarts/5.4.3/echarts.min.js"></script>
    <style>
        /* Custom styles for ECharts if needed, but prefer Tailwind */
        body {
            font-family: 'Inter', sans-serif; /* Using Inter, a common Tailwind font */
        }
        .chart {
            min-height: 450px; /* Ensure charts have enough space */
        }
        /* Style for the select dropdown to match the dark theme */
        select {
            appearance: none; /* Remove default arrow */
            background-image: url('data:image/svg+xml;charset=US-ASCII,%3Csvg%20xmlns%3D%22http%3A//www.w3.org/2000/svg%22%20width%3D%22292.4%22%20height%3D%22292.4%22%3E%3Cpath%20fill%3D%22%23CBD5E0%22%20d%3D%22M287%2069.4a17.6%2017.6%200%200%200-13-5.4H18.4c-5%200-9.3%201.8-12.9%205.4A17.6%2017.6%200%200%200%200%2082.2c0%205%201.8%209.3%205.4%2012.9l128%20127.9c3.6%203.6%207.8%205.4%2012.8%205.4s9.2-1.8%2012.8-5.4L287%2095c3.5-3.5%205.4-7.8%205.4-12.8%200-5-1.9-9.4-5.4-12.8z%22/%3E%3C/svg%3E');
            background-repeat: no-repeat;
            background-position: right 0.7rem center;
            background-size: 0.65em auto;
            padding-right: 2.5rem; /* Make space for custom arrow */
        }
    </style>
</head>
<body class="bg-slate-900 text-slate-200 p-4 md:p-8">
    <div class="dashboard-container max-w-7xl mx-auto bg-slate-800 shadow-2xl rounded-lg p-6 md:p-8">
        <div class="dashboard-header text-center mb-8 pb-4 border-b border-slate-700">
            <h1 class="text-3xl md:text-4xl font-bold text-sky-400">{{ page_title }}</h1>
        </div>

        {% if error_message %}
        <div class="error-message bg-red-700 border border-red-500 text-white px-4 py-3 rounded-md relative mb-6 text-center" role="alert">
            <strong class="font-bold">Error:</strong>
            <span class="block sm:inline">{{ error_message }}</span>
        </div>
        {% else %}

        <div class="controls flex flex-col sm:flex-row justify-center items-center mb-8 gap-4 p-4 bg-slate-700/50 rounded-md">
            <div>
                <label for="project-filter" class="block text-sm font-medium text-slate-300 mb-1">Filter by Project:</label>
                <select id="project-filter" class="bg-slate-700 border border-slate-600 text-slate-200 text-sm rounded-md focus:ring-sky-500 focus:border-sky-500 block w-full p-2.5">
                    <option value="all">All Projects</option>
                    </select>
            </div>
        </div>

        <div class="chart-row grid grid-cols-1 gap-8 mb-8">
            <div class="chart-box bg-slate-800 p-6 rounded-lg shadow-xl border border-slate-700">
                <div class="chart-title text-xl font-semibold text-center mb-4 text-sky-300">ORT/DV Quantity by Month</div>
                <div id="qty-chart" class="chart w-full h-[40vh]"></div>
            </div>
        </div>

        <div class="chart-row grid grid-cols-1 gap-8">
            <div class="chart-box bg-slate-800 p-6 rounded-lg shadow-xl border border-slate-700">
                <div class="chart-title text-xl font-semibold text-center mb-4 text-sky-300">ORT/DV Unit Cost by Month</div>
                <div id="cost-chart" class="chart w-full h-[40vh]"></div>
            </div>
        </div>

        <div id="qty-chart-data" style="display: none;">{{ qty_chart_data|safe }}</div>
        <div id="cost-chart-data" style="display: none;">{{ cost_chart_data|safe }}</div>

        <script>
            document.addEventListener('DOMContentLoaded', function() {
                // Default ECharts theme settings for dark mode
                const defaultTheme = {
                    darkMode: true,
                    color: [ // A more modern and distinct color palette
                        '#5470c6', '#91cc75', '#fac858', '#ee6666', '#73c0de',
                        '#3ba272', '#fc8452', '#9a60b4', '#ea7ccc'
                    ],
                    backgroundColor: 'transparent', // Use container background
                    textStyle: {
                        fontFamily: 'Inter, sans-serif',
                        color: '#e0e0e0' // Light gray for text
                    },
                    title: {
                        textStyle: {
                            color: '#cbd5e1', // Lighter title text (slate-300)
                            fontSize: 18,
                            fontWeight: 'bold'
                        },
                        subtextStyle: {
                            color: '#94a3b8' // (slate-400)
                        }
                    },
                    legend: {
                        textStyle: {
                            color: '#cbd5e1'
                        },
                        inactiveColor: '#64748b' // (slate-500)
                    },
                    xAxis: {
                        axisLine: {
                            lineStyle: {
                                color: '#475569' // (slate-600)
                            }
                        },
                        axisLabel: {
                            color: '#94a3b8' // (slate-400)
                        },
                        splitLine: {
                            show: false // Hide vertical grid lines for cleaner look
                        }
                    },
                    yAxis: {
                        axisLine: {
                            lineStyle: {
                                color: '#475569'
                            }
                        },
                        axisLabel: {
                            color: '#94a3b8'
                        },
                        splitLine: {
                            lineStyle: {
                                color: '#334155', // (slate-700) - subtle grid lines
                                type: 'dashed'
                            }
                        }
                    },
                    tooltip: {
                        backgroundColor: 'rgba(30, 41, 59, 0.9)', // slate-800 with opacity
                        borderColor: '#475569', // slate-600
                        textStyle: {
                            color: '#e2e8f0' // slate-200
                        },
                        axisPointer: {
                            lineStyle: {
                                color: '#64748b' // (slate-500)
                            },
                            crossStyle: {
                                color: '#64748b'
                            },
                            shadowStyle: { // For shadow type axisPointer
                                color: 'rgba(100, 116, 139, 0.2)' // (slate-500 with opacity)
                            }
                        }
                    },
                    dataZoom: {
                        textStyle: { color: '#94a3b8' },
                        borderColor: '#475569',
                        handleIcon: 'path://M10.7,11.9v-1.3H9.3v1.3c-4.9,0.3-8.8,4.4-8.8,9.4c0,5,3.9,9.1,8.8,9.4v1.3h1.3v-1.3c4.9-0.3,8.8-4.4,8.8-9.4C19.5,16.3,15.6,12.2,10.7,11.9z M13.3,24.4H6.7V23h6.6V24.4z M13.3,19.6H6.7v-1.4h6.6V19.6z',
                        handleStyle: {
                            color: '#64748b',
                            shadowBlur: 3,
                            shadowColor: 'rgba(0, 0, 0, 0.6)',
                            shadowOffsetX: 2,
                            shadowOffsetY: 2
                        },
                        brushStyle: {
                            color: 'rgba(100, 116, 139, 0.3)' // (slate-500 with opacity)
                        }
                    },
                    toolbox: {
                        iconStyle: {
                            borderColor: '#94a3b8'
                        },
                        emphasis: {
                            iconStyle: {
                                borderColor: '#cbd5e1'
                            }
                        }
                    },
                    bar: {
                        itemStyle: {
                            borderRadius: [4, 4, 0, 0] // Rounded top corners for bars
                        }
                    }
                };

                // Initialize ECharts instances with the default theme
                const qtyChart = echarts.init(document.getElementById('qty-chart'), defaultTheme);
                const costChart = echarts.init(document.getElementById('cost-chart'), defaultTheme);

                let qtyChartDataRaw = {};
                let costChartDataRaw = {};

                try {
                    const qtyDataElement = document.getElementById('qty-chart-data');
                    const costDataElement = document.getElementById('cost-chart-data');

                    if (qtyDataElement && qtyDataElement.textContent) {
                        qtyChartDataRaw = JSON.parse(qtyDataElement.textContent || '{}');
                    }
                    if (costDataElement && costDataElement.textContent) {
                        costChartDataRaw = JSON.parse(costDataElement.textContent || '{}');
                    }
                } catch (error) {
                    console.error("Error parsing chart data:", error);
                    const errorDiv = document.createElement('div');
                    errorDiv.className = 'bg-red-700 border border-red-500 text-white px-4 py-3 rounded-md relative mb-6 text-center';
                    errorDiv.textContent = 'Fatal Error: Could not load chart data. ' + error.message;
                    document.querySelector('.dashboard-container').insertBefore(errorDiv, document.querySelector('.controls').nextSibling);
                    // Hide chart containers if data parsing fails
                    document.querySelectorAll('.chart-row').forEach(el => el.style.display = 'none');
                    return; // Stop further execution
                }

                function prepareChartData(rawData, valueType = 'qty') {
                    if (Object.keys(rawData).length === 0) {
                        return { dates: [], projects: [], series: [], legendData: [], xAxisData: [] };
                    }

                    const dates = Object.keys(rawData).sort((a, b) => {
                        const parseDate = (dateStr) => {
                            const [monthStr, yearStr] = dateStr.split(' ');
                            const monthMap = { 'Jan': '01', 'Feb': '02', 'Mar': '03', 'Apr': '04', 'May': '05', 'Jun': '06', 'Jul': '07', 'Aug': '08', 'Sep': '09', 'Oct': '10', 'Nov': '11', 'Dec': '12' };
                            return `${yearStr}-${monthMap[monthStr]}`;
                        };
                        return parseDate(a).localeCompare(parseDate(b));
                    });

                    const allProjects = Array.from(new Set(Object.values(rawData).flatMap(dataOnDate => Object.keys(dataOnDate).filter(key => typeof dataOnDate[key] === 'object')))).sort();
                    const legendData = allProjects;
                    const series = [];
                    const xAxisData = []; // For the combined ORT/DV labels

                    // Define colors for projects - can expand or use ECharts default theme colors
                    const projectColors = defaultTheme.color; // Use colors from the theme

                    dates.forEach(date => {
                        xAxisData.push({ value: `ORT\n${date}`, date: date, type: 'ORT' });
                        xAxisData.push({ value: `DV\n${date}`, date: date, type: 'DV' });
                    });

                    ['ORT', 'DV'].forEach(type => {
                        allProjects.forEach((project, index) => {
                            const data = dates.map(date => {
                                return rawData[date]?.[project]?.[type] || 0;
                            });

                            // We need to interleave 0s for the other type if we use a single x-axis for both ORT and DV
                            const interleavedData = [];
                            dates.forEach(date => {
                                if (type === 'ORT') {
                                    interleavedData.push(rawData[date]?.[project]?.['ORT'] || 0);
                                    interleavedData.push(0); // Placeholder for DV
                                } else { // DV
                                    interleavedData.push(0); // Placeholder for ORT
                                    interleavedData.push(rawData[date]?.[project]?.['DV'] || 0);
                                }
                            });


                            series.push({
                                name: project,
                                type: 'bar',
                                stack: type, // Stack by ORT or DV
                                data: interleavedData,
                                itemStyle: {
                                    color: projectColors[index % projectColors.length] // Cycle through theme colors
                                },
                                emphasis: {
                                    focus: 'series',
                                    itemStyle: {
                                      shadowBlur: 10,
                                      shadowOffsetX: 0,
                                      shadowColor: 'rgba(0, 0, 0, 0.5)'
                                    }
                                },
                                label: { // Show value inside bars
                                    show: true,
                                    position: 'inside',
                                    formatter: (params) => (params.value > 0 ? (valueType === 'cost' ? `$${params.value.toLocaleString()}` : params.value.toLocaleString()) : ''),
                                    color: '#fff',
                                    fontSize: 10,
                                    textShadowBlur: 2,
                                    textShadowColor: 'rgba(0,0,0,0.7)'
                                }
                            });
                        });
                    });
                    
                    // Filter out series that are entirely zero for the current filter
                    const activeSeries = series.filter(s => s.data.some(val => val > 0));
                    const activeLegendData = Array.from(new Set(activeSeries.map(s => s.name)));


                    return {
                        dates: dates, // Original dates for reference
                        projects: allProjects,
                        series: activeSeries,
                        legendData: activeLegendData,
                        xAxisData: xAxisData.map(d => d.value) // The "ORT\nDate", "DV\nDate" labels
                    };
                }


                function getChartOptions(chartTitle, yAxisName, yAxisFormatter, preparedData) {
                    return {
                        title: {
                            text: chartTitle,
                            left: 'center',
                        },
                        tooltip: {
                            trigger: 'axis',
                            axisPointer: { type: 'shadow' },
                            formatter: function(params) {
                                let tooltipContent = '';
                                const dateLabel = params[0].axisValueLabel.split('\n')[1];
                                const typeLabel = params[0].axisValueLabel.split('\n')[0];
                                tooltipContent += `${dateLabel} - ${typeLabel}<br/>`;
                                
                                let totalForStack = 0;
                                params.forEach(param => {
                                    if (param.value > 0) { // Only show if value is greater than 0
                                        tooltipContent += `${param.marker} ${param.seriesName}: ${yAxisFormatter === '${value}' ? '$' : ''}${param.value.toLocaleString()}<br/>`;
                                        totalForStack += param.value;
                                    }
                                });
                                if (params.length > 0) {
                                     tooltipContent += `<b>Total: ${yAxisFormatter === '${value}' ? '$' : ''}${totalForStack.toLocaleString()}</b>`;
                                }
                                return tooltipContent;
                            }
                        },
                        legend: {
                            data: preparedData.legendData,
                            bottom: 10,
                            type: 'scroll', // In case of many projects
                        },
                        grid: {
                            left: '1%',
                            right: '1%',
                            bottom: '15%', // Adjust for legend and x-axis labels
                            top: '15%', // Adjust for title
                            containLabel: true
                        },
                        xAxis: [
                            { // Primary X-axis for ORT/DV labels
                                type: 'category',
                                data: preparedData.xAxisData,
                                axisLabel: {
                                    interval: 0, // Show all labels (ORT/DV)
                                    formatter: function(value) { return value.split('\n')[0]; }, // Show only ORT or DV
                                    fontSize: 10,
                                },
                                axisTick: { alignWithLabel: true },
                            },
                            { // Secondary X-axis for Date labels, centered under pairs
                                type: 'category',
                                data: preparedData.xAxisData,
                                position: 'bottom',
                                offset: 25, // Space between ORT/DV and Date labels
                                axisLine: { show: false },
                                axisTick: { show: false, length: 30, alignWithLabel: true, interval: 1 },
                                axisLabel: {
                                    interval: (index, value) => index % 2 === 0, // Show only for ORT (first of pair)
                                    formatter: function(value) { return value.split('\n')[1]; }, // Show only Date
                                    fontSize: 11,
                                    fontWeight: 'bold',
                                    color: '#a0aec0' // Lighter color for date
                                }
                            }
                        ],
                        yAxis: {
                            type: 'value',
                            name: yAxisName,
                            nameTextStyle: { fontSize: 12, padding: [0, 0, 0, yAxisName.length > 10 ? 50 : 30] }, // Adjust padding for long names
                            axisLabel: {
                                formatter: yAxisFormatter === '${value}' ? '${value}' : function(value){ return value.toLocaleString(); },
                                fontSize: 10
                            }
                        },
                        series: preparedData.series,
                        dataZoom: [ // Add dataZoom for better navigation with many dates
                            {
                                type: 'slider',
                                xAxisIndex: [0, 1], // Control both x-axes
                                startValue: preparedData.xAxisData.length > 20 ? preparedData.xAxisData.length - 20 : 0, // Show last 10 pairs (20 items) or all
                                endValue: preparedData.xAxisData.length -1,
                                bottom: 50, // Position above legend
                                height: 20,
                                filterMode: 'filter' // 'filter' is better for bar charts
                            },
                            {
                                type: 'inside',
                                xAxisIndex: [0, 1],
                                filterMode: 'filter'
                            }
                        ],
                        toolbox: { // Add toolbox for utility
                            show: true,
                            orient: 'vertical',
                            left: 'right',
                            top: 'center',
                            feature: {
                                mark: { show: true },
                                dataView: { show: true, readOnly: false, title: 'Data View', lang: ['Data View', 'Close', 'Refresh'] },
                                magicType: { show: true, type: ['line', 'bar', 'stack'], title: {line: 'Line', bar: 'Bar', stack: 'Stack'} },
                                restore: { show: true, title: 'Restore' },
                                saveAsImage: { show: true, title: 'Save Image', backgroundColor: '#1f2937' } // bg for saved image
                            }
                        },
                    };
                }

                const projectFilter = document.getElementById('project-filter');
                
                // Populate project filter
                const allQtyProjects = Array.from(new Set(Object.values(qtyChartDataRaw).flatMap(dataOnDate => Object.keys(dataOnDate).filter(key => typeof dataOnDate[key] === 'object')))).sort();
                projectFilter.innerHTML = '<option value="all">All Projects</option>'; // Clear existing
                allQtyProjects.forEach(project => {
                    const option = document.createElement('option');
                    option.value = project;
                    option.textContent = project;
                    projectFilter.appendChild(option);
                });


                function updateChartsByProject(selectedProject) {
                    let filteredQtyDataRaw = {};
                    let filteredCostDataRaw = {};

                    if (selectedProject === 'all') {
                        filteredQtyDataRaw = JSON.parse(JSON.stringify(qtyChartDataRaw)); // Deep copy
                        filteredCostDataRaw = JSON.parse(JSON.stringify(costChartDataRaw));
                    } else {
                        for (const date in qtyChartDataRaw) {
                            if (qtyChartDataRaw[date][selectedProject]) {
                                filteredQtyDataRaw[date] = { [selectedProject]: qtyChartDataRaw[date][selectedProject] };
                            }
                        }
                        for (const date in costChartDataRaw) {
                            if (costChartDataRaw[date][selectedProject]) {
                                filteredCostDataRaw[date] = { [selectedProject]: costChartDataRaw[date][selectedProject] };
                            }
                        }
                    }
                    
                    const preparedQtyData = prepareChartData(filteredQtyDataRaw, 'qty');
                    const preparedCostData = prepareChartData(filteredCostDataRaw, 'cost');

                    const qtyChartTitle = `ORT/DV Quantity (${selectedProject === 'all' ? 'All Projects' : selectedProject})`;
                    const costChartTitle = `ORT/DV Unit Cost (${selectedProject === 'all' ? 'All Projects' : selectedProject})`;

                    qtyChart.setOption(getChartOptions(qtyChartTitle, 'Quantity', '{value}', preparedQtyData), true);
                    costChart.setOption(getChartOptions(costChartTitle, 'Unit Cost ($)', '${value}', preparedCostData), true);
                }

                projectFilter.addEventListener('change', function() {
                    updateChartsByProject(this.value);
                });

                // Initial chart render
                updateChartsByProject('all');

                // Handle window resize
                window.addEventListener('resize', function() {
                    if (qtyChart && typeof qtyChart.resize === 'function') {
                        qtyChart.resize();
                    }
                    if (costChart && typeof costChart.resize === 'function') {
                        costChart.resize();
                    }
                });
            });
        </script>
        {% endif %}
    </div>
</body>
</html>

{% load static %}
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{{ page_title }} - Enhanced</title>
    <link rel="stylesheet" href="{% static 'bootstrap/css/bootstrap.min.css' %}">
    <script src="{% static 'js/echarts.min.js' %}"></script>
    <style>
        body {
            background-color: #000;
            color: #fdfefe;
        }
        .chart {
            min-height: 450px;
        }
        select {
            appearance: none;
            background-image: url('data:image/svg+xml;charset=US-ASCII,%3Csvg%20xmlns%3D%22http%3A//www.w3.org/2000/svg%22%20width%3D%22292.4%22%20height%3D%22292.4%22%3E%3Cpath%20fill%3D%22%23CBD5E0%22%20d%3D%22M287%2069.4a17.6%2017.6%200%200%200-13-5.4H18.4c-5%200-9.3%201.8-12.9%205.4A17.6%2017.6%200%200%200%200%2082.2c0%205%201.8%209.3%205.4%2012.9l128%20127.9c3.6%203.6%207.8%205.4%2012.8%205.4s9.2-1.8%2012.8-5.4L287%2095c3.5-3.5%205.4-7.8%205.4-12.8%200-5-1.9-9.4-5.4-12.8z%22/%3E%3C/svg%3E');
            background-repeat: no-repeat;
            background-position: right 0.7rem center;
            background-size: 0.65em auto;
            padding-right: 2.5rem;
        }
    </style>
</head>
<body style="background-color: black; color: #fdfefe;">
    <div class="dashboard-container" style="max-width: 7xl; margin-left: auto; margin-right: auto; background-color: #1f2937; box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04); border-radius: 0.5rem; padding: 1.5rem 2rem;">
        <div class="dashboard-header" style="text-align: center; margin-bottom: 2rem; padding-bottom: 1rem; border-bottom: 1px solid #475569;">
            <h1 style="font-size: 2.25rem; line-height: 2.5rem; font-weight: 700; color: #38bdf8;">{{ page_title }}</h1>
        </div>

        {% if error_message %}
        <div class="error-message" style="background-color: #b91c1c; border: 1px solid #ef4444; color: white; padding: 1rem 1rem; border-radius: 0.375rem; position: relative; margin-bottom: 1.5rem; text-align: center;" role="alert">
            <strong style="font-weight: 700;">Error:</strong>
            <span style="display: block;">{{ error_message }}</span>
        </div>
        {% else %}


        <div class="chart-row" style="display: grid; grid-template-columns: repeat(1, minmax(0, 1fr)); gap: 2rem; margin-bottom: 2rem;">
            <div class="chart-box" style="background-color: #1f2937; padding: 1.5rem; border-radius: 0.5rem; box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04); border: 1px solid #475569;">
                <div id="qty-chart" class="chart" style="width: 100%; height: 40vh;"></div>
            </div>
        </div>

        <div class="chart-row" style="display: grid; grid-template-columns: repeat(1, minmax(0, 1fr)); gap: 2rem;">
            <div class="chart-box" style="background-color: #1f2937; padding: 1.5rem; border-radius: 0.5rem; box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04); border: 1px solid #475569;">
                <div id="cost-chart" class="chart" style="width: 100%; height: 40vh;"></div>
            </div>
        </div>

        <div id="qty-chart-data" style="display: none;">{{ qty_chart_data|safe }}</div>
        <div id="cost-chart-data" style="display: none;">{{ cost_chart_data|safe }}</div>

        <script>
            function hexToRgba(hex, alpha) {
                let c = hex.replace('#', '');
                if (c.length === 3) c = c.split('').map(x => x + x).join('');
                const num = parseInt(c, 16);
                return `rgba(${(num >> 16) & 255},${(num >> 8) & 255},${num & 255},${alpha})`;
            }

            document.addEventListener('DOMContentLoaded', function() {
                const defaultTheme = 'dark';
                const qtyChart = echarts.init(document.getElementById('qty-chart'), defaultTheme);
                const costChart = echarts.init(document.getElementById('cost-chart'), defaultTheme);

                // 自定义配色，去除“猪肝色”#7B3F00
                const customColorArray = [
                    '#5470C6', '#91CC75', '#FAC858', '#EE6666', '#73C0DE', '#3BA272',
                    '#FC8452', '#9A60B4', '#EA7CCC', '#A16252', '#6E7074', '#BDA29A', '#6E7074'
                ].filter(color => color.toLowerCase() !== '#7b3f00' && color.toLowerCase() !== '#a16252'); // 彻底排除猪肝色

                // 默认配置
                const defaultSettings = {
                    backgroundColor: 'transparent',
                    legend: {
                        bottom: 10,
                        padding: [5, 10]
                    },
                    grid: {
                        left: '1%',
                        right: '1%',
                        bottom: '15%',
                        top: '15%',
                        containLabel: true
                    },
                    toolbox: {
                        show: true,
                        orient: 'vertical',
                        left: 'right',
                        top: 'center',
                        feature: {
                            mark: { show: true },
                            dataView: { show: true, readOnly: false },
                            magicType: { show: true, type: ['line', 'bar', 'stack'] },
                            restore: { show: true },
                            saveAsImage: { show: true }
                        }
                    }
                };


                let qtyChartDataRaw = {};
                let costChartDataRaw = {};

                try {
                    const qtyDataElement = document.getElementById('qty-chart-data');
                    const costDataElement = document.getElementById('cost-chart-data');

                    if (qtyDataElement && qtyDataElement.textContent) {
                        qtyChartDataRaw = JSON.parse(qtyDataElement.textContent || '{}');
                    }
                    if (costDataElement && costDataElement.textContent) {
                        costChartDataRaw = JSON.parse(costDataElement.textContent || '{}');
                    }
                } catch (error) {
                    console.error("Error parsing chart data:", error);
                    const errorDiv = document.createElement('div');
                    errorDiv.className = 'bg-red-700 border border-red-500 text-white px-4 py-3 rounded-md relative mb-6 text-center';
                    errorDiv.textContent = 'Fatal Error: Could not load chart data. ' + error.message;
                    document.querySelector('.dashboard-container').insertBefore(errorDiv, document.querySelector('.controls').nextSibling);
                    document.querySelectorAll('.chart-row').forEach(el => el.style.display = 'none');
                    return;
                }

                function prepareChartData(rawData, valueType = 'qty') {
                    if (Object.keys(rawData).length === 0) {
                        return { dates: [], projects: [], series: [], legendData: [], xAxisData: [] };
                    }

                    const dates = Object.keys(rawData).sort((a, b) => {
                        const parseDate = (dateStr) => {
                            const [monthStr, yearStr] = dateStr.split(' ');
                            const monthMap = { 'Jan': '01', 'Feb': '02', 'Mar': '03', 'Apr': '04', 'May': '05', 'Jun': '06', 'Jul': '07', 'Aug': '08', 'Sep': '09', 'Oct': '10', 'Nov': '11', 'Dec': '12' };
                            return `${yearStr}-${monthMap[monthStr]}`;
                        };
                        return parseDate(a).localeCompare(parseDate(b));
                    });

                    const allProjects = Array.from(new Set(Object.values(rawData).flatMap(dataOnDate => Object.keys(dataOnDate).filter(key => typeof dataOnDate[key] === 'object')))).sort();
                    const legendData = [];
                    const series = [];
                    const xAxisData = dates;
                    const projectColors = defaultTheme.color;

                    ['ORT', 'DV'].forEach(type => {
                        allProjects.forEach((project, projectIndex) => {
                            const data = dates.map(date => {
                                return rawData[date]?.[project]?.[type] || 0;
                            });

                            const seriesName = `${project} - ${type}`;
                            legendData.push(seriesName);

                            series.push({
                                name: seriesName,
                                type: 'bar',
                                stack: type,
                                data: data,
                                itemStyle: Object.assign(
                                    {
                                        opacity: type === 'DV' ? 1 : 1
                                    },
                                    (type === 'DV' && project.toLowerCase() === 'hermes') ? { color: '#48c9b0' } :
                                    (type === 'DV' && project.toLowerCase() === 'kosmos') ? { color: '#c0392b' } :
                                    (type === 'DV' && project.toLowerCase() === 'mono') ? { color: '#884ea0' } :
                                    (type === 'DV' && project.toLowerCase() === 'voyager') ? { color: '#f9e79f' } :
                                    {}
                                ),
                                
                                label: {
                                    show: true,
                                    position: 'inside',
                                    formatter: (params) => (params.value > 0 ? (valueType === 'cost' ? `$${params.value.toLocaleString()}` : params.value.toLocaleString()) : ''),
                                    color: '#fff',
                                    fontSize: 17,
                                    fontWeight: 'bold'
                                }
                            });
                        });
                    });

                    const activeSeries = series.filter(s => s.data.some(val => val > 0));
                    const activeLegendData = Array.from(new Set(activeSeries.map(s => s.name)));

                    return {
                        dates: dates,
                        projects: allProjects,
                        series: activeSeries,
                        legendData: activeLegendData,
                        xAxisData: xAxisData
                    };
                }

                function getChartOptions(chartTitle, yAxisName, yAxisFormatter, preparedData) {
                    return {
                        color: customColorArray,
                        title: {
                            text: chartTitle,
                            left: 'center',
                        },
                        tooltip: {
                            trigger: 'axis',
                            axisPointer: { type: 'shadow' },
                            formatter: function(params) {
                                let tooltipContent = `${params[0].name}<br/>`;
                                let totalORT = 0;
                                let totalDV = 0;
                                let projectValues = {};
        
                                params.forEach(param => {
                                    const [project, type] = param.seriesName.split(' - ');
                                    if (!projectValues[project]) {
                                        projectValues[project] = { ORT: 0, DV: 0 };
                                    }
                                    if (type === 'ORT') {
                                        projectValues[project].ORT += param.value;
                                        totalORT += param.value;
                                    } else if (type === 'DV') {
                                        projectValues[project].DV += param.value;
                                        totalDV += param.value;
                                    }
                                });
        
                                tooltipContent += `<b>ORT:</b><br/>`;
                                Object.keys(projectValues).sort().forEach(project => {
                                    if (projectValues[project].ORT > 0) {
                                        tooltipContent += `${params.find(p => p.seriesName === `${project} - ORT`)?.marker || ''} ${project}: ${yAxisFormatter === '${value}' ? '$' : ''}${projectValues[project].ORT.toLocaleString()}<br/>`;
                                    }
                                });
                                tooltipContent += `Total ORT: ${yAxisFormatter === '${value}' ? '$' : ''}${totalORT.toLocaleString()}<br/><br/>`;
        
                                tooltipContent += `<b>DV:</b><br/>`;
                                Object.keys(projectValues).sort().forEach(project => {
                                    if (projectValues[project].DV > 0) {
                                        tooltipContent += `${params.find(p => p.seriesName === `${project} - DV`)?.marker || ''} ${project}: ${yAxisFormatter === '${value}' ? '$' : ''}${projectValues[project].DV.toLocaleString()}<br/>`;
                                    }
                                });
                                tooltipContent += `Total DV: ${yAxisFormatter === '${value}' ? '$' : ''}${totalDV.toLocaleString()}`;
        
                                return tooltipContent;
                            }
                        },
                        legend: {
                            data: preparedData.legendData,
                            bottom: 10,
                            type: 'scroll',
                            textStyle: {
                                fontSize: 14,
                                fontWeight: 'bold'
                            }
                        },
                        grid: {
                            left: '1%',
                            right: '1%',
                            bottom: '15%',
                            top: '15%',
                            containLabel: true
                        },
                        xAxis: {
                            type: 'category',
                            data: preparedData.xAxisData,
                            axisLabel: {
                                interval: 0,
                                fontSize: 14,
                                fontWeight: 'bold',
                            },
                            axisTick: { alignWithLabel: true },
                        },
                        yAxis: {
                            type: 'value',
                            name: yAxisName,
                            nameTextStyle: { fontSize: 12, padding: [0, 0, 0, yAxisName.length > 10 ? 50 : 30] },
                            axisLabel: {
                                formatter: function(value) {
                                    if (yAxisName === 'Quantity' || yAxisName === 'Unit Cost ($)') {
                                        return value.toLocaleString();
                                    }
                                    return value;
                                },
                                fontSize: 10
                            },
                            axisPointer: {
                                show: true,
                                type: 'shadow',
                                label: {
                                    formatter: function (params) {
                                        if (yAxisName === 'Quantity') {
                                            return `ORT/DV: ${params.value.toLocaleString()}`;
                                        } else if (yAxisName === 'Unit Cost ($)') {
                                            return `ORT/DV: $${params.value.toLocaleString()}`;
                                        }
                                        return params.value;
                                    }
                                }
                            }
                        },
                        series: preparedData.series,
                        dataZoom: [
                            {
                                type: 'slider',
                                xAxisIndex: 0,
                                startValue: preparedData.xAxisData.length > 10 ? preparedData.xAxisData.length - 10 : 0,
                                endValue: preparedData.xAxisData.length -1,
                                bottom: 50,
                                height: 20,
                                filterMode: 'filter'
                            },
                            {
                                type: 'inside',
                                xAxisIndex: 0,
                                filterMode: 'filter'
                            }
                        ],
                        toolbox: {
                            show: true,
                            orient: 'vertical',
                            left: 'right',
                            top: 'center',
                            feature: {
                                mark: { show: true },
                                dataView: { show: true, readOnly: false, title: 'Data View', lang: ['Data View', 'Close', 'Refresh'] },
                                magicType: { show: true, type: ['line', 'bar', 'stack'], title: {line: 'Line', bar: 'Bar', stack: 'Stack'} },
                                restore: { show: true, title: 'Restore' },
                                saveAsImage: { show: true, title: 'Save Image', backgroundColor: '#1f2937' }
                            }
                        },
                    };
                }

                // 直接渲染全部项目数据，无需筛选
                const preparedQtyData = prepareChartData(qtyChartDataRaw, 'qty');
                const preparedCostData = prepareChartData(costChartDataRaw, 'cost');
                qtyChart.setOption(getChartOptions('ORT/DV Quantity', 'Quantity', '{value}', preparedQtyData), true);
                costChart.setOption(getChartOptions('ORT/DV Unit Cost ($)', 'Unit Cost ($)', '${value}', preparedCostData), true);

                window.addEventListener('resize', function() {
                    if (qtyChart && typeof qtyChart.resize === 'function') {
                        qtyChart.resize();
                    }
                    if (costChart && typeof costChart.resize === 'function') {
                        costChart.resize();
                    }
                });
            });
        </script>
        {% endif %}
    </div>
</body>
</html>

{% extends 'base1.html' %}
{% load static %}
<!DOCTYPE html>
<html>
<head>
    <script src="{% static 'js/jquery-3.6.1.min.js' %}"></script>
    <style>
        .fixed-height-container {
            max-height: 400px; /* Adjust this value to your needs */
            overflow-y: auto;
            overflow-x: hidden;
            background-color: #f9f9f9;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 4px;
        }
        .form-row {
            margin-bottom: 1rem;
        }
        .btn-primary, .btn-success {
            margin-right: 10px;
        }
    </style>
</head>
<body>
    {% block content %}
    <div class="bg-light text-dark py-3 mb-4 shadow-sm">
        <div class="container">
            <h3 class="display-6">ID: {{ id }} - {{ Reason }}</h3>
        </div>
    </div>

    <div class="container">
        <nav aria-label="breadcrumb">
            <ol class="breadcrumb">
                <li class="breadcrumb-item"><a href="/volcanoreport/query/">返回首页</a></li>
                <li class="breadcrumb-item active" aria-current="page">ID: {{ id }} - {{ Reason }}</li>
            </ol>
        </nav>

        <form method="post" id="queryForm">
            {% csrf_token %}
            <div class="form-row">
                <div class="form-group col-md-5 fixed-height-container">
                    {{ form.SNTextarea.label_tag }}
                    {{ form.SNTextarea }}
                </div>
                <div class="col-md-2"></div>
                <div class="form-group col-md-5 fixed-height-container">
                    {{ form.PartNumber.label_tag }}
                    {{ form.PartNumber }}
                </div>
            </div>
            <div class="form-row">
                <div class="form-group col-md-5">
                    {{ form.part_family_type.label_tag }}
                    {{ form.part_family_type }}
                </div>
            </div>
            <div class="form-row">
                <div class="form-group col-md-5 fixed-height-container" id="station_type_container">
                    {{ form.station_type_list.label_tag }}
                    {{ form.station_type_list }}
                </div>
                <div class="col-md-2"></div>
                <div class="form-group col-md-5 fixed-height-container" id="line_name_container">
                    {{ form.LineName.label_tag }}
                    {{ form.LineName }}
                </div>
            </div>
            <div class="form-row">
                <div class="form-group col-md-5">
                    {{ form.StartTime.label_tag }}
                    {{ form.StartTime }}
                </div>
                <div class="col-md-2"></div>
                <div class="form-group col-md-5">
                    {{ form.EndTime.label_tag }}
                    {{ form.EndTime }}
                </div>
            </div>
            <div class="form-row">
                <div class="form-group col-md-6">
                    {{ form.MPN.label_tag }}
                    {{ form.MPN }}
                </div>
                <div class="form-group col-md-6">
                    {{ form.LotCode.label_tag }}
                    {{ form.LotCode }}
                </div>
                <div class="form-group col-md-6">
                    {{ form.DateCode.label_tag }}
                    {{ form.DateCode }}
                </div>
                <div class="form-group col-md-6">
                    {{ form.Supplier.label_tag }}
                    {{ form.Supplier }}
                </div>
            </div>
            <div class="form-row">
                <button type="submit" class="btn btn-primary col-md-2">Submit</button>
                <button type="submit" name="action" value="export" class="btn btn-success col-md-2">Export to Excel</button>
            </div>
        </form>
    </div>

    <div id="loadingSpinner" class="spinner-border text-primary" role="status" style="display: none;">
        <span class="sr-only">Loading...</span>
    </div>
    <div class="container">
        {% if form.errors %}
        <div class="alert alert-danger" role="alert">
            <ul>
                {% for field, errors in form.errors.items %}
                    {% for error in errors %}
                        <li>{{ error }}</li>
                    {% endfor %}
                {% endfor %}
            </ul>
        </div>
        {% endif %}

        {% if query_results %}
        <h4>查询结果:</h4>
        <div class="table-responsive">
            <h6>{{ query_counts }} 条记录，此页面最多显示前100条，要全部数据，请导出为Excel。</h6>
            <table class="table table-striped table-bordered table-hover">
                <thead class="thead-dark">
                    <tr>
                        {% for column in columns %}
                        <th>{{ column }}</th>
                        {% endfor %}
                    </tr>
                </thead>
                <tbody>
                    {% for row in query_results %}
                    <tr>
                        {% for cell in row %}
                        <td>{{ cell }}</td>
                        {% endfor %}
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
        {% endif %}
    </div>

    <style>
        .bg-light {
            background-color: #f8f9fa !important;
            color: #343a40;
        }

        .breadcrumb {
            background-color: #f8f9fa;
            padding: 10px 20px;
            border-radius: 0.375rem;
            margin-bottom: 1rem;
        }

        .breadcrumb a {
            text-decoration: none;
            color: #007bff;
        }

        .breadcrumb a:hover {
            text-decoration: underline;
            color: #0056b3;
        }

        .container {
            background-color: #ffffff;
            padding: 20px;
            border-radius: 0.375rem;
            box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
        }

        .table-responsive {
            overflow-x: auto;
        }

        table {
            table-layout: auto;
            white-space: nowrap;
        }

        th, td {
            padding: 10px;
            text-align: left;
            vertical-align: top;
        }

        .btn-primary {
            background-color: #007bff;
            border-color: #007bff;
        }

        .btn-primary:hover {
            background-color: #0056b3;
            border-color: #0056b3;
        }

        .btn-success {
            background-color: #28a745;
            border-color: #28a745;
        }

        .btn-success:hover {
            background-color: #218838;
            border-color: #218838;
        }
    </style>

    {% endblock %}
<script src="{% static 'js/jquery-3.6.1.min.js' %}"></script>
    {% block extra_js %}
    <script>
        $(document).ready(function() {
            $('#id_part_family_type').change(function() {
                var partFamilyTypeId = $(this).val();
                if (partFamilyTypeId) {
                    $.ajax({
                        url: '{% url "volcanoreport:get_station_line_data" %}',
                        data: {
                            'partfamilytypeid': partFamilyTypeId
                        },
                        success: function(data) {
                            var stationTypeContainer = $('#station_type_container');
                            var lineNameContainer = $('#line_name_container');

                            stationTypeContainer.empty();
                            lineNameContainer.empty();

                            data.station_types.forEach(function(stationType) {
                                var checkbox = $('<input>').attr('type', 'checkbox').attr('name', 'station_type_list').val(stationType).addClass('form-check-input');
                                var label = $('<label>').text(stationType).addClass('form-check-label').prepend(checkbox);
                                var div = $('<div>').addClass('form-check').append(label);
                                stationTypeContainer.append(div);
                            });

                            data.line_names.forEach(function(lineName) {
                                var checkbox = $('<input>').attr('type', 'checkbox').attr('name', 'line_name').val(lineName).addClass('form-check-input');
                                var label = $('<label>').text(lineName).addClass('form-check-label').prepend(checkbox);
                                var div = $('<div>').addClass('form-check').append(label);
                                lineNameContainer.append(div);
                            });
                        },
                        error: function(xhr, status, error) {
                            console.error("AJAX Error: ", status, error);
                        }
                    });
                }
            });

            // Show loading spinner on form submit
            $('#queryForm').on('submit', function() {
                $('#loadingSpinner').show();
            });

            // Hide loading spinner on page load
            $(window).on('load', function() {
                $('#loadingSpinner').hide();
            });
        });
    </script>
    {% endblock %}
</body>
</html>

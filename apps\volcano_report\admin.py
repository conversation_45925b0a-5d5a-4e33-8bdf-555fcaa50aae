from django.contrib import admin
from .models import UDTGetQueryCondition, UDTGetQueryConditiondetail, UdtSMTScrapRecord, udtLineOutputConfirmation  
from django.utils.html import format_html
from django.db.models import Q
from datetime import datetime
from import_export import resources
from import_export.admin import ImportExportModelAdmin
from import_export.fields import Field
from import_export.formats.base_formats import XLSX

@admin.register(UDTGetQueryCondition)
class UDTGetQueryConditionAdmin(admin.ModelAdmin):
    list_display = ('ID','Reason', 'UDP')
    list_filter = ('Reason',)
    search_fields = ('Reason','UDP')
    ordering = ('-ID',)


@admin.register(UDTGetQueryConditiondetail)
class UDTGetQueryConditiondetailAdmin(admin.ModelAdmin):
    list_display = ('ID','Reason', 'UDP','SNTextarea',
'PartnumberTextarea',
'Partfamily',
'StationType',
'LineName',
'TimeInput',
'MPN',
'LotCode',
'DateCode',
'Supplier',
'CreateTime',
'Remark'
)
    list_filter = ('Reason','UDP')
    search_fields = ('Reason',)
    ordering = ('-ID',)


class UdtSMTScrapRecordResource(resources.ModelResource):
    def before_import_row(self, row, **kwargs):
        row['creator'] = kwargs['user'].username
    
    class Meta:
        model = UdtSMTScrapRecord
        fields = ('date', 'serialnumber', 'Partnumber', 'reporting_unit_code', 
                 'PONumber', 'reporting_reason', 'recorder', 'Station', 
                  'ResponsibleDepartment')
        export_order = fields
        import_id_fields = ('serialnumber',)
        exclude = ('id',)

@admin.register(UdtSMTScrapRecord)
class UdtSMTScrapRecordAdmin(ImportExportModelAdmin):
    resource_class = UdtSMTScrapRecordResource

    def get_import_resource_kwargs(self, request, *args, **kwargs):
        resource_kwargs = super().get_import_resource_kwargs(request, *args, **kwargs)
        resource_kwargs['user'] = request.user
        return resource_kwargs

    list_display = ['ID', 'date', 'serialnumber', 'Partnumber', 'PONumber', 
                   'reporting_unit_code', 'Station',
                   'ResponsibleDepartment', 'reporting_reason', 'recorder', 
                   'createtime', 'creator']
    
    search_fields = ['serialnumber', 'Partnumber', 'PONumber', 'Station', 
                 'ResponsibleDepartment']
    
    list_filter = ['date', 'reporting_unit_code', 'Station', 'ResponsibleDepartment']
    
    exclude = ('creator',)

    fieldsets = (
        ('基本信息', {
            'fields': ('date', 'serialnumber', 'Partnumber', 'PONumber', 'reporting_unit_code')
        }),
        ('故障信息', {
            'fields': ('Station', 'ResponsibleDepartment', 'reporting_reason')
        }),
        ('其他信息', {
            'fields': ('recorder',)
        }),
    )

    def get_form(self, request, obj=None, **kwargs):
        form = super().get_form(request, obj, **kwargs)
        form.current_user = request.user
        return form

    def save_model(self, request, obj, form, change):
        if not change:  # 如果是新创建的对象
            obj.creator = request.user.username
        super().save_model(request, obj, form, change)
    
    class Media:
        js = ('admin/js/jquery.init.js', 'js/admin_form.js',)

# 添加导出功能的资源类
class UdtLineOutputConfirmationResource(resources.ModelResource):
    class Meta:
        model = udtLineOutputConfirmation
        # 只导出原始字段，不包含计算字段
        fields = (
            'Date', 
            'Line', 
            'Shift', 
            'PartNumber', 
            'Type', 
            'PlanQty', 
            'Output','Gap'
            'StopTime', 
            'StopCount', 
            'IncidentsDetail', 
            'Note',
            'ConfirmedBy', 
            'ConfirmedAt'
        )
        # 使用相同的顺序导出
        export_order = fields
        # 排除 ID 字段
        exclude = ('ID',)

@admin.register(udtLineOutputConfirmation)
class UDTLineOutputConfirmationAdmin(ImportExportModelAdmin):
    resource_class = UdtLineOutputConfirmationResource
    export_formats = [XLSX]  # 使用 XLSX 而不是 XLSX_FORMAT
    
    # 页面显示时计算完成率，但不影响导出  'UPH', 'DTGap',
    list_display = ['Date', 'Line', 'Shift', 'PartNumber', 'Type', 
                    'PlanQty', 'Output', 'Gap', 'get_completion_rate',
                    'StopTime', 'StopCount', 'get_incidents_detail','ConfirmedBy']
    list_filter = ['Date', 'Line', 'Shift', 'Type']
    search_fields = ['Line', 'PartNumber', 'Type', 'IncidentsDetail']
    date_hierarchy = 'Date'
    
    # 禁用添加、删除和修改功能
    def has_add_permission(self, request):
        return False
        
    def has_delete_permission(self, request, obj=None):
        return False
        
    def has_change_permission(self, request, obj=None):
        return False
        
    def has_import_permission(self, request):
        """禁用导入功能"""
        return False

    def get_completion_rate(self, obj):
        """计算并显示完成率"""
        if obj.PlanQty and obj.PlanQty > 0:
            rate = (obj.Output / obj.PlanQty) * 100
            color = 'green' if rate >= 100 else 'red'
            return format_html('<span style="color: {}">{}</span>', 
                             color, round(rate, 1))
        return '-'
    get_completion_rate.short_description = '完成率'
    
    def changelist_view(self, request, extra_context=None):
        """自定义列表视图"""
        response = super().changelist_view(request, extra_context)
        
        try:
            qs = response.context_data['cl'].queryset
        except (AttributeError, KeyError):
            return response
            
        # 计算汇总数据
        from django.db.models import Sum
        metrics = {
            'total_plan': qs.aggregate(total=Sum('PlanQty'))['total'] or 0,
            'total_output': qs.aggregate(total=Sum('Output'))['total'] or 0,
        }
        
        if metrics['total_plan'] > 0:
            completion = (metrics['total_output'] / metrics['total_plan']) * 100
            metrics['total_completion'] = str(round(completion, 1))
        else:
            metrics['total_completion'] = '0'
            
        response.context_data.update(metrics)
        
        return response
    
    def get_queryset(self, request):
        """自定义查询集"""
        qs = super().get_queryset(request)
        
        start_date = request.GET.get('start_date')
        end_date = request.GET.get('end_date')
        line_name = request.GET.get('line_name')
        
        if start_date:
            qs = qs.filter(Date__gte=start_date)
        if end_date:
            qs = qs.filter(Date__lte=end_date)
        if line_name:
            qs = qs.filter(Line=line_name)
            
        return qs
    
    class Media:
        js = ('admin/js/jquery.init.js', 'js/admin_form.js',)

    readonly_fields = ['Gap', 'DTGap', 'ConfirmedAt']
    get_completion_rate.short_description = '完成率'

    def get_incidents_detail(self, obj):
        """格式化显示停线详情"""
        if obj.IncidentsDetail:
            # 如果内容太长，截断显示
            max_length = 50
            detail = obj.IncidentsDetail
            if len(detail) > max_length:
                detail = detail[:max_length] + '...'
            return format_html(
                '<span title="{}">{}</span>',
                obj.IncidentsDetail,  # 完整内容显示在 title 属性
                detail  # 截断的内容显示在页面上
            )
        return '-'
    get_incidents_detail.short_description = '停线详情'
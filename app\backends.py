from django.contrib.auth.backends import ModelBackend
from django.contrib.auth.backends import UserModel
#from my_user_auth.ad_auth import ldap_auth




from ldap3 import Server, Connection, ALL, SUBTREE, ServerPool
#域服务器IP，辅域就行了
LDAP_SERVER_POOL = ["***********"]
#LDAP服务端口
LDAP_SERVER_PORT = 389

def ldap_auth(username, password):
    ldap_server_pool = ServerPool(LDAP_SERVER_POOL)
    #用户以************的形式录入
    ad_user=f"""{username}@abc.net"""
    conn = Connection(ldap_server_pool, user=ad_user, password=password, check_names=True, lazy=False, raise_exceptions=False)
    
    conn.open()
    conn.bind()
    if conn.result["result"]==0:
        conn.unbind()
        return True
    else:
        conn.unbind()
        return False

class UserBackend(ModelBackend):
    def authenticate(self, request, username=None, password=None, **kwargs):
        if username is None:
            username = kwargs.get(UserModel.USERNAME_FIELD)
        if username is None or password is None:
            return
        try:
            user = UserModel._default_manager.get_by_natural_key(username)
        except UserModel.DoesNotExist:
            # Run the default password hasher once to reduce the timing
            # difference between an existing and a nonexistent user (#20760).
            UserModel().set_password(password)
        else:
            if user.is_ad_account:
                if ldap_auth(user.username,password) and self.user_can_authenticate(user):
                    print("域用户登陆成功",user.username)
                    return user
            else: 
                if user.check_password(password) and self.user_can_authenticate(user):
                    print("网站账号登陆成功",user.username)
                    return user
from django.shortcuts import render
from django.contrib import messages
from ..models import UdtUnbindNotMRB
from ..database import DB
from operation_logs.models import OperationLog
import json

def unbind_not_mrb(request):
    context = {}
    if request.method == 'POST':
        sn_list = request.POST.get('snList', '')
        snc = request.POST.get('SNC', '')
        remark = request.POST.get('Remark', '')

        # 保存用户输入到上下文
        context['sn_list'] = sn_list
        context['snc'] = snc
        context['remark'] = remark

        if 'query' in request.POST:
            results = UdtUnbindNotMRB.objects.filter(serial_number__in=sn_list.split()).order_by('-id')
            context['results'] = results

        elif 'insert' in request.POST:
            db = DB()
            serial_numbers = ','.join([f"'{value.strip()}'" for value in sn_list.split() if value.strip()])

            sql = f'''
            INSERT INTO [udtUnbindNotMRB] (unitid, SerialNumber, sncnumber, CreationTime, remark)
            SELECT unitid, value, :snc_number, GETDATE(), :remark
            FROM dbo.ffSerialNumber
            WHERE value IN ({serial_numbers})
            '''

            try:
                db.exec_non_query(sql, {'snc_number': snc, 'remark': remark})
                messages.success(request, "Batch insertion successful!")
            except Exception as e:
                messages.error(request, f"An error occurred: {e}")

            results = UdtUnbindNotMRB.objects.filter(serial_number__in=sn_list.split()).order_by('-id')
            context['results'] = results

            # 添加更详细的日志记录
            log_details = {
                'SerialNumbers': sn_list.split(),
                'SNCNumber': snc,
                'Remark': remark
            }
            OperationLog.objects.create(
                username=request.user.username,
                operation='INSERT',
                table_name='udtUnbindNotMRB',
                details=json.dumps(log_details, ensure_ascii=False)
            )

    return render(request, 'unbind/unbind_not_mrb.html', context)

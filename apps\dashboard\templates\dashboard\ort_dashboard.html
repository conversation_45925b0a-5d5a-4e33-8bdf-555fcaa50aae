<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{{ page_title }}</title>
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/echarts/5.4.3/echarts.min.js"></script>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #1a1a1a;
        }
        .dashboard-container {
            max-width: 1200px;
            margin: 0 auto;
            background-color: #2a2a2a;
            color: #e0e0e0;
            border-radius: 8px;
            box-shadow: 0 0 10px rgba(0,0,0,0.3);
            padding: 20px;
        }
        .dashboard-header {
            text-align: center;
            margin-bottom: 30px;
            padding-bottom: 15px;
            border-bottom: 1px solid #eee;
        }
        .chart-container {
            margin-bottom: 40px;
        }
        .chart-row {
            display: flex;
            flex-wrap: wrap;
            gap: 20px;
            margin-bottom: 20px;
        }
        .chart-box {
            flex: 1;
            min-width: 300px;
            border: 1px solid #444;
            border-radius: 6px;
            padding: 15px;
            background-color: #333;
            box-shadow: 0 2px 5px rgba(0,0,0,0.2);
        }
        .chart-title {
            text-align: center;
            font-size: 18px;
            margin-bottom: 15px;
            font-weight: bold;
            color: #e0e0e0;
        }
        .chart {
            height: 500px;
            width: 100%;
        }

        .full-width {
            width: 100%;
            max-width: 100%;
        }
        .controls {
            display: flex;
            justify-content: center;
            margin-bottom: 20px;
            gap: 20px;
            color: #e0e0e0;
        }
        select {
            padding: 8px 12px;
            border-radius: 4px;
            border: 1px solid #444;
            background-color: #333;
            color: #e0e0e0;
        }
        .error-message {
            color: #ff6b6b;
            text-align: center;
            padding: 20px;
            background-color: #3a2a2a;
            border-radius: 6px;
            margin: 20px 0;
            border: 1px solid #ff6b6b;
        }
    </style>
</head>
<body>
    <div class="dashboard-container">
        <div class="dashboard-header">
            <h1>{{ page_title }}</h1>
        </div>

        {% if error_message %}
        <div class="error-message">
            {{ error_message }}
        </div>
        {% else %}

        <div class="controls">
            <div>
                <label for="project-filter">Project:</label>
                <select id="project-filter">
                    <option value="all">All Projects</option>
                </select>
            </div>
        </div>

        <div class="chart-row">
            <div class="chart-box full-width">
                <div class="chart-title">ORT/DV Quantity by Month</div>
                <div id="qty-chart" class="chart"></div>
            </div>
        </div>

        <div class="chart-row">
            <div class="chart-box full-width">
                <div class="chart-title">ORT/DV Unit Cost by Month</div>
                <div id="cost-chart" class="chart"></div>
            </div>
        </div>

        <!-- Hidden elements to store data from Django view -->
        <div id="qty-chart-data" style="display: none;">{{ qty_chart_data|safe }}</div>
        <div id="cost-chart-data" style="display: none;">{{ cost_chart_data|safe }}</div>

        <script>
            // Wait for document to be ready
            document.addEventListener('DOMContentLoaded', function() {
                // 获取从 Django 视图传递过来的数据
                let qtyChartDataRaw = {};
                let costChartDataRaw = {};

                try {
                    const qtyDataElement = document.getElementById('qty-chart-data');
                    const costDataElement = document.getElementById('cost-chart-data');

                    if (qtyDataElement && qtyDataElement.textContent) {
                        qtyChartDataRaw = JSON.parse(qtyDataElement.textContent || '{}');
                    }

                    if (costDataElement && costDataElement.textContent) {
                        costChartDataRaw = JSON.parse(costDataElement.textContent || '{}');
                    }
                } catch (error) {
                    console.error("Error parsing chart data:", error);
                    // Show error message on the page
                    const errorDiv = document.createElement('div');
                    errorDiv.className = 'error-message';
                    errorDiv.textContent = 'Error loading chart data: ' + error.message;
                    document.querySelector('.dashboard-container').appendChild(errorDiv);
                }

                // 辅助函数：将原始数据转换为 ECharts 格式
                function prepareChartData(rawData) {
                    if (Object.keys(rawData).length === 0) {
                        return { dates: [], projects: [], series: [], legendData: [] };
                    }

                    const dates = Object.keys(rawData).sort((a, b) => {
                        // Convert "Mon YYYY" to "YYYY-MM" for proper sorting
                        const parseDate = (dateStr) => {
                            const [monthStr, yearStr] = dateStr.split(' ');
                            const monthMap = {
                                'Jan': '01', 'Feb': '02', 'Mar': '03', 'Apr': '04', 'May': '05', 'Jun': '06',
                                'Jul': '07', 'Aug': '08', 'Sep': '09', 'Oct': '10', 'Nov': '11', 'Dec': '12'
                            };
                            return `${yearStr}-${monthMap[monthStr]}`;
                        };
                        return parseDate(a).localeCompare(parseDate(b));
                    });

                    // Define all possible projects and their colors
                    const projectColors = {
                        'Hermes': '#5470c6',    // Blue
                        'Voyager': '#91cc75',   // Green
                        'Kosmos': '#fac858',    // Yellow
                        'Alpha': '#ee6666',     // Red
                        'Mono': '#73c0de',      // Light Blue
                        'Other': '#3ba272'      // Teal
                    };

                    // Get all projects from the data
                    const allProjects = Array.from(new Set(Object.values(rawData).flatMap(Object.keys))).sort();

                    // Create legend data for projects only (not ORT/DV)
                    const legendData = allProjects;

                    // Create series for ORT and DV stacks
                    const series = [];

                    // Create a series for each project in ORT stack
                    allProjects.forEach(project => {
                        // Create data for ORT stack
                        const ortData = dates.map(date => {
                            const value = rawData[date][project] ? rawData[date][project]['ORT'] : 0;
                            return value;
                        });

                        series.push({
                            name: project,
                            type: 'bar',
                            stack: 'ORT',
                            data: ortData,
                            itemStyle: {
                                color: projectColors[project] || '#999999' // Use defined color or gray if not defined
                            },
                            emphasis: {
                                focus: 'series'
                            },
                            label: {
                                show: true,
                                position: 'inside',
                                formatter: '{c}',
                                color: '#fff'
                            }
                        });
                    });

                    // Create a series for each project in DV stack
                    allProjects.forEach(project => {
                        // Create data for DV stack
                        const dvData = dates.map(date => {
                            const value = rawData[date][project] ? rawData[date][project]['DV'] : 0;
                            return value;
                        });

                        series.push({
                            name: project,
                            type: 'bar',
                            stack: 'DV',
                            data: dvData,
                            itemStyle: {
                                color: projectColors[project] || '#999999' // Use defined color or gray if not defined
                            },
                            emphasis: {
                                focus: 'series'
                            },
                            label: {
                                show: true,
                                position: 'inside',
                                formatter: '{c}',
                                color: '#fff'
                            }
                        });
                    });

                    // Create custom x-axis data with ORT and DV for each date
                    const customXAxisData = [];
                    const dateLabels = []; // For displaying month labels

                    dates.forEach(date => {
                        customXAxisData.push('ORT\n' + date);
                        customXAxisData.push('DV\n' + date);
                        // Add a label for the month
                        dateLabels.push({
                            value: customXAxisData.length - 1.5, // Position between ORT and DV
                            textStyle: {
                                color: '#e0e0e0',
                                fontSize: 12,
                                fontWeight: 'bold'
                            },
                            formatter: date
                        });
                    });

                    return {
                        dates: customXAxisData,
                        projects: allProjects,
                        series: series,
                        legendData: legendData,
                        dateLabels: dateLabels
                    };
                }

                // 准备 QTY 图表数据
                const { dates: qtyDates, projects: qtyProjects, series: qtySeries, legendData: qtyLegendData, dateLabels: qtyDateLabels } = prepareChartData(qtyChartDataRaw);

                // 准备 Cost 图表数据
                const { dates: costDates, projects: costProjects, series: costSeries, legendData: costLegendData, dateLabels: costDateLabels } = prepareChartData(costChartDataRaw);

                // 初始化 ECharts 实例
                const qtyChart = echarts.init(document.getElementById('qty-chart'));
                const costChart = echarts.init(document.getElementById('cost-chart'));

                // QTY 图表配置
                const qtyOption = {
                    title: {
                        text: 'ORT Lab Device Storage Qty',
                        left: 'center',
                        textStyle: {
                            color: '#e0e0e0'
                        }
                    },
                    tooltip: {
                        trigger: 'axis',
                        axisPointer: {
                            type: 'shadow'
                        },
                        formatter: function(params) {
                            // Group params by stack (ORT or DV)
                            const stackGroups = {};
                            params.forEach(param => {
                                const stack = param.seriesIndex < qtyProjects.length ? 'ORT' : 'DV';
                                if (!stackGroups[stack]) {
                                    stackGroups[stack] = [];
                                }
                                stackGroups[stack].push(param);
                            });

                            // Create tooltip content
                            let tooltip = params[0].axisValueLabel.split('\n')[1] + '<br/>';

                            // Add ORT section if exists
                            if (stackGroups['ORT'] && stackGroups['ORT'].length > 0) {
                                tooltip += '<div style="margin-top: 5px;"><b>ORT:</b></div>';
                                stackGroups['ORT'].forEach(param => {
                                    if (param.value > 0) {
                                        tooltip += param.marker + ' ' + param.seriesName + ': ' + param.value + '<br/>';
                                    }
                                });
                            }

                            // Add DV section if exists
                            if (stackGroups['DV'] && stackGroups['DV'].length > 0) {
                                tooltip += '<div style="margin-top: 5px;"><b>DV:</b></div>';
                                stackGroups['DV'].forEach(param => {
                                    if (param.value > 0) {
                                        tooltip += param.marker + ' ' + param.seriesName + ': ' + param.value + '<br/>';
                                    }
                                });
                            }

                            return tooltip;
                        }
                    },
                    legend: {
                        data: qtyLegendData,
                        bottom: 10,
                        textStyle: {
                            color: '#e0e0e0'
                        }
                    },
                    grid: {
                        left: '3%',
                        right: '4%',
                        bottom: '20%',  // Increased to accommodate month labels
                        top: '15%',
                        containLabel: true
                    },
                    xAxis: [
                        {
                            type: 'category',
                            data: qtyDates,
                            axisLabel: {
                                color: '#e0e0e0',
                                interval: 0,   // Show all labels
                                formatter: function(value) {
                                    // Extract the type (ORT/DV) and date
                                    const parts = value.split('\n');
                                    return parts[0]; // Just show ORT or DV
                                }
                            },
                            axisPointer: {
                                label: {
                                    formatter: function(params) {
                                        // Extract the date part
                                        const parts = params.value.split('\n');
                                        return parts[1]; // Return the date part
                                    }
                                }
                            }
                        },
                        {
                            type: 'category',
                            position: 'bottom',
                            offset: 35,
                            axisLine: {show: false},
                            axisTick: {show: false},
                            axisLabel: {
                                show: true,
                                interval: function(index, value) {
                                    // Only show labels for even indices (first bar of each pair)
                                    return index % 2 === 0;
                                },
                                formatter: function(value, index) {
                                    // Extract the date part
                                    const parts = value.split('\n');
                                    return parts[1]; // Return the date part
                                },
                                color: '#e0e0e0',
                                fontSize: 12,
                                fontWeight: 'bold'
                            }
                        }
                    ],
                    yAxis: {
                        type: 'value',
                        name: 'Quantity',
                        axisLabel: {
                            color: '#e0e0e0'
                        },
                        nameTextStyle: {
                            color: '#e0e0e0'
                        }
                    },
                    series: qtySeries,
                    // Add color theme for projects
                    color: ['#5470c6', '#91cc75', '#fac858', '#ee6666', '#73c0de', '#3ba272'],
                    // Add bar width and gap settings
                    barMaxWidth: 60,  // Maximum width of bars
                    barCategoryGap: '40%',  // Gap between date groups
                    barGap: '0%'      // No gap between bars in same stack
                };

                // Cost 图表配置
                const costOption = {
                    title: {
                        text: 'ORT Lab Device Scrap Cost',
                        left: 'center',
                        textStyle: {
                            color: '#e0e0e0'
                        }
                    },
                    tooltip: {
                        trigger: 'axis',
                        axisPointer: {
                            type: 'shadow'
                        },
                        formatter: function(params) {
                            // Group params by stack (ORT or DV)
                            const stackGroups = {};
                            params.forEach(param => {
                                const stack = param.seriesIndex < costProjects.length ? 'ORT' : 'DV';
                                if (!stackGroups[stack]) {
                                    stackGroups[stack] = [];
                                }
                                stackGroups[stack].push(param);
                            });

                            // Create tooltip content
                            let tooltip = params[0].axisValueLabel.split('\n')[1] + '<br/>';

                            // Add ORT section if exists
                            if (stackGroups['ORT'] && stackGroups['ORT'].length > 0) {
                                tooltip += '<div style="margin-top: 5px;"><b>ORT:</b></div>';
                                stackGroups['ORT'].forEach(param => {
                                    if (param.value > 0) {
                                        tooltip += param.marker + ' ' + param.seriesName + ': $' + param.value + '<br/>';
                                    }
                                });
                            }

                            // Add DV section if exists
                            if (stackGroups['DV'] && stackGroups['DV'].length > 0) {
                                tooltip += '<div style="margin-top: 5px;"><b>DV:</b></div>';
                                stackGroups['DV'].forEach(param => {
                                    if (param.value > 0) {
                                        tooltip += param.marker + ' ' + param.seriesName + ': $' + param.value + '<br/>';
                                    }
                                });
                            }

                            return tooltip;
                        }
                    },
                    legend: {
                        data: costLegendData,
                        bottom: 10,
                        textStyle: {
                            color: '#e0e0e0'
                        }
                    },
                    grid: {
                        left: '3%',
                        right: '4%',
                        bottom: '20%',  // Increased to accommodate month labels
                        top: '15%',
                        containLabel: true
                    },
                    xAxis: [
                        {
                            type: 'category',
                            data: costDates,
                            axisLabel: {
                                color: '#e0e0e0',
                                interval: 0,   // Show all labels
                                formatter: function(value) {
                                    // Extract the type (ORT/DV) and date
                                    const parts = value.split('\n');
                                    return parts[0]; // Just show ORT or DV
                                }
                            },
                            axisPointer: {
                                label: {
                                    formatter: function(params) {
                                        // Extract the date part
                                        const parts = params.value.split('\n');
                                        return parts[1]; // Return the date part
                                    }
                                }
                            }
                        },
                        {
                            type: 'category',
                            position: 'bottom',
                            offset: 35,
                            axisLine: {show: false},
                            axisTick: {show: false},
                            axisLabel: {
                                show: true,
                                interval: function(index, value) {
                                    // Only show labels for even indices (first bar of each pair)
                                    return index % 2 === 0;
                                },
                                formatter: function(value, index) {
                                    // Extract the date part
                                    const parts = value.split('\n');
                                    return parts[1]; // Return the date part
                                },
                                color: '#e0e0e0',
                                fontSize: 12,
                                fontWeight: 'bold'
                            }
                        }
                    ],
                    yAxis: {
                        type: 'value',
                        name: 'Unit Cost ($)',
                        axisLabel: {
                            formatter: '${value}',
                            color: '#e0e0e0'
                        },
                        nameTextStyle: {
                            color: '#e0e0e0'
                        }
                    },
                    series: costSeries,
                    // Add color theme for projects
                    color: ['#5470c6', '#91cc75', '#fac858', '#ee6666', '#73c0de', '#3ba272'],
                    // Add bar width and gap settings
                    barMaxWidth: 60,  // Maximum width of bars
                    barCategoryGap: '40%',  // Gap between date groups
                    barGap: '0%'      // No gap between bars in same stack
                };

                // Apply options to charts
                qtyChart.setOption(qtyOption);
                costChart.setOption(costOption);

                // Populate project filter dropdown
                const projectFilter = document.getElementById('project-filter');
                projectFilter.innerHTML = '<option value="all">All Projects</option>'; // Clear existing options
                qtyProjects.forEach(project => {
                    const option = document.createElement('option');
                    option.value = project;
                    option.textContent = project;
                    projectFilter.appendChild(option);
                });

                // Function to update charts based on selected project
                function updateChartsByProject(selectedProject) {
                    const filteredQtyChartData = {};
                    const filteredCostChartData = {};

                    if (selectedProject === 'all') {
                        // Use all data
                        Object.assign(filteredQtyChartData, qtyChartDataRaw);
                        Object.assign(filteredCostChartData, costChartDataRaw);
                    } else {
                        // Filter data for selected project only
                        for (const date in qtyChartDataRaw) {
                            if (qtyChartDataRaw[date][selectedProject]) {
                                // Create a new object with only the selected project
                                filteredQtyChartData[date] = {};
                                filteredQtyChartData[date][selectedProject] = qtyChartDataRaw[date][selectedProject];
                            }
                        }
                        for (const date in costChartDataRaw) {
                            if (costChartDataRaw[date][selectedProject]) {
                                // Create a new object with only the selected project
                                filteredCostChartData[date] = {};
                                filteredCostChartData[date][selectedProject] = costChartDataRaw[date][selectedProject];
                            }
                        }
                    }

                    // Prepare chart data with filtered data
                    const { dates: filteredQtyDates, series: filteredQtySeries, legendData: filteredQtyLegendData } = prepareChartData(filteredQtyChartData);
                    const { dates: filteredCostDates, series: filteredCostSeries, legendData: filteredCostLegendData } = prepareChartData(filteredCostChartData);

                    // Update quantity chart
                    qtyOption.xAxis[0].data = filteredQtyDates;
                    qtyOption.xAxis[1].data = filteredQtyDates;
                    qtyOption.series = filteredQtySeries;
                    qtyOption.legend.data = filteredQtyLegendData;
                    qtyOption.title.text = selectedProject === 'all' ? 'ORT Lab Device Storage Qty (All Projects)' : `ORT Lab Device Storage Qty (${selectedProject})`;
                    qtyChart.setOption(qtyOption, true);

                    // Update cost chart
                    costOption.xAxis[0].data = filteredCostDates;
                    costOption.xAxis[1].data = filteredCostDates;
                    costOption.series = filteredCostSeries;
                    costOption.legend.data = filteredCostLegendData;
                    costOption.title.text = selectedProject === 'all' ? 'ORT Lab Device Scrap Cost (All Projects)' : `ORT Lab Device Scrap Cost (${selectedProject})`;
                    costChart.setOption(costOption, true);


                }

                // Event listener for project filter
                projectFilter.addEventListener('change', function() {
                    updateChartsByProject(this.value);
                });

                // Initial chart update
                updateChartsByProject('all');

                // Handle resize
                window.addEventListener('resize', function() {
                    qtyChart.resize();
                    costChart.resize();
                });
            });
        </script>
        {% endif %}
    </div>
</body>
</html>
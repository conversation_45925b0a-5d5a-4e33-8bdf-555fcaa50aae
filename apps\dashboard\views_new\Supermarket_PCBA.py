from django.shortcuts import render
from django.db import connections
from django.http import JsonResponse
import pandas as pd
import re

# usersapi.py (Modified)
from django.shortcuts import render
from django.db import connections
from django.http import JsonResponse
import pandas as pd
import re # Keep re if used elsewhere, otherwise it can be removed if not needed for this function

def get_rdpPCBASupermarket_rw(request):
    """
    获取各项目下状态为 "Pass" 和 "OnHold" 的料号数据，并为每个料号准备图表所需数据。
    """
    raw_data = []
    error_message = None

    try:
        with connections['VolcanoReporDB'].cursor() as cursor:
            # Expected columns: Project, Name, Status, TotalQty
            # Assuming this stored procedure returns: Project, Name, Status, TotalQty
            cursor.execute("EXEC rdpPCBAAgingReportData_rw") # MAKE SURE THIS SP EXISTS AND RETURNS THE CORRECT COLUMNS
            columns = [col[0] for col in cursor.description]
            for row in cursor.fetchall():
                raw_data.append(dict(zip(columns, row)))
    except Exception as e:
        print(f"Database error: {e}")
        error_message = f'Error fetching data from database: {str(e)}'
        # Return early if DB connection fails
        context = {
            'pass_data_by_project': {},
            'aggregated_onhold_data': [],
            'page_title': 'Assembly Supermarket PCBA Inventory',
            'error_message': error_message
        }
        return render(request, 'dashboard/supermarket_pcba.html', context)


    pass_data_by_project = {}
    aggregated_onhold_data = []

    if raw_data:
        df = pd.DataFrame(raw_data)

        # Ensure required columns are present
        required_cols = ['Project', 'Name', 'Status', 'TotalQty']
        if not all(col in df.columns for col in required_cols):
            error_message = "Data from database is missing one or more required columns: Project, Name, Status, TotalQty."
        else:
            # Convert TotalQty to numeric, coercing errors
            df.loc[:, 'TotalQty'] = pd.to_numeric(df['TotalQty'], errors='coerce')
            # Drop rows where TotalQty could not be converted (became NaN)
            df.dropna(subset=['TotalQty'], inplace=True)
            
            # Filter for 'Pass' status
            pass_df = df[df['Status'] == 'Pass'].copy()
            
            # Filter for 'OnHold' status
            onhold_df = df[df['Status'] == 'OnHold'].copy()

            # 获取所有项目列表（包括 Pass 和 OnHold 的）
            all_projects = set(df['Project'].unique())

            # 处理 Pass 状态数据
            for project in all_projects:
                project_pass_data = pass_df[pass_df['Project'] == project]
                project_part_data = []
                
                if not project_pass_data.empty:
                    for _, row in project_pass_data.iterrows():
                        project_part_data.append({
                            'Name': row['Name'],
                            'quantity': row['TotalQty']
                        })
                else:
                    # 如果没有 Pass 数据，创建一个空的占位符
                    project_part_data.append({
                        'Name': 'No Pass Data',
                        'quantity': 0
                    })
                
                pass_data_by_project[project] = project_part_data

            # 处理 OnHold 状态数据 - 聚合所有项目的 OnHold 数据，按料号汇总并排序
            if not onhold_df.empty:
                # 按料号 (Name) 分组，计算每个料号的总数量
                onhold_summary_df = onhold_df.groupby('Name')['TotalQty'].sum().reset_index()
                # 按数量降序排序
                onhold_summary_df = onhold_summary_df.sort_values(by='TotalQty', ascending=False)
                
                for _, row in onhold_summary_df.iterrows():
                    aggregated_onhold_data.append({
                        'Name': row['Name'],
                        'quantity': row['TotalQty']
                    })
                
    else:
        if not error_message: # Only set this if no DB error occurred earlier
            error_message = 'No data returned from the database.'

    context = {
        'pass_data_by_project': pass_data_by_project,
        'aggregated_onhold_data': aggregated_onhold_data,
        'page_title': 'Assembly Supermarket PCBA Inventory',
        'error_message': error_message
    }
    # Use a new template or modify supermarket_pcba.html
    return render(request, 'dashboard/supermarket_pcba.html', context)

# Other views like get_prime_detail, get_mid_detail would remain here if needed for other pages
# ...



from django.views.decorators.csrf import csrf_exempt

@csrf_exempt
def supermarket_pcba_detail(request):
    """
    展示 PCBA 明细数据，来源 SP udpPCBAAgingReportDataDetail_rw，按 Project 过滤
    """
    from django.db import connections
    import pandas as pd

    project = request.GET.get('project', None)
    detail_data = []
    error_message = None
    total_qty = 0

    try:
        with connections['VolcanoReporDB'].cursor() as cursor:
            cursor.execute("EXEC udpPCBAAgingReportDataDetail_rw")
            columns = [col[0] for col in cursor.description]
            for row in cursor.fetchall():
                detail_data.append(dict(zip(columns, row)))
    except Exception as e:
        error_message = f"数据库查询失败：{str(e)}"
        detail_data = []

    # 字段兼容性处理
    if detail_data and project:
        # 特殊处理：当 project='OnHold'时，按 Status 字段过滤 OnHold 数据
        if str(project).strip().lower() == 'onhold':
            detail_data = [d for d in detail_data if str(d.get('Status', '')).strip() == 'OnHold']
        else:
            # 只保留对应 Project 的数据
            detail_data = [d for d in detail_data if str(d.get('Project', '')).strip() == str(project).strip()]
        # 统计总数
        total_qty = sum([d.get('Qty', d.get('QTY', d.get('quantity', 0))) or 0 for d in detail_data])
    elif not detail_data:
        error_message = error_message or "无明细数据"

    # 兼容字段名
    for d in detail_data:
        # 统一 Qty 字段
        if 'QTY' in d and 'Qty' not in d:
            d['Qty'] = d['QTY']
        if 'quantity' in d and 'Qty' not in d:
            d['Qty'] = d['quantity']

    context = {
        'page_title': 'Assembly Supermarket PCBA Inventory Report',
        'chart_title': project or '',
        'total_qty': total_qty,
        'detail_data': detail_data,
        'error_message': error_message,
        'color': '',  # 可扩展
    }
    return render(request, 'dashboard/supermarket_pcba_detail.html', context)


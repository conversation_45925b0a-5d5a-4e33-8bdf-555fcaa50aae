from django.urls import path
from . import views
from django.contrib.admin.sites import site
from django.contrib import admin
from django.urls import include
from django.conf import settings
from django.conf.urls.static import static

app_name = 'mtghpreport'

urlpatterns = [
    path('admin/', admin.site.urls),
    
    path('get_lines/', views.get_lines, name='get_lines'),
    path('line_output/', views.line_output_view, name='line_output_view'),
    path('line_output/data/', views.line_output_data, name='line_output_data'),
    path('line_output/export/', views.line_output_export, name='line_output_export'),
    path('line_output/save_confirmation/', views.save_confirmation, name='save_confirmation'),
    path('week-summary/', views.week_summary_view, name='week_summary'),
    path('export-week-summary/', views.export_week_summary, name='export_week_summary'),
    path('incident_details/', views.incident_details_view, name='incident_details'),
    path('incident_details/data/', views.incident_details_data, name='incident_details_data'),
    path('incident_details/export/', views.incident_details_export, name='incident_details_export'),
]
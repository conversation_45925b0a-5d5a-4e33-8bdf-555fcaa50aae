import pandas as pd
from django.db import connections
from django.shortcuts import render

def get_daily_packing_production_summary_chart(request):
    Y_AXIS_MAX = 3000  # 定义 Y 轴最大值和每日总目标
    raw_data = []
    error_message = None
    
    # 初始化两组图表数据
    # 新的堆叠柱状图数据结构
    hermes_packing_context_data = {
        'categories': [],
        'planned_data': [],             # 原始计划量 (用于 Tooltip)
        'actual_data': [],              # 实际产量 (Input) - 柱子底部
        'gap_abs_data': [],             # 差异绝对值 (|Input - Planned|) - 柱子中部
        'unmet_target_data': [],        # 未完成量 (Target - Input) - 柱子顶部
        'gap_sign_data': [],            # 差异符号 ('positive' 或 'negative') - 用于 Gap 颜色
        'y_axis_max': Y_AXIS_MAX,       # Y 轴最大值 (ShiftTarget)
        'page_title': 'Hermes Packing Plan VS Actual',
        'data_exists': False
    }
    alpha_packing_context_data = {
        'categories': [],
        'planned_data': [],
        'actual_data': [],
        'gap_abs_data': [],
        'unmet_target_data': [],
        'gap_sign_data': [],
        'y_axis_max': Y_AXIS_MAX,
        'page_title': 'Alpha Packing Plan VS Actual',
        'data_exists': False
    }

    type_col_name = None # 将在获取列后定义
    desc_col_name = None
    # Plandate_col_name = None # 计划日期列
    # shift_col_name = None # 班次列
    plan_qty_col_name = None
    input_qty_col_name = None

    try:
        with connections['VolcanoReporDB'].cursor() as cursor:
            cursor.execute("EXEC usp_GetDailyPackingLineProductionSummary_rw")
            columns = [col[0] for col in cursor.description]
            
            # 存储过程现在返回 6 列：Type, Description, Plandate, Shift, PlannedQuantity, Input
            if len(columns) < 6:
                error_message = f"存储过程返回的列数不足。期望至少 6 列，实际返回{len(columns)}列：{', '.join(columns)}"
            else:
                type_col_name = columns[0]
                desc_col_name = columns[1]
                # Plandate_col_name = columns[2] # 对应 Plandate
                # shift_col_name = columns[3] # 对应 Shift
                plan_qty_col_name = columns[4] # PlannedQuantity 现在是第 5 列 (索引 4)
                input_qty_col_name = columns[5] # Input 现在是第 6 列 (索引 5)

                for row in cursor.fetchall():
                    raw_data.append(dict(zip(columns, row)))
    
    except Exception as e:
        print(f"Database error for Daily Packing Production Summary: {e}")
        error_message = f'数据库错误：获取每日包装产线生产总结数据失败：{str(e)}'

    if not error_message and not raw_data:
        print("No data returned from stored procedure usp_GetDailyPackingLineProductionSummary_rw.")
        # 保持 error_message 为 None，让后续逻辑判断是否真的没有相关类型的数据

    if raw_data and not error_message:
        try:
            df = pd.DataFrame(raw_data)

            if not all([type_col_name, desc_col_name, plan_qty_col_name, input_qty_col_name]):
                 raise ValueError("列名变量 (type_col_name, etc.) 未从数据库元数据正确初始化。")

            required_processing_cols = [type_col_name, desc_col_name, plan_qty_col_name, input_qty_col_name]
            if not all(col in df.columns for col in required_processing_cols):
                missing_df_cols = [col for col in required_processing_cols if col not in df.columns]
                raise ValueError(f"DataFrame 创建后缺少必要的列进行处理。期望：{', '.join(required_processing_cols)}。实际 DataFrame 列：{df.columns.tolist()}。缺失：{', '.join(missing_df_cols)}")

            df[plan_qty_col_name] = pd.to_numeric(df[plan_qty_col_name], errors='coerce').fillna(0)
            df[input_qty_col_name] = pd.to_numeric(df[input_qty_col_name], errors='coerce').fillna(0)

            product_types_to_process = {
                "Hermes Packing": hermes_packing_context_data,
                "Alpha Packing": alpha_packing_context_data
            }

            for product_type, context_ref in product_types_to_process.items():
                if type_col_name not in df.columns:
                    raise KeyError(f"用于筛选产品类型的列 '{type_col_name}' 不在 DataFrame 中。可用列：{df.columns.tolist()}")

                df_filtered = df[df[type_col_name] == product_type]
                
                if not df_filtered.empty:
                    if desc_col_name not in df_filtered.columns:
                         raise KeyError(f"用于分组的描述列 '{desc_col_name}' 不在筛选后的 DataFrame 中 (类型：{product_type})。可用列：{df_filtered.columns.tolist()}")

                    summary_df = df_filtered.groupby(desc_col_name, as_index=False).agg(
                        PlannedQuantity_Total=(plan_qty_col_name, 'sum'),
                        Input_Total=(input_qty_col_name, 'sum')
                    )
                    # 计算新堆叠图所需的数据
                    summary_df['Actual_Production'] = summary_df['Input_Total']
                    summary_df['Gap_Value'] = summary_df['Input_Total'] - summary_df['PlannedQuantity_Total'] # 实际 - 计划
                    summary_df['Absolute_Gap'] = summary_df['Gap_Value'].abs()
                    summary_df['Unmet_Target'] = (Y_AXIS_MAX - summary_df['Actual_Production']).apply(lambda x: max(0, x)) # 确保非负
                    summary_df['Gap_Sign'] = summary_df['Gap_Value'].apply(lambda x: 'positive' if x >= 0 else 'negative')

                    # 填充 context 数据
                    context_ref['categories'] = summary_df[desc_col_name].tolist()
                    context_ref['planned_data'] = summary_df['PlannedQuantity_Total'].tolist() # 保留原始计划用于 Tooltip
                    context_ref['actual_data'] = summary_df['Actual_Production'].tolist()
                    context_ref['gap_abs_data'] = summary_df['Absolute_Gap'].tolist()
                    context_ref['unmet_target_data'] = summary_df['Unmet_Target'].tolist()
                    context_ref['gap_sign_data'] = summary_df['Gap_Sign'].tolist()
                    context_ref['data_exists'] = True
                else:
                    print(f"No data found for product type: {product_type}")

        except ValueError as ve:
            print(f"Data processing error (ValueError): {ve}")
            error_message = f'数据处理错误 (值错误): {str(ve)}'
        except KeyError as ke:
            print(f"Data processing error (KeyError): {ke}")
            error_message = f'数据处理错误 (键错误，可能列名不匹配): {str(ke)}'
        except Exception as e_proc:
            print(f"Unexpected error during data processing: {e_proc}")
            error_message = f'数据处理时发生意外错误：{str(e_proc)}'

    final_context = {
        # 更新 final_context 以包含新的数据键
        'error_message': error_message,
        'hermes_packing_categories': hermes_packing_context_data['categories'],
        'hermes_packing_planned_data': hermes_packing_context_data['planned_data'], # 原始计划 (Tooltip 用)
        'hermes_packing_actual_data': hermes_packing_context_data['actual_data'],     # 实际 (底部)
        'hermes_packing_gap_abs_data': hermes_packing_context_data['gap_abs_data'],   # Gap 绝对值 (中部)
        'hermes_packing_unmet_data': hermes_packing_context_data['unmet_target_data'],# 未完成 (顶部)
        'hermes_packing_gap_sign_data': hermes_packing_context_data['gap_sign_data'], # Gap 符号 (颜色用)
        'hermes_packing_y_axis_max': hermes_packing_context_data['y_axis_max'],
        'hermes_packing_page_title': hermes_packing_context_data['page_title'],

        'alpha_packing_categories': alpha_packing_context_data['categories'],
        'alpha_packing_planned_data': alpha_packing_context_data['planned_data'],
        'alpha_packing_actual_data': alpha_packing_context_data['actual_data'],
        'alpha_packing_gap_abs_data': alpha_packing_context_data['gap_abs_data'],
        'alpha_packing_unmet_data': alpha_packing_context_data['unmet_target_data'],
        'alpha_packing_gap_sign_data': alpha_packing_context_data['gap_sign_data'],
        'alpha_packing_y_axis_max': alpha_packing_context_data['y_axis_max'],
        'alpha_packing_page_title': alpha_packing_context_data['page_title'],
    }
    
    if not error_message and not hermes_packing_context_data['data_exists'] and not alpha_packing_context_data['data_exists']:
        if not raw_data: 
             final_context['error_message'] = '存储过程 usp_GetDailyPackingLineProductionSummary_rw 未返回任何相关数据。'
        else: 
             final_context['error_message'] = '未找到 "Hermes Packing" 或 "Alpha Packing" 的生产数据。'

    return render(request, 'dashboard/packing_production_summary_chart.html', final_context)
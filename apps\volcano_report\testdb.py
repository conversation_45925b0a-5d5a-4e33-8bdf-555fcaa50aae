from sqlalchemy import create_engine, text

def get_complex_station_types(partfamilyID):
    # 创建SQLAlchemy引擎
    engine = create_engine(
        'mssql+pymssql://Report_Volcano:V!2017CANL@172.30.30.217:1437/FF_Volcano_Report'
    )

    connection = engine.connect()
    print("connection")

    try:
        query = text("EXEC [dbo].UDPGetPartFamilyTypeStations @PartfamilyTypeID=6")
        #query = text("SELECT *FROM dbo.ffLine")
        result = connection.execute(query)
        rows = result.fetchall()
        if not rows:
            raise ValueError("Stored procedure did not return any data")

        columns = result.keys()
        print('Columns:', columns)
        print('Data fetched:', rows)
        return columns, rows
    except Exception as e:
        print("Error executing stored procedure:", e)
        return None, None
    finally:
        connection.close()


get_complex_station_types(6)
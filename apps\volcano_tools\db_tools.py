from sqlalchemy import create_engine, text
from sqlalchemy.pool import QueuePool

class DB:
    def __init__(self, server, database, username, password):
        connection_string = f'mssql+pyodbc://{username}:{password}@{server}/{database}?driver=ODBC+Driver+17+for+SQL+Server'
        self.engine = create_engine(connection_string, poolclass=QueuePool, pool_size=10, max_overflow=20)

    def exec_query(self, query, parameters=None):
        """
        Execute a SELECT query
        """
        with self.engine.connect() as connection:
            result = connection.execute(text(query), parameters) if parameters else connection.execute(text(query))
            result_set = result.fetchall()
            return result_set

    def exec_non_query(self, query, parameters=None):
        """
        Execute an INSERT/UPDATE/DELETE query
        """
        with self.engine.connect() as connection:
            result = connection.execute(text(query), parameters) if parameters else connection.execute(text(query))
            connection.commit()
from datetime import datetime
from .forms import OnHoldDeviceForm
import json
from django.db import connections
from django.http import JsonResponse
from django.shortcuts import render
from django.contrib import messages
from ..tools import add_fs_language
from django.views.decorators.http import require_POST
from functools import wraps
import time
import logging
from ..database  import DB
from django.utils import timezone
from sqlalchemy import text



logger = logging.getLogger(__name__)

def udtonholddevice_view(request):
    """
    Render the main view for the On Hold Device page
    """
    return render(request, 'onhold/udtonholddevice.html')


def log_performance(func):
    @wraps(func)
    def wrapper(*args, **kwargs):
        start_time = time.time()
        result = func(*args, **kwargs)
        end_time = time.time()
        logger.info(f"{func.__name__} execution time: {end_time - start_time:.2f} seconds")
        return result
    return wrapper


def handle_exceptions(func):
    @wraps(func)
    def wrapper(*args, **kwargs):
        try:
            return func(*args, **kwargs)
        except json.JSONDecodeError:
            logger.error("Invalid JSON data received")
            return JsonResponse({'error': 'Invalid JSON format'}, status=400)
        except Exception as e:
            logger.exception(f"Unexpected error in {func.__name__}")
            return JsonResponse({'error': str(e)}, status=500)
    return wrapper


def measure_time(start_time, label):
    """Helper function to measure execution time of code blocks"""
    duration = time.time() - start_time
    logger.info(f"Performance - {label}: {duration:.3f} seconds")
    return time.time()


@require_POST
@handle_exceptions
@log_performance
def insert_on_hold_devices(request):
    try:
        overall_start = time.time()
        
        # 1. 获取和验证数据
        data = json.loads(request.body)
        serial_numbers_input = data.get('serial_numbers', '').strip()
        
        if not serial_numbers_input:
            return JsonResponse({'error': 'Serial numbers are required'}, status=400)
            
        # 准备参数
        remark = data.get('remark', '')
        retcode = data.get('retcode') or add_fs_language(remark, 'udtOnHoldDevice')
        status = 1 if data.get('Status', False) else 0
        isEngravingAssy = 1 if data.get('isEngravingAssy', False) else 0
        isHoldCarton = 1 if data.get('isHoldCarton', False) else 0
        isHoldQAapproved = 1 if data.get('isHoldQAapproved', False) else 0
        cartonID = 1 if data.get('cartonID', False) else 0
        
        # 2. 处理序列号
        serial_numbers = [sn.strip() for sn in serial_numbers_input.splitlines() if sn.strip()]
        if not serial_numbers:
            return JsonResponse({'error': 'No valid serial numbers provided'}, status=400)
            
        # 3. 使用分步SQL操作
        db = DB()
        with db.get_connection() as connection:
            try:
                serial_values = ', '.join(f"'{sn}'" for sn in serial_numbers)
                
                # 3.1 创建临时表
                create_temp_table = f"""
                SELECT unitid, Value INTO #SerialNumbers 
                FROM dbo.ffSerialNumber WITH (NOLOCK)
                WHERE Value IN ({serial_values})
                """
                connection.execute(text(create_temp_table))
                
                # 3.2 执行插入操作
                insert_sql = f"""
                INSERT INTO udtOnHoldDevice (
                    unitid, remark, retcode, lastupdate, 
                    isEngravingAssy, isHoldCarton, isHoldQAapproved, 
                    Status, cartonID
                )
                OUTPUT inserted.*
                SELECT 
                    sn.unitid,
                    :remark,
                    :retcode,
                    GETDATE(),
                    :isEngravingAssy,
                    :isHoldCarton,
                    :isHoldQAapproved,
                    :status,
                    :cartonID
                FROM #SerialNumbers sn
                LEFT JOIN udtOnHoldDevice ud WITH (NOLOCK)
                    ON ud.unitid = sn.unitid 
                    AND ud.retcode = :retcode
                WHERE ud.unitid IS NULL
                """
                
                # 使用参数化查询执行插入
                result = connection.execute(
                    text(insert_sql),
                    {
                        'remark': remark,
                        'retcode': retcode,
                        'isEngravingAssy': isEngravingAssy,
                        'isHoldCarton': isHoldCarton,
                        'isHoldQAapproved': isHoldQAapproved,
                        'status': status,
                        'cartonID': cartonID
                    }
                )
                
                # 获取插入的行数
                inserted_rows = result.fetchall()
                inserted_count = len(inserted_rows)
                
                # 3.3 清理临时表
                connection.execute(text("DROP TABLE IF EXISTS #SerialNumbers"))
                connection.commit()
                
            except Exception as e:
                connection.rollback()
                raise e
                
        # 4. 查询最新结果
        query_result = query_on_hold_devices_internal(serial_numbers_input, retcode)
        total_time = time.time() - overall_start
        
        return JsonResponse({
            'message': '处理成功' if inserted_count > 0 else '没有需要处理的新记录',
            'retcode': retcode,
            'execution_time': total_time,
            'processed_count': inserted_count,
            'query_result': query_result
        })
        
    except Exception as e:
        logger.exception("Insert operation failed")
        return JsonResponse({
            'error': str(e),
            'detail': getattr(e, 'detail', None)
        }, status=500)



def query_on_hold_devices_internal(serial_numbers_input, retcode_input):
    try:
        with connections['VolcanoFFDB'].cursor() as cursor:
            if serial_numbers_input:
                serial_numbers = [f"'{sn.strip()}'" for sn in serial_numbers_input.splitlines() if sn.strip()]
                serial_numbers_str = ',\n'.join(serial_numbers)
                query = f"""
                SELECT sn.Value, ud.lastupdate, ud.remark, ud.status, ud.isEngravingAssy,
                       ud.isHoldCarton, ud.isHoldQAapproved, ud.retcode, ud.cartonID
                FROM ffSerialNumber(nolock) sn
                JOIN udtOnHoldDevice(nolock) ud ON ud.unitid = sn.UnitID
                WHERE sn.Value IN ({serial_numbers_str})
                """
            elif retcode_input:
                query = f"""
                SELECT sn.Value, ud.lastupdate, ud.remark, ud.status, ud.isEngravingAssy,
                       ud.isHoldCarton, ud.isHoldQAapproved, ud.retcode, ud.cartonID
                FROM ffSerialNumber(nolock) sn
                JOIN udtOnHoldDevice(nolock) ud ON ud.unitid = sn.UnitID
                WHERE ud.retcode = '{retcode_input}'
                """
            else:
                return []

            cursor.execute(query)
            results = cursor.fetchall()

        return [
            {
                'serial_number': row[0],
                'lastupdate': row[1].strftime('%Y-%m-%d %H:%M:%S') if isinstance(row[1], datetime.datetime) else '',
                'remark': row[2],
                'Status': row[3],
                'isEngravingAssy': row[4],
                'isHoldCarton': row[5],
                'isHoldQAapproved': row[6],
                'Retcode': row[7],
                'cartonID': row[8]
            }
            for row in results
        ]

    except Exception as e:
        print('Query exception occurred: ', str(e))
        return []



import json
import time
import datetime
from django.views.decorators.http import require_POST


@require_POST
def query_on_hold_devices(request):
    try:
        # 直接从请求体读取 JSON 数据
        data = json.loads(request.body)
        serial_numbers_input = data.get('serial_numbers', '')
        retcode_input = data.get('retcode', '')
        #print(retcode_input)

        with connections['VolcanoFFDB'].cursor() as cursor:
            start_time = time.time()  # 记录开始时间

            if serial_numbers_input and retcode_input:
                serial_numbers = [f"'{sn.strip()}'" for sn in serial_numbers_input.splitlines() if sn.strip()]
                serial_numbers_str = ',\n'.join(serial_numbers)
                query = f"""
                SELECT sn.Value, ud.lastupdate, ud.remark, ud.status, ud.isEngravingAssy,
                       ud.isHoldCarton, ud.isHoldQAapproved, ud.retcode, ud.cartonID
                FROM ffSerialNumber(nolock) sn
                JOIN udtOnHoldDevice(nolock) ud ON ud.unitid = sn.UnitID
                WHERE sn.Value IN ({serial_numbers_str}) AND ud.retcode = '{retcode_input}'
                ORDER BY ud.id DESC
                """

            elif serial_numbers_input:
                # 按行分割输入，去除每行的前后空白字符
                serial_numbers = [f"'{sn.strip()}'" for sn in serial_numbers_input.splitlines() if sn.strip()]
                serial_numbers_str = ',\n'.join(serial_numbers)

                query = f"""
                SELECT sn.Value, ud.lastupdate, ud.remark, ud.status, ud.isEngravingAssy,
                       ud.isHoldCarton, ud.isHoldQAapproved, ud.retcode, ud.cartonID
                FROM ffSerialNumber(nolock) sn
                JOIN udtOnHoldDevice(nolock) ud ON ud.unitid = sn.UnitID
                WHERE  sn.Value IN ({serial_numbers_str}) order by ud.id desc
                """
            elif retcode_input:
                query = f"""
                SELECT sn.Value, ud.lastupdate, ud.remark, ud.status, ud.isEngravingAssy,
                       ud.isHoldCarton, ud.isHoldQAapproved, ud.retcode, ud.cartonID
                FROM ffSerialNumber(nolock) sn
                JOIN udtOnHoldDevice(nolock) ud ON ud.unitid = sn.UnitID
                WHERE   ud.retcode = '{retcode_input}' order by ud.id desc
                """
            else:
                return JsonResponse({'error': '请输入序列号或Retcode'}, status=400)

            #print(f"Query: {query}")

            cursor.execute(query)
            results = cursor.fetchall()

            execution_time = time.time() - start_time  # 计算执行时间
            print(f"Query execution time: {execution_time:.6f} seconds")

        data = [
            {
                'serial_number': row[0],
                'lastupdate': row[1].strftime('%Y-%m-%d %H:%M:%S') if isinstance(row[1], datetime.datetime) else '',
                'remark': row[2],
                'Status': row[3],
                'isEngravingAssy': row[4],
                'isHoldCarton': row[5],
                'isHoldQAapproved': row[6],
                'Retcode': row[7],
                'cartonID': row[8]
            }
            for row in results
        ]

        # print(data)

        return JsonResponse({'data': data})

    except json.JSONDecodeError:
        return JsonResponse({'error': 'JSON解析错误'}, status=400)
    except Exception as e:
        print('An exception occurred: ', str(e))  # 打印异常信息
        return JsonResponse({'error': str(e)}, status=500)



from django.views.decorators.http import require_POST
from django.db import connections
from django.http import JsonResponse
import json
from django.http import HttpRequest


import json
import logging
from django.http import JsonResponse
from django.views.decorators.http import require_POST
from django.db import connections
from django.http import HttpRequest

logger = logging.getLogger(__name__)

import json
import logging
from django.http import JsonResponse, HttpResponse
from django.views.decorators.http import require_POST
from django.db import connections
from django.http import HttpRequest

logger = logging.getLogger(__name__)


@require_POST
def update_on_hold_devices(request):
    try:
        data = json.loads(request.body)
        logger.info(f"Received update data: {data}")

        serial_numbers_input = data.get('serial_numbers', '')
        Status = 1 if data.get('Status', False) else 0
        isEngravingAssy = 1 if data.get('isEngravingAssy', False) else 0
        isHoldCarton = 1 if data.get('isHoldCarton', False) else 0
        isHoldQAapproved = 1 if data.get('isHoldQAapproved', False) else 0
        retcode = data.get('retcode')

        if not retcode:
            logger.warning("Attempt to update without retcode")
            return JsonResponse({'error': 'Retcode is required'}, status=400)

        if not serial_numbers_input:
            logger.warning("Attempt to update without serial numbers")
            return JsonResponse({'error': 'Serial numbers are required'}, status=400)

        serial_numbers = [f"'{sn.strip()}'" for sn in serial_numbers_input.splitlines() if sn.strip()]
        serial_numbers_str = ',\n'.join(serial_numbers)

        with connections['VolcanoFFDB'].cursor() as cursor:
            update_query = f"""
            UPDATE udtOnHoldDevice 
            SET status={Status}, 
                isEngravingAssy={isEngravingAssy}, 
                isHoldCarton={isHoldCarton}, 
                isHoldQAapproved={isHoldQAapproved},
                cartonID={1 if data.get('cartonID', False) else 0},
                lastupdate=GETDATE()
            WHERE unitid IN (
                SELECT UnitID 
                FROM dbo.ffSerialNumber 
                WHERE Value IN ({serial_numbers_str})
            ) AND retcode='{retcode}'
            """
            logger.debug(f"Executing update query: {update_query}")
            cursor.execute(update_query)
            rows_affected = cursor.rowcount
            connections['VolcanoFFDB'].commit()
            logger.info(f"Update affected {rows_affected} rows")

        # 使用查询视图的逻辑获取最新数据
        query_data = {
            'serial_numbers': serial_numbers_input,
            'retcode': retcode
        }
        query_request = HttpRequest()
        query_request.method = 'POST'
        query_request._body = json.dumps(query_data).encode('utf-8')
        query_response = query_on_hold_devices(query_request)

        if not isinstance(query_response, HttpResponse):
            raise ValueError(f"Unexpected response type from query_on_hold_devices: {type(query_response)}")

        query_result = json.loads(query_response.content)

        if 'data' not in query_result:
            raise KeyError("Expected 'data' key in query result, but it was not found")

        return JsonResponse({
            'status': 'success',
            'message': f'Successfully updated {rows_affected} records.',
            'query_result': query_result['data']
        })

    except json.JSONDecodeError as e:
        logger.error(f"JSON parsing error: {str(e)}")
        return JsonResponse({'status': 'error', 'error': 'JSON parsing error'}, status=400)
    except Exception as e:
        logger.exception(f"Unexpected error in update_on_hold_devices: {str(e)}")
        return JsonResponse({'status': 'error', 'error': str(e)}, status=500)
{% extends 'base1.html' %}
{% load static %}

{% block content %}
<style>
    .production-order-container {
        max-width: 1200px;
        margin: 2rem auto;
        padding: 2rem;
        background: #ffffff;
        border-radius: 20px;
        box-shadow: 0 2px 10px rgba(0,0,0,0.05);
    }

    .page-title {
        font-size: 2rem;
        font-weight: 500;
        color: #1d1d1f;
        margin-bottom: 2rem;
        text-align: center;
    }

    .form-section {
        background: #f5f5f7;
        border-radius: 16px;
        padding: 2rem;
        margin-bottom: 2rem;
    }

    .input-area {
        background: #ffffff;
        border-radius: 12px;
        border: 1px solid #d2d2d7;
        padding: 1rem;
        transition: all 0.3s ease;
    }

    .input-area:focus-within {
        border-color: #0066cc;
        box-shadow: 0 0 0 4px rgba(0,102,204,0.1);
    }

    .checkbox-group {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
        gap: 1rem;
        margin: 1.5rem 0;
    }

    .custom-checkbox {
        background: #ffffff;
        padding: 1rem;
        border-radius: 10px;
        border: 1px solid #d2d2d7;
        transition: all 0.2s ease;
    }

    .custom-checkbox:hover {
        background: #f5f5f7;
    }

    .custom-checkbox input[type="checkbox"] {
        margin-right: 8px;
    }

    .action-buttons {
        display: flex;
        gap: 1rem;
        justify-content: center;
        margin: 2rem 0;
    }

    .btn {
        padding: 0.75rem 1.5rem;
        border-radius: 20px;
        font-weight: 500;
        transition: all 0.2s ease;
        border: none;
    }

    .btn-primary {
        background: #0066cc;
    }

    .btn-success {
        background: #34c759;
    }

    .btn-warning {
        background: #ff9f0a;
    }

    .btn-secondary {
        background: #6c757d;
    }

    .btn:hover {
        transform: translateY(-1px);
        box-shadow: 0 4px 12px rgba(0,0,0,0.15);
    }

    .results-table {
        margin-top: 2rem;
        border-radius: 12px;
        overflow: hidden;
        box-shadow: 0 2px 10px rgba(0,0,0,0.05);
    }

    .table {
        margin-bottom: 0;
    }

    .table thead th {
        background: #f5f5f7;
        border-bottom: none;
        padding: 1rem;
        font-weight: 500;
        color: #1d1d1f;
    }

    .table tbody td {
        padding: 1rem;
        vertical-align: middle;
        border-top: 1px solid #d2d2d7;
    }

    .alert {
        border-radius: 12px;
        margin-bottom: 1rem;
    }

    .form-text {
        color: #86868b;
        font-size: 0.875rem;
        margin-top: 0.5rem;
    }

    textarea {
        width: 100%;
        border: none;
        resize: vertical;
        min-height: 100px;
    }

    textarea:focus {
        outline: none;
    }

    label {
        font-weight: 500;
        color: #1d1d1f;
        margin-bottom: 0.5rem;
    }
</style>

<div class="production-order-container">
    <h1 class="page-title">Production Order Management</h1>
    
    <form method="post">
        {% csrf_token %}
        <div class="form-section">
            <div class="form-group">
                <label>{{ form.production_order_numbers.label }}</label>
                <div class="input-area">
                    {{ form.production_order_numbers }}
                </div>
                <small class="form-text">{{ form.production_order_numbers.help_text }}</small>
            </div>

            <div class="checkbox-group">
                <div class="custom-checkbox">
                    {{ form.hold_16 }}
                    {{ form.hold_16.label_tag }}
                </div>
                <div class="custom-checkbox">
                    {{ form.hold_first_packing_43 }}
                    {{ form.hold_first_packing_43.label_tag }}
                </div>
                <div class="custom-checkbox">
                    {{ form.hold_carton_40 }}
                    {{ form.hold_carton_40.label_tag }}
                </div>
                <div class="custom-checkbox">
                    {{ form.hold_qa_approved_31 }}
                    {{ form.hold_qa_approved_31.label_tag }}
                </div>
            </div>

            <div class="form-group">
                <label>{{ form.reason.label }}</label>
                <div class="input-area">
                    {{ form.reason }}
                </div>
                <small class="form-text">{{ form.reason.help_text }}</small>
            </div>
        </div>

        <div class="action-buttons">
            <button type="submit" name="action" value="search" class="btn btn-primary">Search</button>
            <button type="submit" name="action" value="insert" class="btn btn-success">Insert</button>
            <button type="submit" name="action" value="release" class="btn btn-warning">Release</button>
            <button type="submit" name="action" value="reset" class="btn btn-secondary">Reset</button>
        </div>
    </form>

    {% if messages %}
    <div class="messages">
        {% for message in messages %}
        <div class="alert alert-{{ message.tags }}">
            {{ message }}
        </div>
        {% endfor %}
    </div>
    {% endif %}

    {% if results %}
    <div class="results-table">
        <table class="table">
            <thead>
                <tr>
                    <th>ID</th>
                    <th>Production Order Number</th>
                    <th>Hold</th>
                    <th>Hold First Package</th>
                    <th>Hold Carton</th>
                    <th>Hold QA Approve</th>
                    <th>Error Code</th>
                    <th>Onhold Reason</th>
                </tr>
            </thead>
            <tbody>
                {% for result in results %}
                <tr>
                    <td>{{ result.ID }}</td>
                    <td>{{ result.ProductionOrderNumber }}</td>
                    <td>{{ result.Hold|default:'' }}</td>
                    <td>{{ result.HoldFirstPackage|default:'' }}</td>
                    <td>{{ result.HoldCarton|default:'' }}</td>
                    <td>{{ result.HoldQAapprove|default:'' }}</td>
                    <td>{{ result.ErrorCode|default:'' }}</td>
                    <td>{{ result.OnholdReason|default:'' }}</td>
                </tr>
                {% endfor %}
            </tbody>
        </table>
    </div>
    {% endif %}
</div>
{% endblock %} 
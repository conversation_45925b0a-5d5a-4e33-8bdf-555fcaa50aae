from django.urls import path
from . import views
from django.contrib.admin.sites import site
from django.contrib import admin
from django.urls import include
from django.conf import settings
from django.conf.urls.static import static

app_name = 'volcanoreport'

urlpatterns = [
    path('admin/', admin.site.urls),
    path('query/', views.query_conditions_view, name='query_conditions'),
    path('query_page/<int:id>/', views.query_page, name='query_page'),
    path('get-station-line-data/', views.get_station_line_data, name='get_station_line_data'),
    path('get_part_info/', views.get_part_info, name='get_part_info'),
    path('admin/volcano_report/get_partnumbers/', views.get_partnumbers, name='get_partnumbers'),
    path('get_partnumbers/', views.get_partnumbers, name='get_partnumbers'),
    path('production_plan/', views.production_plan_view, name='production_plan'),
    path('production_plan/list/', views.production_plan_list, name='production_plan_list'),
    path('production_plan/create/', views.production_plan_create, name='production_plan_create'),
    path('production_plan/delete/', views.production_plan_delete, name='production_plan_delete'),
    path('get_lines/', views.get_lines, name='get_lines'),
    path('line_output/', views.line_output_view, name='line_output_view'),
    path('line_output/data/', views.line_output_data, name='line_output_data'),
    path('line_output/export/', views.line_output_export, name='line_output_export'),
    path('line_output/save_confirmation/', views.save_confirmation, name='save_confirmation'),
    path('week-summary/', views.week_summary_view, name='week_summary'),
    path('export-week-summary/', views.export_week_summary, name='export_week_summary'),
    path('incident_details/', views.incident_details_view, name='incident_details'),
    path('incident_details/data/', views.incident_details_data, name='incident_details_data'),
    path('incident_details/export/', views.incident_details_export, name='incident_details_export'),
]
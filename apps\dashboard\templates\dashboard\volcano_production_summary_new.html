{% extends "dashboard/base_dashboard_new.html" %}
{% load static %}

{% block title %}Volcano Production Dashboard{% endblock %}

{% block page_styles %}
<style>
    .card-header {
        padding: 1.25rem 1.5rem;
        border-bottom: 1px solid var(--border-color);
        display: flex;
        justify-content: center !important;
        align-items: center;
        text-align: center;
        width: 100%;
    }

    .card-header .card-title {
        width: 100%;
        text-align: center;
    }

    .chart-grid {
        display: grid;
        grid-template-columns: repeat(2, 1fr);
        gap: 2rem;
        align-items: start;
        margin-bottom: 2rem;
        width: 100%;
        box-sizing: border-box;
        padding-left: 2vw;
        padding-right: 2vw;
    }

    .chart-container {
        min-height: 300px;
        height: 35vh;
        max-height: 40vh;
    }
    
    .card {
        display: flex;
        flex-direction: column;
        height: 100%;
    }
    
    .card-body {
        flex: 1 1 auto;
        display: flex;
        flex-direction: column;
        justify-content: center;
    }

    @media (max-width: 900px) {
        .chart-grid {
            grid-template-columns: 1fr;
            gap: 1rem;
        }
        .chart-container {
            height: 40vh;
            min-height: 200px;
        }
    }
    
    .no-data {
        text-align: center;
        color: #bbb;
        font-size: 1.2em;
        padding: 2em 0;
    }
    
    .legend {
        display: flex;
        justify-content: center;
        flex-wrap: wrap;
        gap: 10px;
        margin-bottom: 5px;
    }
    
    .legend-item {
        display: flex;
        align-items: center;
        margin: 0 10px;
    }
    
    .legend-color {
        width: 12px;
        height: 12px;
        margin-right: 5px;
        border-radius: 2px;
    }
    
    .legend-color.actual {
        background: var(--green-primary);
    }
    
    .legend-color.gap {
        background: var(--blue-primary);
    }
    
    .legend-color.gap-neg {
        background: var(--red-primary);
    }
    
    .legend-color.unmet {
        background: var(--gray-light);
    }
    
    .legend-text {
        font-size: 0.85rem;
        color: var(--text-secondary);
    }
</style>
{% endblock %}

{% block content %}
<div class="dashboard-header">
    <h1 class="dashboard-title">Volcano Plan VS Actual</h1>
</div>

<div class="chart-grid" style="grid-template-columns: repeat(2, 1fr); width: 98vw; max-width: 1800px; margin: 0 auto;">
    <!-- First row: Hermes Packing and Alpha BB -->
    {% if packing_chart.categories %}
        <div class="card">
            <div class="card-header">
                <h2 class="card-title">{{ packing_chart.page_title|default:"Hermes Packing Plan vs Actual" }}</h2>
            </div>
            <div class="card-body">
                <div id="chartPacking" class="chart-container"></div>
                {{ packing_chart.categories|json_script:"packing_categories_data" }}
                {{ packing_chart.planned_data|json_script:"packing_planned_data" }}
                {{ packing_chart.actual_data|json_script:"packing_actual_data" }}
                {{ packing_chart.gap_abs_data|json_script:"packing_gap_abs_data" }}
                {{ packing_chart.unmet_target_data|json_script:"packing_unmet_data" }}
                {{ packing_chart.gap_sign_data|json_script:"packing_gap_sign_data" }}
                {{ packing_chart.y_axis_max|json_script:"packing_y_axis_max_data" }}
            </div>
        </div>
    {% else %}
        <div class="card">
            <div class="card-body">
                <div class="no-data">No Hermes Packing data available.</div>
            </div>
        </div>
    {% endif %}

    {% if bb_alpha_chart.categories %}
        <div class="card">
            <div class="card-header">
                <h2 class="card-title">{{ bb_alpha_chart.page_title|default:"Alpha BB Plan vs Actual" }}</h2>
            </div>
            <div class="card-body">
                <div id="chartBBAlpha" class="chart-container"></div>
                {{ bb_alpha_chart.categories|json_script:"bb_alpha_categories_data" }}
                {{ bb_alpha_chart.planned_data|json_script:"bb_alpha_planned_data" }}
                {{ bb_alpha_chart.actual_data|json_script:"bb_alpha_actual_data" }}
                {{ bb_alpha_chart.gap_abs_data|json_script:"bb_alpha_gap_abs_data" }}
                {{ bb_alpha_chart.unmet_target_data|json_script:"bb_alpha_unmet_data" }}
                {{ bb_alpha_chart.gap_sign_data|json_script:"bb_alpha_gap_sign_data" }}
                {{ bb_alpha_chart.y_axis_max|json_script:"bb_alpha_y_axis_max_data" }}
            </div>
        </div>
    {% else %}
        <div class="card">
            <div class="card-body">
                <div class="no-data">No Alpha BB data available.</div>
            </div>
        </div>
    {% endif %}

    <!-- Second row: Hermes Charger and Hermes Holder -->
    {% if bb_charger_chart.categories %}
        <div class="card">
            <div class="card-header">
                <h2 class="card-title">{{ bb_charger_chart.page_title|default:"Hermes Charger Plan vs Actual" }}</h2>
            </div>
            <div class="card-body">
                <div id="chartBBCharger" class="chart-container"></div>
                {{ bb_charger_chart.categories|json_script:"bb_charger_categories_data" }}
                {{ bb_charger_chart.planned_data|json_script:"bb_charger_planned_data" }}
                {{ bb_charger_chart.actual_data|json_script:"bb_charger_actual_data" }}
                {{ bb_charger_chart.gap_abs_data|json_script:"bb_charger_gap_abs_data" }}
                {{ bb_charger_chart.unmet_target_data|json_script:"bb_charger_unmet_data" }}
                {{ bb_charger_chart.gap_sign_data|json_script:"bb_charger_gap_sign_data" }}
                {{ bb_charger_chart.y_axis_max|json_script:"bb_charger_y_axis_max_data" }}
            </div>
        </div>
    {% else %}
        <div class="card">
            <div class="card-body">
                <div class="no-data">No Hermes Charger data available.</div>
            </div>
        </div>
    {% endif %}
    
    {% if bb_holder_chart.categories %}
        <div class="card">
            <div class="card-header">
                <h2 class="card-title">{{ bb_holder_chart.page_title|default:"Hermes Holder Plan vs Actual" }}</h2>
            </div>
            <div class="card-body">
                <div id="chartBBHolder" class="chart-container"></div>
                {{ bb_holder_chart.categories|json_script:"bb_holder_categories_data" }}
                {{ bb_holder_chart.planned_data|json_script:"bb_holder_planned_data" }}
                {{ bb_holder_chart.actual_data|json_script:"bb_holder_actual_data" }}
                {{ bb_holder_chart.gap_abs_data|json_script:"bb_holder_gap_abs_data" }}
                {{ bb_holder_chart.unmet_target_data|json_script:"bb_holder_unmet_data" }}
                {{ bb_holder_chart.gap_sign_data|json_script:"bb_holder_gap_sign_data" }}
                {{ bb_holder_chart.y_axis_max|json_script:"bb_holder_y_axis_max_data" }}
            </div>
        </div>
    {% else %}
        <div class="card">
            <div class="card-body">
                <div class="no-data">No Hermes Holder data available.</div>
            </div>
        </div>
    {% endif %}
</div>
{% endblock %}

{% block page_scripts %}
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // 全局变量
        window.chartManager = {
            charts: {},
            isInitialized: false
        };

        // 安全执行函数
        function runSafely(fn) {
            try {
                fn();
            } catch (error) {
                console.error('操作失败：', error);
            }
        }
        
        // Get JSON data safely
        function getJsonData(id) {
            const element = document.getElementById(id);
            if (!element) {
                console.warn(`Element with ID '${id}' not found.`);
                return [];
            }
            
            try {
                const content = element.textContent;
                if (!content || content.trim() === '') {
                    console.warn(`Element with ID '${id}' has empty content.`);
                    return [];
                }
                
                const data = JSON.parse(content);
                
                if (Array.isArray(data)) return data;
                if (typeof data === 'object' && data !== null) return data;
                
                console.warn(`Data from '${id}' is not an array or object:`, data);
                return [];
            } catch (e) {
                console.error(`Error parsing JSON from '${id}':`, e);
                return [];
            }
        }

        function initDashboard() {
            try {
                console.log('Starting dashboard initialization...');
                
                // 读取各数据集
                const packingCategories = getJsonData('packing_categories_data');
                const packingPlanned = getJsonData('packing_planned_data');
                const packingActual = getJsonData('packing_actual_data');
                const packingGapAbs = getJsonData('packing_gap_abs_data');
                const packingUnmet = getJsonData('packing_unmet_data');
                const packingGapSign = getJsonData('packing_gap_sign_data');
                const packingYAxisMax = getJsonData('packing_y_axis_max_data');

                // 输出数据进行调试
                console.log('Packing data:', {
                    categories: packingCategories,
                    planned: packingPlanned,
                    actual: packingActual,
                    gapAbs: packingGapAbs,
                    gapSign: packingGapSign,
                    unmet: packingUnmet
                });

                // 渲染Packing图表
                if (packingCategories && packingCategories.length > 0) {
                    renderProductionChart(
                        'chartPacking',
                        packingCategories, packingPlanned, packingActual, packingGapAbs,
                        packingUnmet, packingGapSign, packingYAxisMax
                    );
                }
                
                // 其他图表处理方式相同
                // BB Alpha
                const bbAlphaCategories = getJsonData('bb_alpha_categories_data');
                const bbAlphaPlanned = getJsonData('bb_alpha_planned_data');
                const bbAlphaActual = getJsonData('bb_alpha_actual_data');
                const bbAlphaGapAbs = getJsonData('bb_alpha_gap_abs_data');
                const bbAlphaUnmet = getJsonData('bb_alpha_unmet_data');
                const bbAlphaGapSign = getJsonData('bb_alpha_gap_sign_data');
                const bbAlphaYAxisMax = getJsonData('bb_alpha_y_axis_max_data');

                // 输出Alpha BB数据
                console.log('BB Alpha data:', {
                    categories: bbAlphaCategories,
                    planned: bbAlphaPlanned,
                    actual: bbAlphaActual,
                    gapAbs: bbAlphaGapAbs,
                    gapSign: bbAlphaGapSign,
                    unmet: bbAlphaUnmet
                });
                
                if (bbAlphaCategories && bbAlphaCategories.length > 0) {
                    renderProductionChart(
                        'chartBBAlpha',
                        bbAlphaCategories, bbAlphaPlanned, bbAlphaActual, bbAlphaGapAbs,
                        bbAlphaUnmet, bbAlphaGapSign, bbAlphaYAxisMax
                    );
                }
                
                // 其他图表同样处理
                const bbChargerCategories = getJsonData('bb_charger_categories_data');
                const bbChargerPlanned = getJsonData('bb_charger_planned_data');
                const bbChargerActual = getJsonData('bb_charger_actual_data');
                const bbChargerGapAbs = getJsonData('bb_charger_gap_abs_data');
                const bbChargerUnmet = getJsonData('bb_charger_unmet_data');
                const bbChargerGapSign = getJsonData('bb_charger_gap_sign_data');
                const bbChargerYAxisMax = getJsonData('bb_charger_y_axis_max_data');
                
                if (bbChargerCategories && bbChargerCategories.length > 0) {
                    renderProductionChart(
                        'chartBBCharger',
                        bbChargerCategories, bbChargerPlanned, bbChargerActual, bbChargerGapAbs,
                        bbChargerUnmet, bbChargerGapSign, bbChargerYAxisMax
                    );
                }
                
                const bbHolderCategories = getJsonData('bb_holder_categories_data');
                const bbHolderPlanned = getJsonData('bb_holder_planned_data');
                const bbHolderActual = getJsonData('bb_holder_actual_data');
                const bbHolderGapAbs = getJsonData('bb_holder_gap_abs_data');
                const bbHolderUnmet = getJsonData('bb_holder_unmet_data');
                const bbHolderGapSign = getJsonData('bb_holder_gap_sign_data');
                const bbHolderYAxisMax = getJsonData('bb_holder_y_axis_max_data');
                
                if (bbHolderCategories && bbHolderCategories.length > 0) {
                    renderProductionChart(
                        'chartBBHolder',
                        bbHolderCategories, bbHolderPlanned, bbHolderActual, bbHolderGapAbs,
                        bbHolderUnmet, bbHolderGapSign, bbHolderYAxisMax
                    );
                }
            } catch (error) {
                console.error('初始化报表失败：', error);
            }
        }
        
        function renderProductionChart(chartId, categories, plannedData, actualData, gapAbsData, unmetData, gapSignData, yAxisMaxValueParam) {
            try {
                // 1. 工具函数
                const utils = {
                    normalizeNumber: (val, defaultValue = 0) => {
                        if (val === null || val === undefined) return defaultValue;
                        const num = Number(val);
                        return isNaN(num) ? defaultValue : num;
                    },
                    
                    normalizeString: (val, defaultValue = '') => {
                        if (val === null || val === undefined) return defaultValue;
                        return String(val);
                    },
                    
                    normalizeArray: (arr, mapper, defaultValue) => {
                        if (!Array.isArray(arr)) {
                            console.warn('Expected array but got:', arr);
                            return [];
                        }
                        return arr.map((v, i) => {
                            try {
                                return mapper(v, defaultValue);
                            } catch (e) {
                                console.warn(`Error mapping array item at index ${i}:`, e);
                                return defaultValue;
                            }
                        });
                    },
                        
                    ensureLength: (arr, length, defaultValue) => {
                        if (!Array.isArray(arr)) {
                            console.warn('Expected array but got:', arr);
                            return Array(length).fill(defaultValue);
                        }
                        const result = [...arr];
                        while (result.length < length) result.push(defaultValue);
                        return result.slice(0, length);
                    }
                };
        
                // 2. 初始化检查
                if (!chartId) throw new Error('Chart ID is required');
        
                const element = document.getElementById(chartId);
                if (!element) throw new Error(`Element not found: ${chartId}`);
        
                // 3. 资源管理
                const cleanupOldChart = () => {
                    if (window.charts?.[chartId]) {
                        try {
                            window.charts[chartId].dispose();
                        } catch (e) {
                            console.warn(`Failed to dispose chart ${chartId}:`, e);
                        }
                    }
                };
                
                cleanupOldChart();
                window.charts = window.charts || {};
        
                // 4. 尺寸处理
                const style = window.getComputedStyle(element);
                const dims = {
                    width: Math.max(parseInt(style.width) || element.clientWidth || 400, 100),
                    height: Math.max(parseInt(style.height) || element.clientHeight || 300, 100)
                };
        
                // 5. 数据规范化
                const config = {
                    maxValue: utils.normalizeNumber(yAxisMaxValueParam, 3000),
                    dataLength: categories?.length || 0
                };
        
                const chartData = {
                    categories: utils.normalizeArray(categories, utils.normalizeString),
                    actual: utils.normalizeArray(actualData, utils.normalizeNumber),
                    planned: utils.normalizeArray(plannedData, utils.normalizeNumber),
                    gapAbs: utils.normalizeArray(gapAbsData, utils.normalizeNumber),
                    unmet: utils.normalizeArray(unmetData, utils.normalizeNumber),
                    gapSign: utils.normalizeArray(gapSignData, utils.normalizeString, 'positive')
                };
        
                // 统一数组长度
                Object.keys(chartData).forEach(key => {
                    chartData[key] = utils.ensureLength(
                        chartData[key],
                        config.dataLength,
                        key === 'categories' ? '' : key === 'gapSign' ? 'positive' : 0
                    );
                });
        
                // 6. 创建图表实例
                if (!window.echarts) {
                    console.error("ECharts library not found. Make sure it's loaded properly.");
                    return;
                }

                // 清空已存在的图表
                cleanupOldChart();
                
                // 准备容器
                element.style.minWidth = '200px';
                element.style.minHeight = '200px';
                element.style.width = '100%';
                element.style.height = '100%';
                
                // 等待下一帧确保 DOM 更新
                requestAnimationFrame(() => {
                    // 获取实际尺寸
                    const actualWidth = Math.max(element.clientWidth, 200);
                    const actualHeight = Math.max(element.clientHeight, 200);
                    
                    // 创建图表实例
                    const chart = echarts.init(element, null, {
                        renderer: 'canvas',
                        width: actualWidth,
                        height: actualHeight,
                        devicePixelRatio: window.devicePixelRatio,
                        useDirtyRect: true, // 启用局部刷新以提高性能
                        backgroundColor: 'transparent'
                    });
                    
                    // 注册到全局管理器
                    window.charts[chartId] = chart;
                    
                    // 确保容器尺寸正确
                    element.style.width = actualWidth + 'px';
                    element.style.height = actualHeight + 'px';

                    const colors = {
                        actual: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                            { offset: 0, color: '#28a745' }, { offset: 1, color: '#1e7e34' }
                        ]),
                        gap_pos: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                            { offset: 0, color: '#007bff' }, { offset: 1, color: '#0056b3' }
                        ]),
                        gap_neg: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                            { offset: 0, color: '#dc3545' }, { offset: 1, color: '#bd2130' }
                        ]),
                        unmet: '#ced4da'
                    };

                    // 4. 图表基础配置
                    const option = {
                        backgroundColor: 'transparent',
                        animation: true,
                        animationDuration: 800,
                        animationEasing: 'cubicInOut',
                        tooltip: {
                            trigger: 'axis',
                            axisPointer: { type: 'shadow' },
                            formatter: function (params) {
                                if (!params || params.length === 0 || !params[0]) { return 'No data'; }
                                const categoryIndex = params[0].dataIndex;

                                const categoryName = chartData.categories[categoryIndex] || 'N/A';
                                const plan = utils.normalizeNumber(chartData.planned[categoryIndex], 'N/A');
                                const actual = utils.normalizeNumber(chartData.actual[categoryIndex], 'N/A');

                                let gap = 'N/A';
                                if (typeof actual === 'number' && typeof plan === 'number') {
                                    gap = actual - plan;
                                }

                                let tooltipText = `<div style="font-weight:bold;font-size:14px;margin-bottom:8px;">${categoryName}</div>`;
                                tooltipText += `<div style="display:flex;justify-content:space-between;margin:5px 0;"><span>Planned:</span><span style="font-weight:bold;">${typeof plan === 'number' ? plan.toLocaleString() : plan}</span></div>`;
                                tooltipText += `<div style="display:flex;justify-content:space-between;margin:5px 0;"><span>Actual:</span><span style="font-weight:bold;color:${gap === 'N/A' || gap >= 0 ? '#28a745' : '#dc3545'};">${typeof actual === 'number' ? actual.toLocaleString() : actual}</span></div>`;
                                tooltipText += `<div style="display:flex;justify-content:space-between;margin:5px 0;"><span>Gap:</span><span style="font-weight:bold;color:${gap === 'N/A' || gap >= 0 ? '#007bff' : '#dc3545'};">${typeof gap === 'number' ? gap.toLocaleString() : gap}</span></div>`;
                                
                                // Displaying 'Remain Plan' (unmetData) in tooltip
                                const remainPlan = utils.normalizeNumber(chartData.unmet[categoryIndex], 'N/A');
                                tooltipText += `<div style="display:flex;justify-content:space-between;margin:5px 0;"><span>Remain Plan:</span><span style="font-weight:bold;">${typeof remainPlan === 'number' ? remainPlan.toLocaleString() : remainPlan}</span></div>`;


                                if (config.maxValue > 0 && typeof actual === 'number' && typeof plan === 'number' && plan > 0) {
                                    tooltipText += `<div style="display:flex;justify-content:space-between;margin:5px 0;"><span>Target Completion:</span><span style="font-weight:bold;">${Math.round((actual/plan)*100)}%</span></div>`;
                                }
                                return tooltipText;
                            },
                            backgroundColor: '#22262e',
                            borderColor: '#32383e',
                            textStyle: { color: '#e9ecef' },
                            extraCssText: 'box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15); border-radius: 8px; padding: 12px;'
                        },
                        grid: {
                            left: '8%',
                            right: '4%',
                            bottom: '15%',
                            top: '15%',
                            containLabel: true
                        },
                        legend: {
                            data: ['Actual', 'Gap', '-Gap', 'Remain Plan'],
                            textStyle: { color: '#adb5bd' },
                            top: '0',
                            left: 'center',
                            itemGap: 20,
                            icon: 'rect',
                            itemWidth: 14,
                            itemHeight: 14,
                            selected: {
                                'Actual': true,
                                'Gap': true,
                                '-Gap': true,
                                'Remain Plan': true
                            }
                        },
                        xAxis: {
                            type: 'category',
                            data: chartData.categories,
                            axisTick: { show: false },
                            axisLine: { lineStyle: { color: '#32383e' } },
                            axisLabel: {
                                color: '#adb5bd',
                                interval: 0,
                                rotate: 30,
                                margin: 12,
                                fontSize: 11,
                                formatter: value => {
                                    const str = utils.normalizeString(value);
                                    return str.length > 15 ? str.slice(0, 15) + '...' : str;
                                }
                            }
                        },
                        yAxis: [{
                            type: 'value',
                            name: 'Quantity',
                            max: config.maxValue,
                            nameTextStyle: { color: '#adb5bd', fontSize: 12, padding: [0, 0, 0, -5] },
                            splitLine: { lineStyle: { color: '#32383e', type: 'dashed' } },
                            axisLine: { show: true, lineStyle: { color: '#32383e' } },
                            axisLabel: { color: '#adb5bd', fontSize: 11,
                                formatter: function(value) { return value.toLocaleString(); }
                            }
                        }],
                        series: [
                            {
                                name: 'Actual',
                                type: 'bar',
                                stack: 'total',
                                barWidth: '60%',
                                z: 3,
                                label: {
                                    show: true,
                                    position: 'inside',
                                    color: '#fff',
                                    fontSize: 11,
                                    formatter: function(params) {
                                        return params.value > 0 ? params.value : '';
                                    }
                                },
                                itemStyle: {
                                    color: '#28a745'
                                },
                                data: chartData.actual
                            },
                            {
                                name: 'Gap',
                                type: 'bar',
                                stack: 'total',
                                barWidth: '60%',
                                z: 2,
                                label: {
                                    show: true,
                                    position: 'inside',
                                    color: '#fff',
                                    fontSize: 11
                                },
                                itemStyle: {
                                    color: '#007bff'
                                },
                                data: chartData.gapAbs.map((val, idx) => {
                                    if (chartData.gapSign[idx] === 'positive') {
                                        console.log('Positive gap:', val);
                                        return val;
                                    }
                                    return 0;
                                })
                            },
                            {
                                name: '-Gap',
                                type: 'bar',
                                stack: 'total',
                                barWidth: '60%',
                                z: 2,
                                label: {
                                    show: true,
                                    position: 'inside',
                                    color: '#fff',
                                    fontSize: 11
                                },
                                itemStyle: {
                                    color: '#dc3545'
                                },
                                data: chartData.gapAbs.map((val, idx) => {
                                    if (chartData.gapSign[idx] === 'negative') {
                                        console.log('Negative gap:', val);
                                        return val;
                                    }
                                    return 0;
                                })
                            },
                            {
                                name: 'Remain Plan',
                                type: 'bar',
                                stack: 'total',
                                barWidth: '60%',
                                z: 1,
                                label: {
                                    show: true,
                                    position: 'inside',
                                    color: '#fff',
                                    fontSize: 11,
                                    formatter: function(params) {
                                        return params.value > 0 ? params.value : '';
                                    }
                                },
                                itemStyle: {
                                    color: '#6c757d'
                                },
                                data: chartData.unmet
                            }
                        ]
                    };

                    // 设置图表配置并初始化
                    chart.setOption(option, true);
                    
                    // 设置尺寸变化监听
                    const handleResize = () => {
                        const newWidth = element.clientWidth;
                        const newHeight = element.clientHeight;
                        if (newWidth > 0 && newHeight > 0) {
                            chart.resize({
                                width: newWidth,
                                height: newHeight,
                                silent: true
                            });
                        }
                    };

                    const debouncedResize = (() => {
                        let timer;
                        return () => {
                            clearTimeout(timer);
                            timer = setTimeout(handleResize, 250);
                        };
                    })();
                    
                    // 添加 resize 事件监听
                    window.addEventListener('resize', debouncedResize);
                    
                    // 设置清理函数
                    element._chartCleanup = () => {
                        window.removeEventListener('resize', debouncedResize);
                        if (chart) {
                            try {
                                chart.dispose();
                            } catch (e) {
                                console.warn('Chart cleanup failed:', e);
                            }
                        }
                    };
                });
            } catch (error) {
                console.error('Chart rendering failed:', error);
            }
        }

        // 初始化逻辑
        function initializeDashboard() {
            if (!window.chartManager.isInitialized) {
                initDashboard();
                window.chartManager.isInitialized = true;
            }
        }

        // 页面卸载时清理
        function cleanupDashboard() {
            try {
                if (window.chartManager.charts) {
                    Object.entries(window.chartManager.charts).forEach(([id, chart]) => {
                        if (chart?.dispose) {
                            chart.dispose();
                        }
                        const elem = document.getElementById(id);
                        if (elem?._chartCleanup) {
                            elem._chartCleanup();
                        }
                    });
                    window.chartManager.charts = {};
                    window.chartManager.isInitialized = false;
                }
            } catch (err) {
                console.warn('清理失败：', err);
            }
        }

        // 安全初始化
        initializeDashboard();
        
        // 页面卸载时清理
        window.addEventListener('beforeunload', cleanupDashboard);
    });
</script>
{% endblock %} 
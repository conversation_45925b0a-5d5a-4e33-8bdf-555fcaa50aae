/*!
 KeyTable 1.2.1
 ©2010-2014 SpryMedia Ltd - datatables.net/license
*/
var KeyTable;
(function(F,n,K){var A=function(d){KeyTable=function(j){function A(a){return function(e,c,p){(null===e||"number"==typeof e)&&(null===c||"number"==typeof c)&&"function"==typeof p?k[a].push({x:e,y:c,fn:p}):"object"==typeof e&&"function"==typeof c?(e=E(e),k[a].push({x:e[0],y:e[1],fn:c})):alert("Unhandable event type was added: x"+e+"  y:"+c+"  z:"+p)}}function L(a){return function(e,c,p){(null===e||"number"==typeof e)&&(null===c||"number"==typeof c)?"function"==typeof p?B(a,e,c,p):B(a,e,c):"object"==
typeof e?(e=E(e),"function"==typeof c?B(a,e[0],e[1],c):B(a,e[0],e[1])):alert("Unhandable event type was removed: x"+e+"  y:"+c+"  z:"+p)}}function B(a,e,c,p){for(var h=0,b=0,d=k[a].length;b<d-h;b++)if("undefined"!=typeof p)k[a][b-h].x==e&&(k[a][b-h].y==c&&k[a][b-h].fn==p)&&(k[a].splice(b-h,1),h++);else if(k[a][b-h].x==e&&k[a][b-h].y==c)return k[a].splice(b,1),1;return h}function C(a,e,c){for(var p=0,a=k[a],b=0;b<a.length;b++)if(a[b].x==e&&a[b].y==c||null===a[b].x&&a[b].y==c||a[b].x==e&&null===a[b].y||
null===a[b].x&&null===a[b].y)a[b].fn(w(e,c),e,c),p++;return p}function q(a,e){if(t!=a){"undefined"==typeof e&&(e=!0);null!==t&&G(t);d(a).addClass(x);d(a).parent().addClass(x);var c;if(i){c=i;for(var b=H(a)[1],h=o;b>=c.fnDisplayEnd();)0<=c._iDisplayLength?c._iDisplayStart+c._iDisplayLength<c.fnRecordsDisplay()&&(c._iDisplayStart+=c._iDisplayLength):c._iDisplayStart=0,i.oApi._fnCalculateEnd(c);for(;b<c._iDisplayStart;)c._iDisplayStart=0<=c._iDisplayLength?c._iDisplayStart-c._iDisplayLength:0,0>c._iDisplayStart&&
(c._iDisplayStart=0),i.oApi._fnCalculateEnd(c);i.oApi._fnDraw(c);o=h}b=E(a);t=a;l=b[0];g=b[1];var r,j,k,m,f;if(e){r=d(F).height();b=d(F).width();j=d(n).scrollTop();h=d(n).scrollLeft();k=a.offsetHeight;m=a.offsetWidth;f=a;var q=0,s=0;if(f.offsetParent){q=f.offsetLeft;s=f.offsetTop;for(f=f.offsetParent;f;)q+=f.offsetLeft,s+=f.offsetTop,f=f.offsetParent}f=[q,s];if(i&&"undefined"!=typeof c.oScroll&&(""!==c.oScroll.sX||""!==c.oScroll.sY))f[1]-=d(c.nTable.parentNode).scrollTop(),f[0]-=d(c.nTable.parentNode).scrollLeft();
f[1]+k>j+r?(r=f[1]+k-r,n.documentElement.scrollTop=r,n.body.scrollTop=r):f[1]<j&&(r=f[1],n.documentElement.scrollTop=r,n.body.scrollTop=r);f[0]+m>h+b?(b=f[0]+m-b,n.documentElement.scrollLeft=b,n.body.scrollLeft=b):f[0]<h&&(b=f[0],n.documentElement.scrollLeft=b,n.body.scrollLeft=b)}if(i&&"undefined"!=typeof c.oScroll&&(""!==c.oScroll.sX||""!==c.oScroll.sY))(c=c.nTable.parentNode,r=c.clientHeight,b=c.clientWidth,j=c.scrollTop,h=c.scrollLeft,k=a.offsetHeight,m=a.offsetWidth,a.offsetTop+k>r+j?c.scrollTop=
a.offsetTop+k-r:a.offsetTop<j&&(c.scrollTop=a.offsetTop),a.offsetLeft+m>b+h)?c.scrollLeft=a.offsetLeft+m-b:a.offsetLeft<h&&(c.scrollLeft=a.offsetLeft);o||(o=!0);C("focus",l,g)}}function y(){G(t);t=g=l=null;o=!1}function G(a){d(a).removeClass(x);d(a).parent().removeClass(x);C("blur",l,g)}function I(){for(var a=this;"TD"!=a.nodeName;)a=a.parentNode;q(a);o||(o=!0)}function w(a,b){return i?"undefined"!=typeof i.aoData[i.aiDisplay[b]]?i.aoData[i.aiDisplay[b]].nTr.getElementsByTagName("td")[a]:null:d("tr:eq("+
b+")>td:eq("+a+")",z)[0]}function E(a){return i?[d("td",a.parentNode).index(a),d("tr",a.parentNode.parentNode).index(a.parentNode)+i._iDisplayStart]:[d("td",a.parentNode).index(a),d("tr",a.parentNode.parentNode).index(a.parentNode)]}function H(a){for(var b=0,c=i.aiDisplay.length;b<c;b++)for(var d=i.aoData[i.aiDisplay[b]].nTr.getElementsByTagName("td"),h=0,g=d.length;h<g;h++)if(d[h]==a)return[h,b];return null}this.block=!1;this.event={remove:{}};this.fnGetCurrentPosition=function(){return[l,g]};this.fnGetCurrentData=
function(){return t.innerHTML};this.fnGetCurrentTD=function(){return t};this.fnSetPosition=function(a,b){"object"==typeof a&&a.nodeName?q(a):q(w(a,b))};this.fnBlur=function(){y()};var z=null,t=null,l=null,g=null,J=null,x="focus",o=!1,k={action:[],esc:[],focus:[],blur:[]},i=null,D,s,u=!1,m;for(m in k)m&&(this.event[m]=A(m),this.event.remove[m]=L(m));var v;j===K?(m=d("table.KeyTable")[0],v=null):d.isPlainObject(j)?(m=j.table,v=j.datatable):(v=(new d.fn.dataTable.Api(j)).settings()[0],m=v.nTable);var b=
j,J=this;"undefined"==typeof b&&(b={});"undefined"==typeof b.focus&&(b.focus=[0,0]);b.table=m;d(b.table).addClass("KeyTable");"undefined"!=typeof b.focusClass&&(x=b.focusClass);"undefined"!=typeof v&&(i=v);"undefined"==typeof b.initScroll&&(b.initScroll=!0);"undefined"==typeof b.form&&(b.form=!1);D=b.form;z=b.table.getElementsByTagName("tbody")[0];D?(j=n.createElement("div"),s=n.createElement("input"),j.style.height="1px",j.style.width="0px",j.style.overflow="hidden","undefined"!=typeof b.tabIndex&&
(s.tabIndex=b.tabIndex),j.appendChild(s),b.table.parentNode.insertBefore(j,b.table.nextSibling),d(s).focus(function(){if(!u){o=true;u=false;typeof b.focus.nodeName!="undefined"?q(b.focus,b.initScroll):q(w(b.focus[0],b.focus[1]),b.initScroll);setTimeout(function(){s.blur()},0)}}),o=!1):("undefined"!=typeof b.focus.nodeName?q(b.focus,b.initScroll):q(w(b.focus[0],b.focus[1]),b.initScroll),o||(o=!0));d(n).bind("keydown",function(a){if(J.block||!o||a.metaKey||a.altKey||a.ctrlKey)return true;var b;b=z.getElementsByTagName("tr")[0].getElementsByTagName("td").length;
var c;if(i){c=i.aiDisplay.length;var d=H(t);if(d===null)return;l=d[0];g=d[1]}else c=z.getElementsByTagName("tr").length;d=a.keyCode==9&&a.shiftKey?-1:a.keyCode;switch(d){case 13:a.preventDefault();a.stopPropagation();C("action",l,g);return true;case 27:if(!C("esc",l,g)){y();return}a=l;b=g;break;case -1:case 37:if(l>0){a=l-1;b=g}else if(g>0){a=b-1;b=g-1}else{if(d==-1&&D){u=true;s.focus();setTimeout(function(){u=false},0);o=false;y();return true}return false}break;case 38:if(g>0){a=l;b=g-1}else return false;
break;case 36:a=l;b=0;break;case 33:a=l;b=g-10;b<0&&(b=0);break;case 9:case 39:if(l<b-1){a=l+1;b=g}else if(g<c-1){a=0;b=g+1}else{if(d==9&&D){u=true;s.focus();setTimeout(function(){u=false},0);o=false;y();return true}return false}break;case 40:if(g<c-1){a=l;b=g+1}else return false;break;case 35:a=l;b=c-1;break;case 34:a=l;b=g+10;b>c-1&&(b=c-1);break;default:return true}q(w(a,b));return false});if(i)d(i.nTable).on("click","td",I);else d(z).on("click","td",I);d(n).click(function(a){for(var a=a.target,
d=false;a;){if(a==b.table){d=true;break}a=a.parentNode}d||y()})};KeyTable.version="1.2.1";d.fn.dataTable.KeyTable=KeyTable;return d.fn.DataTable.KeyTable=KeyTable};"function"===typeof define&&define.amd?define(["jquery","datatables"],A):"object"===typeof exports?A(require("jquery"),require("datatables")):jQuery&&!jQuery.fn.dataTable.KeyTable&&A(jQuery,jQuery.fn.dataTable)})(window,document);

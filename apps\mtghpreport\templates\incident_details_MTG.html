{% extends 'base1.html' %}
{% load static %}

{% block head %}
{% endblock %}

{% block content %}
<div class="app-container">
    <h1 class="page-title">停线事件详情查询</h1>

    <div class="form-section">
        <form method="post" id="queryForm">
            {% csrf_token %}
            <div class="form-layout">
                <div class="form-group">
                    <label for="start_time">开始时间</label>
                    <input type="datetime-local" class="form-control" id="start_time" name="start_time" required>
                </div>
                <div class="form-group">
                    <label for="end_time">结束时间</label>
                    <input type="datetime-local" class="form-control" id="end_time" name="end_time" required>
                </div>
            </div>

            <div class="action-buttons">
                <button type="submit" class="btn btn-primary">查询</button>
                <button type="button" class="btn btn-info" id="exportBtn">导出Excel</button>
            </div>
        </form>
    </div>

    <!-- 数据列表 -->
    <div class="table-section mt-4">
        <table class="table table-striped" id="incidentTable">
            <thead>
                <tr>
                    <th>线别</th>
                    <th>班次</th>
                    <th>相关部门</th>
                    <th>开始时间</th>
                    <th>结束时间</th>
                    <th>停线时长(分钟)</th>
                    <th>问题描述</th>
                    <th>责任人</th>
                </tr>
            </thead>
        </table>
    </div>
</div>

<style>
/* 复用line_output.html的样式 */
.app-container {
    padding: 20px;
    position: relative;
    max-width: 100%;
    overflow-x: auto;
}

.form-section {
    background: #fff;
    padding: 30px;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    max-width: 800px;
    margin: 20px auto;
}

.form-layout {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 20px;
    align-items: end;
}

.form-group {
    margin: 0;
}

.form-group label {
    display: block;
    margin-bottom: 8px;
    font-weight: 500;
    color: #333;
    font-size: 15px;
}

.form-control {
    width: 100%;
    height: 38px;
    border: 1px solid #ddd;
    border-radius: 4px;
    padding: 8px 12px;
}

.action-buttons {
    margin-top: 30px;
    display: flex;
    gap: 15px;
    justify-content: center;
}

.btn {
    min-width: 120px;
    height: 38px;
    font-size: 15px;
}

.table-section {
    background: #fff;
    padding: 20px;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    margin-top: 20px;
    overflow-x: auto;
    width: 100%;
}

.page-title {
    text-align: center;
    margin-bottom: 30px;
    font-size: 24px;
    color: #333;
    font-weight: 500;
}

/* 添加表格相关样式 */
.table-warning {
    background-color: #fff3cd !important;
}

.table-section table.dataTable tbody tr.table-warning:hover {
    background-color: #ffe7b6 !important;
}

/* 调整表格单元格样式 */
#incidentTable td {
    vertical-align: middle;
    white-space: normal;
    word-break: break-word;
}

/* 问题描述列样式 */
#incidentTable td:nth-child(7) {
    max-width: 300px;
    min-width: 200px;
}

/* 时间列样式 */
#incidentTable td:nth-child(4),
#incidentTable td:nth-child(5) {
    white-space: nowrap;
}
</style>

{% endblock %}

{% block extra_js %}
<script>
$(document).ready(function() {
    // 将formatDateTime函数移到全局作用域
    function formatDateTime(dateTimeStr) {
        // 解析datetime-local输入的值
        var parts = dateTimeStr.split('T');
        if (parts.length !== 2) {
            console.error('Invalid datetime format:', dateTimeStr);
            return '';
        }
        return parts[0] + ' ' + parts[1] + ':00';
    }

    // 设置默认时间范围（今天8点到明天8点）
    function setDefaultTimeRange() {
        var today = new Date();
        var tomorrow = new Date(today);
        tomorrow.setDate(today.getDate() + 1);
        
        var startDate = new Date(today.getFullYear(), today.getMonth(), today.getDate(), 8, 0);
        var endDate = new Date(tomorrow.getFullYear(), tomorrow.getMonth(), tomorrow.getDate(), 8, 0);
        
        var startStr = startDate.getFullYear() + '-' + 
                      String(startDate.getMonth() + 1).padStart(2, '0') + '-' + 
                      String(startDate.getDate()).padStart(2, '0') + 'T08:00';
        
        var endStr = endDate.getFullYear() + '-' + 
                    String(endDate.getMonth() + 1).padStart(2, '0') + '-' + 
                    String(endDate.getDate()).padStart(2, '0') + 'T08:00';
        
        $('#start_time').val(startStr);
        $('#end_time').val(endStr);
    }
    
    setDefaultTimeRange();

    // 初始化DataTable
    var table = $('#incidentTable').DataTable({
        processing: true,
        serverSide: false,
        searching: true,
        ordering: true,
        paging: true,
        info: true,
        lengthMenu: [[10, 25, 50, -1], [10, 25, 50, "全部"]],
        dom: '<"top"lf>rt<"bottom"ip><"clear">',
        language: {
            url: "//cdn.datatables.net/plug-ins/1.10.21/i18n/Chinese.json"
        },
        columns: [
            { data: 'LineName' },
            { data: 'Shift' },
            { data: 'RelatedDept' },
            { 
                data: 'StartTime',
                render: function(data) {
                    return moment(data).format('YYYY-MM-DD HH:mm:ss');
                }
            },
            { 
                data: 'EndTime',
                render: function(data) {
                    return data ? moment(data).format('YYYY-MM-DD HH:mm:ss') : '-';
                }
            },
            { 
                data: 'StopTime',
                type: 'num',  // 指定列类型为数字
                render: function(data) {
                    return data;  // 只返回数字，不添加"分钟"文本
                }
            },
            { 
                data: 'IssueDescription',
                render: function(data) {
                    return data || '-';
                }
            },
            { 
                data: 'ResponsiblePerson',
                render: function(data) {
                    return data || '-';
                }
            }
        ],
        order: [[3, 'desc']], // 按开始时间倒序排序
        createdRow: function(row, data, dataIndex) {
            // 如果没有结束时间，说明事件仍在进行中
            if (!data.EndTime) {
                $(row).addClass('table-warning');
            }
        }
    });

    // 表单提交处理
    $('#queryForm').on('submit', function(e) {
        e.preventDefault();
        
        var loadingIndex = layer.load(1, {
            shade: [0.3, '#fff'],
            content: '<div class="loading-text">正在查询数据，请稍候...</div>'
        });
        
        var startTime = formatDateTime($('#start_time').val());
        var endTime = formatDateTime($('#end_time').val());
        
        $.ajax({
            url: '{% url "volcanoreport:incident_details_data" %}',
            type: 'POST',
            data: {
                'start_time': startTime,
                'end_time': endTime,
                'csrfmiddlewaretoken': '{{ csrf_token }}'
            },
            success: function(response) {
                layer.close(loadingIndex);
                if(response.data) {
                    table.clear().rows.add(response.data).draw();
                    layer.msg('查询成功', {icon: 1});
                } else {
                    layer.msg('查询失败：' + response.error, {icon: 2});
                }
            },
            error: function(xhr) {
                console.error('Error:', xhr.responseText);
                layer.close(loadingIndex);
                layer.msg('系统错误，请稍后重试', {icon: 2});
            }
        });
    });

    // 导出Excel功能
    $('#exportBtn').on('click', function() {
        var startTime = formatDateTime($('#start_time').val());
        var endTime = formatDateTime($('#end_time').val());
        
        window.location.href = `{% url 'volcanoreport:incident_details_export' %}?start_time=${encodeURIComponent(startTime)}&end_time=${encodeURIComponent(endTime)}`;
    });
});
</script>
{% endblock %} 
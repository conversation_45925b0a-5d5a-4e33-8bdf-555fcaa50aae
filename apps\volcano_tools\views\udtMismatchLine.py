from django.shortcuts import render
from django.http import JsonResponse
from django.db import connections
from django.views import View
from apps.volcano_tools.models import UdtMismatchLine
from django.views.decorators.csrf import csrf_exempt
import json
from django.db.models import Q
from django.utils.decorators import method_decorator
from django.core.cache import cache
import logging
import re

logger = logging.getLogger(__name__)

@method_decorator(csrf_exempt, name='dispatch')
class UdtMismatchLineView(View):
    MAX_BATCH_SIZE = 10000
    CACHE_TIMEOUT = 300  # 5分钟缓存

    def get(self, request):
        """渲染主页面，只返回最新的100条记录"""
        latest_records = UdtMismatchLine.objects.all().order_by('-ID')[:100]
        return render(request, 'UdtMismatchLine/UdtMismatchLine.html', {
            'initial_data': list(latest_records.values('ID', 'UnitID', 'Serialnumber'))
        })

    def post(self, request):
        """处理所有POST请求"""
        try:
            action = request.path.split('/')[-2]
            data = json.loads(request.body)
            
            handlers = {
                'data': self._handle_datatable_query,
                'batch-query': self._handle_batch_query,
                'batch-insert': self._handle_batch_insert
            }
            
            handler = handlers.get(action)
            if not handler:
                return JsonResponse({'status': 'error', 'message': '无效的操作'}, status=400)
                
            return handler(request, data)
            
        except json.JSONDecodeError:
            return JsonResponse({'status': 'error', 'message': '无效的JSON数据'}, status=400)
        except Exception as e:
            logger.error(f"操作失败: {str(e)}")
            return JsonResponse({'status': 'error', 'message': str(e)}, status=500)

    def _validate_serial_numbers(self, serial_numbers_text):
        """验证序列号格式"""
        serial_numbers = [sn.strip() for sn in serial_numbers_text.split('\n') if sn.strip()]
        
        if not serial_numbers:
            raise ValueError('请输入有效的序列号')
            
        if len(serial_numbers) > self.MAX_BATCH_SIZE:
            raise ValueError(f'一次最多处理{self.MAX_BATCH_SIZE}个序列号')
            
        # 更灵活的序列号格式验证规则
        # 1. 支持有空格分隔的格式：TZYM 3KB P7V SUG7
        # 2. 支持无空格的格式：002D00490004003DDCA6
        pattern = r'^([A-Z0-9]{4}\s[A-Z0-9]{3}\s[A-Z0-9]{3}\s[A-Z0-9]{3,4}|[A-Z0-9]{16,24})$'
        
        invalid_sns = []
        for sn in serial_numbers:
            if not re.match(pattern, sn):
                invalid_sns.append(sn)
        
        if invalid_sns:
            error_msg = '以下序列号格式不正确:\n'
            error_msg += '支持的格式示例:\n'
            error_msg += '1. TZYM 3KB P7V SUG7\n'
            error_msg += '2. 002D00490004003DDCA6\n'
            error_msg += '\n不正确的序列号:\n'
            error_msg += '\n'.join(invalid_sns[:5])
            if len(invalid_sns) > 5:
                error_msg += '\n...'
            raise ValueError(error_msg)
            
        # 处理序列号格式
        processed_sns = []
        for sn in serial_numbers:
            # 如果是空格分隔的格式，保持原样
            if ' ' in sn:
                processed_sns.append(' '.join(sn.split()))
            # 如果是无空格格式，直接使用
            else:
                processed_sns.append(sn)
            
        return processed_sns

    def _handle_datatable_query(self, request, data):
        """处理DataTable的数据请求"""
        try:
            draw = int(data.get('draw', 1))
            start = int(data.get('start', 0))
            length = min(int(data.get('length', 10)), 50)
            search_value = data.get('search', {}).get('value', '')
            
            # 获取存储在session中的查询序列号
            query_serial_numbers = request.session.get('query_serial_numbers', [])
            
            # 构建查询
            queryset = UdtMismatchLine.objects.all()
            
            # 如果有特定的查询序列号，使用这些序列号过滤
            if query_serial_numbers:
                queryset = queryset.filter(Serialnumber__in=query_serial_numbers)
            # 否则，如果有搜索值，使用搜索值过滤
            elif search_value:
                queryset = queryset.filter(
                    Q(Serialnumber__icontains=search_value) |
                    Q(UnitID__icontains=search_value)
                )
            # 如果既没有查询序列号也没有搜索值，只返回最新的100条
            else:
                queryset = queryset.order_by('-ID')[:100]
            
            total = queryset.count()
            
            # 排序
            order_column = int(data['order'][0]['column'])
            order_dir = data['order'][0]['dir']
            columns = ['ID', 'UnitID', 'Serialnumber']
            
            if order_column < len(columns):
                column = columns[order_column]
                if order_dir == 'desc':
                    column = f'-{column}'
                queryset = queryset.order_by(column)
            
            # 分页
            records = queryset[start:start + length]
            
            return JsonResponse({
                'draw': draw,
                'recordsTotal': total,
                'recordsFiltered': total,
                'data': list(records.values('ID', 'UnitID', 'Serialnumber'))
            })
            
        except Exception as e:
            logger.error(f"DataTable查询失败: {str(e)}")
            return JsonResponse({
                'draw': draw,
                'recordsTotal': 0,
                'recordsFiltered': 0,
                'data': [],
                'error': str(e)
            })

    def _handle_batch_query(self, request, data):
        """处理批量查询请求"""
        try:
            serial_numbers = self._validate_serial_numbers(data.get('serial_numbers', ''))
            
            # 将查询的序列号存储在session中
            request.session['query_serial_numbers'] = serial_numbers

            print(request.session['query_serial_numbers'])
            
            results = UdtMismatchLine.objects.filter(
                Serialnumber__in=serial_numbers
            ).values('ID', 'UnitID', 'Serialnumber')
            
            return JsonResponse({
                'status': 'success',
                'data': list(results)
            })
            
        except ValueError as e:
            return JsonResponse({
                'status': 'error',
                'message': str(e)
            }, status=400)

    def _handle_batch_insert(self, request, data):
        """处理批量插入请求"""
        try:
            serial_numbers = self._validate_serial_numbers(data.get('serial_numbers', ''))
            
            if not serial_numbers:
                return JsonResponse({
                    'status': 'error',
                    'message': '请输入有效的序列号'
                }, status=400)

            # 构建SQL插入语句
            serial_numbers_str = ','.join([f"'{sn}'" for sn in serial_numbers])
            
            with connections['VolcanoFFDB'].cursor() as cursor:
                # 先检查哪些序列号已经存在
                cursor.execute(f"""
                    SELECT Serialnumber 
                    FROM UdtMismatchLine 
                    WHERE Serialnumber IN ({serial_numbers_str})
                """)
                existing_sns = {row[0] for row in cursor.fetchall()}
                
                # 过滤出需要插入的序列号
                sns_to_insert = [sn for sn in serial_numbers if sn not in existing_sns]
                
                if not sns_to_insert:
                    return JsonResponse({
                        'status': 'error',
                        'message': '所有序列号都已存在，无需重复插入'
                    })
                
                # 构建插入语句
                sns_to_insert_str = ','.join([f"'{sn}'" for sn in sns_to_insert])
                sql = f"""
                    INSERT INTO UdtMismatchLine (UnitID, Serialnumber)
                    SELECT unitid, value
                    FROM dbo.ffSerialNumber
                    WHERE value IN ({sns_to_insert_str})
                """
                
                try:
                    # 执行插入
                    cursor.execute(sql)
                    
                    # 验证插入结果
                    cursor.execute(f"""
                        SELECT COUNT(*) 
                        FROM UdtMismatchLine 
                        WHERE Serialnumber IN ({sns_to_insert_str})
                    """)
                    inserted_count = cursor.fetchone()[0]
                    
                    # 查询未能插入的序列号（在ffSerialNumber中不存在的）
                    cursor.execute(f"""
                        SELECT value
                        FROM (
                            SELECT value 
                            FROM (VALUES {','.join([f"('{sn}')" for sn in sns_to_insert])}) AS t(value)
                        ) AS source
                        WHERE value NOT IN (
                            SELECT Serialnumber
                            FROM UdtMismatchLine
                            WHERE Serialnumber IN ({sns_to_insert_str})
                        )
                    """)
                    failed_sns = [row[0] for row in cursor.fetchall()]
                    
                    # 构建响应消息
                    message_parts = []
                    if inserted_count > 0:
                        message_parts.append(f'成功插入 {inserted_count} 条记录')
                    if existing_sns:
                        message_parts.append(f'已存在 {len(existing_sns)} 条记录')
                    if failed_sns:
                        message_parts.append('\n\n以下序列号插入失败 (可能序列号不存在):\n' + 
                                          '\n'.join(failed_sns[:5]) +
                                          ('\n...' if len(failed_sns) > 5 else ''))
                    
                    message = '\n'.join(message_parts)
                    
                    return JsonResponse({
                        'status': 'success',
                        'message': message,
                        'inserted_count': inserted_count,
                        'existing_count': len(existing_sns),
                        'error_count': len(failed_sns)
                    })
                    
                except Exception as e:
                    logger.error(f"插入失败: {str(e)}")
                    return JsonResponse({
                        'status': 'error',
                        'message': f'插入失败: {str(e)}'
                    }, status=500)
            
        except ValueError as e:
            return JsonResponse({
                'status': 'error',
                'message': str(e)
            }, status=400)
        except Exception as e:
            logger.error(f"批量插入失败: {str(e)}")
            return JsonResponse({
                'status': 'error',
                'message': f'系统错误: {str(e)}'
            }, status=500)
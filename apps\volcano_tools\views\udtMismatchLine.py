from django.shortcuts import render
from django.http import JsonResponse
from django.db import connections
from django.views import View
from apps.volcano_tools.models import UdtMismatchLine
from django.views.decorators.csrf import csrf_exempt
import json
from django.db.models import Q
from django.utils.decorators import method_decorator
from django.core.cache import cache
import logging
import re

logger = logging.getLogger(__name__)

@method_decorator(csrf_exempt, name='dispatch')
class UdtMismatchLineView(View):
    MAX_BATCH_SIZE = 10000
    CACHE_TIMEOUT = 300  # 5分钟缓存

    def get(self, request):
        """渲染主页面，只返回最新的100条记录"""
        latest_records = UdtMismatchLine.objects.all().order_by('-ID')[:100]
        return render(request, 'UdtMismatchLine/UdtMismatchLine.html', {
            'initial_data': list(latest_records.values('ID', 'UnitID', 'Serialnumber'))
        })

    def post(self, request):
        """处理所有POST请求"""
        try:
            action = request.path.split('/')[-2]
            data = json.loads(request.body)
            
            handlers = {
                'data': self._handle_datatable_query,
                'batch-query': self._handle_batch_query,
                'batch-insert': self._handle_batch_insert
            }
            
            handler = handlers.get(action)
            if not handler:
                return JsonResponse({'status': 'error', 'message': '无效的操作'}, status=400)
                
            return handler(request, data)
            
        except json.JSONDecodeError:
            return JsonResponse({'status': 'error', 'message': '无效的JSON数据'}, status=400)
        except Exception as e:
            logger.error(f"操作失败: {str(e)}")
            return JsonResponse({'status': 'error', 'message': str(e)}, status=500)

    def _validate_serial_numbers(self, serial_numbers_text):
        """验证序列号格式"""
        serial_numbers = [sn.strip() for sn in serial_numbers_text.split('\n') if sn.strip()]
        
        if not serial_numbers:
            raise ValueError('请输入有效的序列号')
            
        if len(serial_numbers) > self.MAX_BATCH_SIZE:
            raise ValueError(f'一次最多处理{self.MAX_BATCH_SIZE}个序列号')
            
        # 更灵活的序列号格式验证规则
        # 1. 支持有空格分隔的格式：TZYM 3KB P7V SUG7
        # 2. 支持无空格的格式：002D00490004003DDCA6
        pattern = r'^([A-Z0-9]{4}\s[A-Z0-9]{3}\s[A-Z0-9]{3}\s[A-Z0-9]{3,4}|[A-Z0-9]{16,24})$'
        
        invalid_sns = []
        for sn in serial_numbers:
            if not re.match(pattern, sn):
                invalid_sns.append(sn)
        
        if invalid_sns:
            error_msg = '以下序列号格式不正确:\n'
            error_msg += '支持的格式示例:\n'
            error_msg += '1. TZYM 3KB P7V SUG7\n'
            error_msg += '2. 002D00490004003DDCA6\n'
            error_msg += '\n不正确的序列号:\n'
            error_msg += '\n'.join(invalid_sns[:5])
            if len(invalid_sns) > 5:
                error_msg += '\n...'
            raise ValueError(error_msg)
            
        # 处理序列号格式
        processed_sns = []
        for sn in serial_numbers:
            # 如果是空格分隔的格式，保持原样
            if ' ' in sn:
                processed_sns.append(' '.join(sn.split()))
            # 如果是无空格格式，直接使用
            else:
                processed_sns.append(sn)
            
        return processed_sns

    def _handle_datatable_query(self, request, data):
        """处理DataTable的数据请求"""
        try:
            draw = int(data.get('draw', 1))
            start = int(data.get('start', 0))
            length = min(int(data.get('length', 10)), 50) # 限制每页最大数量
            search_value = data.get('search', {}).get('value', '')
            order_column_index = int(data.get('order', [{}])[0].get('column', 0))
            order_dir = data.get('order', [{}])[0].get('dir', 'asc')

            # 基础查询
            base_queryset = UdtMismatchLine.objects.all()
            total_records = base_queryset.count() # 未过滤的总数

            # 获取存储在session中的查询序列号
            query_serial_numbers = request.session.get('query_serial_numbers', [])

            # 应用过滤
            queryset = base_queryset
            is_filtered = False
            if query_serial_numbers:
                queryset = queryset.filter(Serialnumber__in=query_serial_numbers)
                is_filtered = True
            elif search_value:
                queryset = queryset.filter(
                    Q(Serialnumber__icontains=search_value) |
                    Q(UnitID__icontains=search_value)
                )
                is_filtered = True

            filtered_records = queryset.count() # 过滤后的总数

            # 应用排序
            columns = ['ID', 'UnitID', 'Serialnumber']
            if 0 <= order_column_index < len(columns):
                column_name = columns[order_column_index]
                if order_dir == 'desc':
                    column_name = f'-{column_name}'
                # 确保在切片前排序
                queryset = queryset.order_by(column_name)
            elif not is_filtered:
                 # 如果没有过滤条件且没有指定排序，默认按 ID 降序
                 queryset = queryset.order_by('-ID')

            # 应用分页 (切片)
            # 如果没有过滤条件，限制最多显示100条
            if not is_filtered:
                 # 确保 start 和 length 在 100 条范围内
                 effective_length = min(length, max(0, 100 - start))
                 if effective_length > 0:
                     records = queryset[start : start + effective_length]
                 else:
                     # 如果 start 超出范围，返回空 QuerySet
                     records = UdtMismatchLine.objects.none()
                 # 过滤后的记录数也限制为100
                 filtered_records = min(filtered_records, 100)
            else:
                records = queryset[start : start + length]

            return JsonResponse({
                'draw': draw,
                'recordsTotal': total_records, # 未过滤的总数
                'recordsFiltered': filtered_records, # 过滤后的总数
                'data': list(records.values('ID', 'UnitID', 'Serialnumber'))
            })

        except Exception as e:
            logger.error(f"DataTable查询失败: {str(e)}")
            # 尝试获取 draw，如果 data 不是字典或不存在，则默认为 1
            draw = int(data.get('draw', 1)) if isinstance(data, dict) else 1
            return JsonResponse({
                'draw': draw,
                'recordsTotal': 0,
                'recordsFiltered': 0,
                'data': [],
                'error': f'处理请求时发生错误: {str(e)}' # 提供更具体的错误信息
            }, status=500) # 返回 500 状态码表示服务器错误

    def _handle_batch_query(self, request, data):
        """处理批量查询请求"""
        try:
            serial_numbers = self._validate_serial_numbers(data.get('serial_numbers', ''))
            
            # 将查询的序列号存储在session中
            request.session['query_serial_numbers'] = serial_numbers

            print(request.session['query_serial_numbers'])
            
            results = UdtMismatchLine.objects.filter(
                Serialnumber__in=serial_numbers
            ).values('ID', 'UnitID', 'Serialnumber')
            
            return JsonResponse({
                'status': 'success',
                'data': list(results)
            })
            
        except ValueError as e:
            return JsonResponse({
                'status': 'error',
                'message': str(e)
            }, status=400)

    def _handle_batch_insert(self, request, data):
        """处理批量插入请求"""
        try:
            serial_numbers = self._validate_serial_numbers(data.get('serial_numbers', ''))
            
            if not serial_numbers:
                return JsonResponse({
                    'status': 'error',
                    'message': '请输入有效的序列号'
                }, status=400)

            # 构建SQL插入语句
            serial_numbers_str = ','.join([f"'{sn}'" for sn in serial_numbers])
            
            with connections['VolcanoFFDB'].cursor() as cursor:
                # 先检查哪些序列号已经存在
                cursor.execute(f"""
                    SELECT Serialnumber 
                    FROM UdtMismatchLine 
                    WHERE Serialnumber IN ({serial_numbers_str})
                """)
                existing_sns = {row[0] for row in cursor.fetchall()}
                
                # 过滤出需要插入的序列号
                sns_to_insert = [sn for sn in serial_numbers if sn not in existing_sns]
                
                if not sns_to_insert:
                    return JsonResponse({
                        'status': 'error',
                        'message': '所有序列号都已存在，无需重复插入'
                    })
                
                # 构建插入语句
                sns_to_insert_str = ','.join([f"'{sn}'" for sn in sns_to_insert])
                sql = f"""
                    INSERT INTO UdtMismatchLine (UnitID, Serialnumber)
                    SELECT unitid, value
                    FROM dbo.ffSerialNumber
                    WHERE value IN ({sns_to_insert_str})
                """
                
                try:
                    # 执行插入
                    cursor.execute(sql)
                    
                    # 验证插入结果
                    cursor.execute(f"""
                        SELECT COUNT(*) 
                        FROM UdtMismatchLine 
                        WHERE Serialnumber IN ({sns_to_insert_str})
                    """)
                    inserted_count = cursor.fetchone()[0]
                    
                    # 查询未能插入的序列号（在ffSerialNumber中不存在的）
                    cursor.execute(f"""
                        SELECT value
                        FROM (
                            SELECT value 
                            FROM (VALUES {','.join([f"('{sn}')" for sn in sns_to_insert])}) AS t(value)
                        ) AS source
                        WHERE value NOT IN (
                            SELECT Serialnumber
                            FROM UdtMismatchLine
                            WHERE Serialnumber IN ({sns_to_insert_str})
                        )
                    """)
                    failed_sns = [row[0] for row in cursor.fetchall()]
                    
                    # 构建响应消息
                    message_parts = []
                    if inserted_count > 0:
                        message_parts.append(f'成功插入 {inserted_count} 条记录')
                    if existing_sns:
                        message_parts.append(f'已存在 {len(existing_sns)} 条记录')
                    if failed_sns:
                        message_parts.append('\n\n以下序列号插入失败 (可能序列号不存在):\n' + 
                                          '\n'.join(failed_sns[:5]) +
                                          ('\n...' if len(failed_sns) > 5 else ''))
                    
                    message = '\n'.join(message_parts)
                    
                    return JsonResponse({
                        'status': 'success',
                        'message': message,
                        'inserted_count': inserted_count,
                        'existing_count': len(existing_sns),
                        'error_count': len(failed_sns)
                    })
                    
                except Exception as e:
                    logger.error(f"插入失败: {str(e)}")
                    return JsonResponse({
                        'status': 'error',
                        'message': f'插入失败: {str(e)}'
                    }, status=500)
            
        except ValueError as e:
            return JsonResponse({
                'status': 'error',
                'message': str(e)
            }, status=400)
        except Exception as e:
            logger.error(f"批量插入失败: {str(e)}")
            return JsonResponse({
                'status': 'error',
                'message': f'系统错误: {str(e)}'
            }, status=500)



from django import forms
import datetime
from .models import luPartFamilyType, ffStationType, ffLine  # 假设这些是你的模型

class QueryForm(forms.Form):
    @staticmethod
    def create_query_form(instance, initial=None):
        query_form = QueryForm(initial=initial)

        if instance.SNTextarea:
            query_form.fields['SNTextarea'] = forms.CharField(
                label='Serial Number Textarea',
                widget=forms.Textarea(attrs={'class': 'form-control', 'rows': 3}),
                required=False
            )
        if instance.PartnumberTextarea:
            query_form.fields['PartNumber'] = forms.CharField(
                label='Part Number',
                widget=forms.Textarea(attrs={'class': 'form-control', 'rows': 3}),
                required=False
            )
        if instance.Partfamily:
            query_form.fields['part_family_type'] = forms.ModelChoiceField(
                queryset=luPartFamilyType.objects.all(),
                label='Part Family Type',
                widget=forms.Select(attrs={'class': 'form-control'}),
                empty_label="Select a Type",
                required=False

            )

        if instance.StationType:
            query_form.fields['station_type_list'] = forms.MultipleChoiceField(
                label='Station Type List',
                widget=forms.CheckboxSelectMultiple(attrs={'class': 'form-check-input', 'id': 'id_station_type_list'}),
                required=False
            )

        if instance.LineName:
            query_form.fields['LineName'] = forms.MultipleChoiceField(
                label='Line Name',
                widget=forms.CheckboxSelectMultiple(attrs={'class': 'form-check-input', 'id': 'id_line_name'}),
                required=False
            )
        if instance.TimeInput:
            today = datetime.datetime.now().date()
            query_form.fields['StartTime'] = forms.DateTimeField(
                label='Start Time',
                widget=forms.DateTimeInput(attrs={'class': 'form-control', 'type': 'datetime-local'},
                                           format='%Y-%m-%dT%H:%M'),
                initial=datetime.datetime.combine(today, datetime.time(8, 0)).strftime('%Y-%m-%dT%H:%M')
            )
            query_form.fields['EndTime'] = forms.DateTimeField(
                label='End Time',
                widget=forms.DateTimeInput(attrs={'class': 'form-control', 'type': 'datetime-local'},
                                           format='%Y-%m-%dT%H:%M'),
                initial=datetime.datetime.combine(today, datetime.time(20, 0)).strftime('%Y-%m-%dT%H:%M')
            )
        if instance.MPN:
            query_form.fields['MPN'] = forms.CharField(
                label='MPN',
                max_length=100,
                widget=forms.TextInput(attrs={'class': 'form-control'}),
                required=False
            )
        if instance.LotCode:
            query_form.fields['LotCode'] = forms.CharField(
                label='Lot Code',
                max_length=100,
                widget=forms.TextInput(attrs={'class': 'form-control'}),
                required=False
            )
        if instance.DateCode:
            query_form.fields['DateCode'] = forms.CharField(
                label='Date Code',
                max_length=100,
                widget=forms.TextInput(attrs={'class': 'form-control'}),
                required=False
            )
        if instance.Supplier:
            query_form.fields['Supplier'] = forms.CharField(
                label='Supplier',
                max_length=100,
                widget=forms.TextInput(attrs={'class': 'form-control'}),
                required=False
            )

        return query_form


from django import forms
from .models import UdtSMTScrapRecord
from django.db import connections

# class UdtSMTScrapRecordForm(forms.ModelForm):
#
#     print('UdtSMTScrapRecordForm')
#     class Meta:
#         model = UdtSMTScrapRecord
#         fields = '__all__'
#
#     def clean_serialnumber(self):
#         serialnumber = self.cleaned_data.get('serialnumber')
#         if serialnumber:
#             with connections['VolcanoReporDB'].cursor() as cursor:
#                 cursor.execute("""
#                     SELECT p.PartNumber, po.ProductionOrderNumber
#                     FROM dbo.ffSerialNumber(nolock) sn
#                     JOIN dbo.ffUnit(nolock)  u ON u.id=sn.UnitID
#                     LEFT JOIN dbo.ffPart(nolock)  p ON p.id=u.PartID
#                     LEFT JOIN dbo.ffProductionOrder(nolock)  po ON po.ID=u.ProductionOrderID
#                     WHERE sn.value=%s
#                 """, [serialnumber])
#                 result = cursor.fetchone()
#                 if result:
#                     self.cleaned_data['Partnumber'] = result[0]
#                     self.cleaned_data['PONumber'] = result[1]
#                 else:
#                     raise forms.ValidationError("无法找到对应的型号和工单信息")
#         return serialnumber


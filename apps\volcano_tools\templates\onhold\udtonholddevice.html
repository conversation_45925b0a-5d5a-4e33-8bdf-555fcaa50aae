{% extends "base1.html" %}
{% load static %}

{% block extra_css %}
<link rel="stylesheet" href="{% static 'js/plugins/layer/skin/layer.css' %}">
<style>
    .app-container {
        padding: 15px;
        max-width: 1200px;
        margin: 0 auto;
    }

    .page-title {
        font-size: 1.5rem;
        margin-bottom: 1rem;
        color: #333;
        border-bottom: 2px solid #eee;
        padding-bottom: 0.5rem;
    }

    .form-section {
        background: #f8f9fa;
        border-radius: 8px;
        padding: 15px;
        margin-bottom: 1rem;
    }

    .form-layout {
        display: grid;
        grid-template-columns: 1fr 1fr;
        gap: 20px;
    }

    .form-column {
        display: flex;
        flex-direction: column;
    }

    .form-group {
        margin-bottom: 1rem;
    }

    .form-group label {
        font-weight: 500;
        color: #495057;
        margin-bottom: 0.3rem;
        display: block;
    }

    .input-area {
        width: 100%;
    }

    .full-height-textarea {
        width: 100%;
        height: 200px;
        resize: vertical;
        border: 1px solid #ced4da;
        border-radius: 4px;
        padding: 8px;
    }

    .input-section input {
        width: 100%;
        padding: 6px 12px;
        border: 1px solid #ced4da;
        border-radius: 4px;
        margin-bottom: 0.5rem;
    }

    .options-section {
        background: white;
        border-radius: 6px;
        padding: 15px;
        box-shadow: 0 1px 3px rgba(0,0,0,0.1);
    }

    .section-title {
        font-size: 1rem;
        margin-bottom: 1rem;
        color: #495057;
    }

    .checkbox-grid {
        display: grid;
        grid-template-columns: repeat(2, 1fr);
        gap: 10px;
    }

    .custom-checkbox {
        display: flex;
        align-items: center;
        margin: 5px 0;
    }

    .custom-checkbox input[type="checkbox"] {
        margin-right: 8px;
    }

    .custom-checkbox label {
        margin: 0;
        font-size: 0.9rem;
        color: #495057;
    }

    .action-buttons {
        display: flex;
        gap: 10px;
        margin-top: 1rem;
        padding: 10px 0;
        border-top: 1px solid #eee;
    }

    .btn {
        padding: 8px 20px;
        border-radius: 4px;
        font-weight: 500;
        transition: all 0.2s;
    }

    .btn-primary {
        background-color: #007bff;
        border-color: #007bff;
    }

    .btn-secondary {
        background-color: #6c757d;
        border-color: #6c757d;
    }

    .btn-warning {
        background-color: #ffc107;
        border-color: #ffc107;
        color: #000;
    }

    .results-section {
        margin-top: 1rem;
    }

    .results-table {
        overflow-x: auto;
        background: white;
        border-radius: 8px;
        box-shadow: 0 1px 3px rgba(0,0,0,0.1);
    }

    .table {
        width: 100%;
        margin-bottom: 0;
    }

    .table th {
        background-color: #f8f9fa;
        font-weight: 500;
        padding: 12px;
        border-bottom: 2px solid #dee2e6;
    }

    .table td {
        padding: 10px 12px;
        border-bottom: 1px solid #dee2e6;
        vertical-align: middle;
    }

    .table tr:hover {
        background-color: #f8f9fa;
    }

    /* 响应式优化 */
    @media (max-width: 768px) {
        .form-layout {
            grid-template-columns: 1fr;
        }
        
        .checkbox-grid {
            grid-template-columns: 1fr;
        }
        
        .action-buttons {
            flex-direction: column;
        }
        
        .btn {
            width: 100%;
            margin-bottom: 0.5rem;
        }
    }

    /* 添加加载动画样式 */
    .loading-overlay {
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: rgba(0, 0, 0, 0.5);
        display: flex;
        justify-content: center;
        align-items: center;
        z-index: 9999;
    }

    .loading-content {
        background: white;
        padding: 20px;
        border-radius: 8px;
        text-align: center;
        box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    }

    .loading-spinner {
        width: 50px;
        height: 50px;
        border: 5px solid #f3f3f3;
        border-top: 5px solid #3498db;
        border-radius: 50%;
        animation: spin 1s linear infinite;
        margin: 0 auto 15px;
    }

    @keyframes spin {
        0% { transform: rotate(0deg); }
        100% { transform: rotate(360deg); }
    }

    .loading-text {
        color: #333;
        font-size: 16px;
        margin-top: 10px;
    }

    .loading-progress {
        width: 200px;
        height: 4px;
        background: #f3f3f3;
        border-radius: 2px;
        margin: 10px auto;
        overflow: hidden;
    }

    .loading-progress-bar {
        width: 0%;
        height: 100%;
        background: #3498db;
        animation: progress 2s ease-in-out infinite;
    }

    @keyframes progress {
        0% { width: 0%; }
        50% { width: 70%; }
        100% { width: 100%; }
    }
</style>
{% endblock %}

{% block content %}
<div class="app-container">
    <h1 class="page-title">SN隔离udtOnHoldDevice</h1>

    <form id="onHoldDeviceForm">
        {% csrf_token %}
        <div class="form-section">
            <div class="form-layout">
                <!-- 左侧区域 -->
                <div class="form-column">
                    <div class="form-group">
                        <label for="serial_numbers">Serial Numbers</label>
                        <div class="input-area">
                            <textarea id="serial_numbers" name="serial_numbers" 
                                    placeholder="Enter serial numbers here..."
                                    class="full-height-textarea"></textarea>
                            <small class="form-text">Enter one serial number per line</small>
                        </div>
                    </div>
                </div>

                <!-- 右侧区域 -->
                <div class="form-column">
                    <!-- 输入区域 -->
                    <div class="input-section">
                        <div class="form-group">
                            <label for="remark">Remark</label>
                            <div class="input-area">
                                <input type="text" id="remark" name="remark" 
                                       placeholder="Enter remark...">
                            </div>
                        </div>

                        <div class="form-group">
                            <label for="retcode">Retcode</label>
                            <div class="input-area">
                                <input type="text" id="retcode" name="retcode" 
                                       placeholder="Enter retcode...">
                            </div>
                        </div>
                    </div>

                    <!-- 选项卡片 -->
                    <div class="options-section">
                        <h5 class="section-title">Options</h5>
                        <div class="checkbox-grid">
                            <div class="custom-checkbox">
                                <input type="checkbox" id="Status" name="Status">
                                <label for="Status">Status</label>
                            </div>
                            <div class="custom-checkbox">
                                <input type="checkbox" id="isEngravingAssy" name="isEngravingAssy">
                                <label for="isEngravingAssy">Is Engraving Assy</label>
                            </div>
                            <div class="custom-checkbox">
                                <input type="checkbox" id="cartonID" name="cartonID">
                                <label for="cartonID">IsFirstPackStation</label>
                            </div>
                            <div class="custom-checkbox">
                                <input type="checkbox" id="isHoldCarton" name="isHoldCarton">
                                <label for="isHoldCarton">Is Hold Carton</label>
                            </div>
                            <div class="custom-checkbox">
                                <input type="checkbox" id="isHoldQAapproved" name="isHoldQAapproved">
                                <label for="isHoldQAapproved">Is Hold QA Approved</label>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="action-buttons">
            <button type="button" class="btn btn-primary" id="queryBtn">Query</button>
            <button type="button" class="btn btn-secondary" id="insertBtn">Insert</button>
            <button type="button" class="btn btn-warning" id="updateBtn">Update</button>
        </div>
    </form>

    <div id="queryResults" class="results-section"></div>
</div>

{% endblock %}

{% block extra_js %}
<script src="{% static 'js/plugins/layer/layer.js' %}"></script>
<script>
// 确保 layer 已加载
if (typeof layer === 'undefined') {
    console.error('Layer.js not loaded properly');
}

function getFormData() {
    return {
        serial_numbers: document.getElementById('serial_numbers').value,
        remark: document.getElementById('remark').value,
        retcode: document.getElementById('retcode').value,
        Status: document.getElementById('Status').checked,
        isEngravingAssy: document.getElementById('isEngravingAssy').checked,
        isHoldCarton: document.getElementById('isHoldCarton').checked,
        isHoldQAapproved: document.getElementById('isHoldQAapproved').checked,
        cartonID: document.getElementById('cartonID').checked
    };
}

// 验证表单数据
function validateFormData(formData, action) {
    if (!formData.serial_numbers.trim()) {
        showMessage('请输入Serial Numbers', 'warning');
        return false;
    }

    if (action === 'update') {
        if (!formData.retcode.trim()) {
            showMessage('更新操作需要输入Retcode', 'warning');
            return false;
        }
    }

    return true;
}

// 修改 showLoading 函数
function showLoading(message = '正在处理数据，请稍候...') {
    const overlay = document.createElement('div');
    overlay.className = 'loading-overlay';
    overlay.innerHTML = `
        <div class="loading-content">
            <div class="loading-spinner"></div>
            <div class="loading-text">${message}</div>
            <div class="loading-progress">
                <div class="loading-progress-bar"></div>
            </div>
        </div>
    `;
    document.body.appendChild(overlay);
    return overlay;
}

// 修改 hideLoading 函数
function hideLoading() {
    const overlays = document.querySelectorAll('.loading-overlay');
    overlays.forEach(overlay => overlay.remove());
}

// 修改 showMessage 函数
function showMessage(message, type = 'info') {
    if (typeof layer === 'undefined') {
        // 使用 Bootstrap alert 作为备用
        const alertDiv = document.createElement('div');
        alertDiv.className = `alert alert-${type} alert-dismissible fade show`;
        alertDiv.style.position = 'fixed';
        alertDiv.style.top = '20px';
        alertDiv.style.left = '50%';
        alertDiv.style.transform = 'translateX(-50%)';
        alertDiv.style.zIndex = '9999';
        
        alertDiv.innerHTML = `
            ${message}
            <button type="button" class="close" data-dismiss="alert" aria-label="Close">
                <span aria-hidden="true">&times;</span>
            </button>
        `;
        
        document.body.appendChild(alertDiv);
        setTimeout(() => alertDiv.remove(), 3000);
        return;
    }

    let icon;
    switch(type) {
        case 'success': icon = 1; break;
        case 'warning': icon = 0; break;
        case 'danger':
        case 'error': icon = 2; break;
        default: icon = -1;
    }
    
    layer.msg(message, {
        icon: icon,
        time: 3000,
        shade: 0.3
    });
}

// 修改 performQuery 函数
async function performQuery() {
    var formData = getFormData();
    if (!validateFormData(formData, 'query')) return;

    const loadingOverlay = showLoading('正在查询数据...');
    
    try {
        const response = await fetch('{% url "volcano_tools:query_on_hold_devices" %}', {
            method: 'POST',
            body: JSON.stringify(formData),
            headers: {
                'Content-Type': 'application/json',
                'X-CSRFToken': getCookie('csrftoken')
            }
        });

        const result = await response.json();
        
        if (result.error) {
            showMessage(result.error, 'danger');
        } else {
            displayResults(result);
            if (result.data && result.data.length > 0) {
                showMessage(`成功查询到 ${result.data.length} 条记录`, 'success');
            }
        }
    } catch (error) {
        showMessage('查询出错: ' + error.message, 'danger');
    } finally {
        hideLoading();
    }
}

// 修改 Insert 按钮事件处理
document.getElementById('insertBtn').addEventListener('click', async function() {
    var formData = getFormData();
    if (!validateFormData(formData, 'insert')) return;

    const loadingOverlay = showLoading('正在插入数据...');
    
    try {
        const response = await fetch('{% url "volcano_tools:insert_on_hold_devices" %}', {
            method: 'POST',
            body: JSON.stringify(formData),
            headers: {
                'Content-Type': 'application/json',
                'X-CSRFToken': getCookie('csrftoken')
            }
        });

        const result = await response.json();
        
        if (result.error) {
            showMessage(result.error, 'danger');
        } else {
            if (result.processed_count === 0) {
                showMessage('没有需要处理的新记录', 'info');
            } else {
                showMessage(`成功处理 ${result.processed_count} 条记录，用时 ${result.execution_time.toFixed(2)} 秒`, 'success');
                if (result.query_result) {
                    displayResults({data: result.query_result});
                }
            }
        }
    } catch (error) {
        showMessage('处理出错: ' + error.message, 'danger');
    } finally {
        hideLoading();
    }
});

// 修改 Update 按钮事件处理
document.getElementById('updateBtn').addEventListener('click', async function() {
    var formData = getFormData();
    if (!validateFormData(formData, 'update')) return;

    const loadingOverlay = showLoading('正在更新数据...');
    
    try {
        const response = await fetch('{% url "volcano_tools:update_on_hold_devices" %}', {
            method: 'POST',
            body: JSON.stringify(formData),
            headers: {
                'Content-Type': 'application/json',
                'X-CSRFToken': getCookie('csrftoken')
            }
        });

        const result = await response.json();

        if (!response.ok) {
            showMessage(result.error || '更新失败，请检查输入', 'warning');
        } else {
            showMessage(`${result.message} 处理完成`, 'success');
            if (result.query_result) {
                displayResults({data: result.query_result});
            }
        }
    } catch (error) {
        showMessage('更新出错: ' + error.message, 'danger');
    } finally {
        hideLoading();
    }
});

// Query按钮事件处理
document.getElementById('queryBtn').addEventListener('click', performQuery);

// 获取 CSRF token 的函数
function getCookie(name) {
    let cookieValue = null;
    if (document.cookie && document.cookie !== '') {
        const cookies = document.cookie.split(';');
        for (let i = 0; i < cookies.length; i++) {
            const cookie = cookies[i].trim();
            if (cookie.substring(0, name.length + 1) === (name + '=')) {
                cookieValue = decodeURIComponent(cookie.substring(name.length + 1));
                break;
            }
        }
    }
    return cookieValue;
}

// 添加displayResults函数
function displayResults(result) {
    var queryResults = document.getElementById('queryResults');
    if (result.error) {
        queryResults.innerHTML = '<div class="alert alert-danger">' + result.error + '</div>';
        return;
    }

    if (!result.data || result.data.length === 0) {
        queryResults.innerHTML = '<div class="alert alert-info">没有找到匹配的记录</div>';
        return;
    }

    var html = '<div class="results-table"><table class="table">';
    html += '<thead><tr>';
    html += '<th>Serial Number</th>';
    html += '<th>Last Update</th>';
    html += '<th>Remark</th>';
    html += '<th>Status</th>';
    html += '<th>Is Engraving Assy</th>';
    html += '<th>Is Hold Carton</th>';
    html += '<th>Is Hold QA Approved</th>';
    html += '<th>Retcode</th>';
    html += '<th>IsFirstPackStation</th>';
    html += '</tr></thead>';
    html += '<tbody>';

    result.data.forEach(function(item) {
        html += '<tr>';
        html += '<td>' + (item.serial_number || '') + '</td>';
        html += '<td>' + (item.lastupdate || '') + '</td>';
        html += '<td>' + (item.remark || '') + '</td>';
        html += '<td>' + (item.Status ? 'Yes' : 'No') + '</td>';
        html += '<td>' + (item.isEngravingAssy ? 'Yes' : 'No') + '</td>';
        html += '<td>' + (item.isHoldCarton ? 'Yes' : 'No') + '</td>';
        html += '<td>' + (item.isHoldQAapproved ? 'Yes' : 'No') + '</td>';
        html += '<td>' + (item.Retcode || '') + '</td>';
        html += '<td>' + (item.cartonID ? 'Yes' : 'No') + '</td>';
        html += '</tr>';
    });

    html += '</tbody></table></div>';
    queryResults.innerHTML = html;
}
</script>
{% endblock %}
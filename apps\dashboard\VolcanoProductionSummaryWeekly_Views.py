import pandas as pd
import numpy as np
from django.db import connections
from django.shortcuts import render

def get_weekly_packing_production_summary_chart(request):
    Y_AXIS_MAX = 15000  # 默认值，如果没有获取到数据时使用
    error_message = None

    # 1. Packing SP: 合并 Hermes/Alpha 所有数据 - 使用周报表存储过程
    packing_raw_data = []
    try:
        with connections['VolcanoReporDB'].cursor() as cursor:
            cursor.execute("EXEC usp_GetWeeklyPackingLineProductionSummary_rw")
            columns = [col[0] for col in cursor.description]
            for row in cursor.fetchall():
                packing_raw_data.append(dict(zip(columns, row)))
    except Exception as e:
        error_message = f'Packing SP 数据库错误：{str(e)}'

    packing_context = {
        'categories': [],
        'planned_data': [],
        'actual_data': [],
        'gap_abs_data': [],
        'unmet_target_data': [],
        'gap_sign_data': [],
        'y_axis_max': Y_AXIS_MAX,
        'page_title': 'Packing Plan VS Actual By Line',
        'data_exists': False
    }
    if packing_raw_data and not error_message:
        df = pd.DataFrame(packing_raw_data)
        
        # 根据图片中的数据列调整
        # Type, Description, WeekStartDate, PlannedQuantity, Production
        desc_col = 'Description'  # 描述列
        plan_col = 'PlannedQuantity'  # 计划数量列
        actual_col = 'Production'  # 实际生产列
        
        if desc_col in df.columns and plan_col in df.columns and actual_col in df.columns:
            df[plan_col] = pd.to_numeric(df[plan_col], errors='coerce').fillna(0)
            df[actual_col] = pd.to_numeric(df[actual_col], errors='coerce').fillna(0)
            
            summary_df = df.groupby(desc_col, as_index=False).agg(
                PlannedQuantity_Total=(plan_col, 'sum'),
                Actual_Production=(actual_col, 'sum')
            )
            
            summary_df['Gap_Value'] = summary_df['Actual_Production'] - summary_df['PlannedQuantity_Total']
            summary_df['Absolute_Gap'] = summary_df['Gap_Value'].abs()
            
            # 计算未完成计划量
            summary_df['Unmet_Target'] = summary_df.apply(
                lambda row: max(0, row['PlannedQuantity_Total'] - row['Actual_Production']), axis=1
            )
            
            summary_df['Gap_Sign'] = summary_df['Gap_Value'].apply(lambda x: 'positive' if x >= 0 else 'negative')
            
            packing_context['categories'] = summary_df[desc_col].tolist()
            packing_context['planned_data'] = [int(x) for x in summary_df['PlannedQuantity_Total'].tolist()]
            packing_context['actual_data'] = [int(x) for x in summary_df['Actual_Production'].tolist()]
            packing_context['gap_abs_data'] = [int(x) for x in summary_df['Absolute_Gap'].tolist()]
            packing_context['unmet_target_data'] = [int(x) for x in summary_df['Unmet_Target'].tolist()]
            packing_context['gap_sign_data'] = summary_df['Gap_Sign'].tolist()
            
            # 计算Y轴最大值
            max_value = max(
                summary_df['PlannedQuantity_Total'].max() if not summary_df.empty else 0,
                summary_df['Actual_Production'].max() if not summary_df.empty else 0
            )
            packing_context['y_axis_max'] = int(max_value * 1.2) if max_value > 0 else Y_AXIS_MAX
            packing_context['data_exists'] = True

    # 2. BB SP: 三类数据 - 使用周报表存储过程
    bb_raw_data = []
    try:
        with connections['VolcanoReporDB'].cursor() as cursor:
            cursor.execute("EXEC usp_GetWeeklyBBLineProductionSummary_rw")
            columns_bb = [col[0] for col in cursor.description]
            for row in cursor.fetchall():
                bb_raw_data.append(dict(zip(columns_bb, row)))
    except Exception as e:
        error_message = f'BB SP 数据库错误：{str(e)}'

    bb_types = ['Hermes Charger', 'Hermes Holder', 'Alpha BB']
    bb_contexts = {}
    for bb_type in bb_types:
        context = {
            'categories': [],
            'planned_data': [],
            'actual_data': [],
            'gap_abs_data': [],
            'unmet_target_data': [],
            'gap_sign_data': [],
            'y_axis_max': Y_AXIS_MAX,
            'page_title': bb_type + ' Plan VS Actual By Line',
            'data_exists': False
        }
        if bb_raw_data and not error_message:
            df_bb = pd.DataFrame(bb_raw_data)
            
            # 根据图片中的数据列调整
            # Type, Description, WeekStartDate, PlannedQuantity, Production
            type_col = 'Type'  # 类型列
            desc_col = 'Description'  # 描述列
            plan_col = 'PlannedQuantity'  # 计划数量列
            actual_col = 'Production'  # 实际生产列
            
            if all(col in df_bb.columns for col in [type_col, desc_col, plan_col, actual_col]):
                df_bb[plan_col] = pd.to_numeric(df_bb[plan_col], errors='coerce').fillna(0)
                df_bb[actual_col] = pd.to_numeric(df_bb[actual_col], errors='coerce').fillna(0)
                
                df_filtered = df_bb[df_bb[type_col] == bb_type]
                if not df_filtered.empty:
                    summary_df = df_filtered.groupby(desc_col, as_index=False).agg(
                        PlannedQuantity_Total=(plan_col, 'sum'),
                        Actual_Production=(actual_col, 'sum')
                    )
                    
                    summary_df['Gap_Value'] = summary_df['Actual_Production'] - summary_df['PlannedQuantity_Total']
                    summary_df['Absolute_Gap'] = summary_df['Gap_Value'].abs()
                    
                    # 计算未完成计划量
                    summary_df['Unmet_Target'] = summary_df.apply(
                        lambda row: max(0, row['PlannedQuantity_Total'] - row['Actual_Production']), axis=1
                    )
                    
                    summary_df['Gap_Sign'] = summary_df['Gap_Value'].apply(lambda x: 'positive' if x >= 0 else 'negative')
                    
                    context['categories'] = summary_df[desc_col].tolist()
                    context['planned_data'] = [int(x) for x in summary_df['PlannedQuantity_Total'].tolist()]
                    context['actual_data'] = [int(x) for x in summary_df['Actual_Production'].tolist()]
                    context['gap_abs_data'] = [int(x) for x in summary_df['Absolute_Gap'].tolist()]
                    context['unmet_target_data'] = [int(x) for x in summary_df['Unmet_Target'].tolist()]
                    context['gap_sign_data'] = summary_df['Gap_Sign'].tolist()
                    
                    # 计算Y轴最大值
                    max_value = max(
                        summary_df['PlannedQuantity_Total'].max() if not summary_df.empty else 0,
                        summary_df['Actual_Production'].max() if not summary_df.empty else 0
                    )
                    context['y_axis_max'] = int(max_value * 1.2) if max_value > 0 else Y_AXIS_MAX
                    context['data_exists'] = True
        bb_contexts[bb_type] = context

    final_context = {
        'error_message': error_message,
        'packing_chart': packing_context,
        'bb_charger_chart': bb_contexts['Hermes Charger'],
        'bb_holder_chart': bb_contexts['Hermes Holder'],
        'bb_alpha_chart': bb_contexts['Alpha BB'],
    }
    return render(request, 'dashboard/volcano_production_summary_weekly_chart.html', final_context)
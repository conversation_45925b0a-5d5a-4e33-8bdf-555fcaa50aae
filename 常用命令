
django-admin startproject myproject

cd

python manage.py startapp fftest_app


python manage.py makemigrations

admin


4K
3840*2160

python manage.py runserver --settings=myproject.settings_dev


python manage.py runserver
python manage.py createsuperuser

python manage.py makemigrations
python manage.py migrate --database=FFTestDB

python manage.py runserver 0.0.0.0:8000

新建目录volcan_tools
python manage.py startapp volcan_tools C:\Rwindest\PY\Mycode\myproject\apps\volcan_tools


python manage.py runserver 0.0.0.0:7000

python manage.py collectstatic --noinput
python manage.py collectstatic



python manage.py makemigrations dashboard
python manage.py migrate --fake-initial dashboard

python manage.py migrate volcano_report

 python manage.py runserver --settings=myproject.settings_dev

 python manage.py runserver 0.0.0.0:8000 --settings=myproject.settings_dev



git config --global user.name "<PERSON><PERSON><PERSON> Xiao"
git config --global user.email "<EMAIL>"
创建一个新仓库
git clone http://10.200.40.77:8080/jan222/djangoproject_simpleui.git
cd djangoproject_simpleui
git switch --create main
touch README.md
git add README.md
git commit -m "add README"
git push --set-upstream origin main
推送现有文件夹
cd existing_folder
git init --initial-branch=main
git remote add origin http://10.200.40.77:8080/jan222/djangoproject_simpleui.git

git status
git add .
git commit -m "增加ffunit查询"
git push --set-upstream origin master


推送现有的 Git 仓库
cd existing_repo
git remote rename origin old-origin
git remote add origin http://10.200.40.77:8080/jan222/djangoproject_simpleui.git
git push --set-upstream origin --all
git push --set-upstream origin --tags


git push --set-upstream origin master




开发环境推送
git push origin master



如果您的本地 db.sqlite3 更改不重要：
您可以放弃本地更改，使用远程版本：

git checkout -- db.sqlite3
git pull origin master
如果您的本地 db.sqlite3 更改很重要：
您可以先暂存（stash）您的更改：

git stash
git pull origin master
git stash pop




----------------

python manage.py migrate mtghpreport --plan


python manage.py migrate mtghpreport

{% extends 'base1.html' %}
{% load static %}

{% block content %}
<div class="app-container">
    <h1 class="page-title">
        <i class="bi bi-database"></i> Volcano Query Data
    </h1>

    {% if conditions %}
        <div class="query-grid">
            {% for condition in conditions %}
                <div class="query-card">
                    <div class="query-card-content">
                        <div class="query-header">
                            <span class="query-badge">{{ condition.ID }}</span>
                            <h3 class="query-title">{{ condition.Reason }}</h3>
                        </div>
                        
                        <div class="query-details">
                            <div class="query-info">
                                <span class="info-label">Use Count:</span>
                                <span class="info-value">{{ condition.useCount }}</span>
                            </div>
                            
                            <p class="query-text udp" onclick="toggleText(this)">{{ condition.UDP }}</p>
                            <p class="query-text remark">{{ condition.Remark }}</p>
                        </div>

                        <a href="{% url 'volcanoreport:query_page' condition.ID %}" 
                           class="query-button">
                            Open Query
                        </a>
                    </div>
                </div>
            {% endfor %}
        </div>
    {% else %}
        <div class="loading-container">
            <div class="loading-spinner"></div>
            <span class="loading-text">Loading data...</span>
        </div>
    {% endif %}
</div>

<style>
/* 查询卡片网格布局 */
.query-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
    gap: 1.5rem;
    padding: 1rem;
}

/* 查询卡片样式 */
.query-card {
    background: #ffffff;
    border-radius: 16px;
    overflow: hidden;
    transition: all 0.3s ease;
    box-shadow: 0 2px 10px rgba(0,0,0,0.05);
}

.query-card:hover {
    transform: translateY(-4px);
    box-shadow: 0 8px 24px rgba(0,0,0,0.1);
}

.query-card-content {
    padding: 1.5rem;
    display: flex;
    flex-direction: column;
    height: 100%;
}

/* 查询卡片头部 */
.query-header {
    display: flex;
    align-items: flex-start;
    margin-bottom: 1rem;
}

.query-badge {
    background: #0066cc;
    color: white;
    padding: 0.25rem 0.75rem;
    border-radius: 20px;
    font-size: 0.875rem;
    margin-right: 0.75rem;
    flex-shrink: 0;
}

.query-title {
    font-size: 1.125rem;
    font-weight: 500;
    color: #1d1d1f;
    margin: 0;
    flex-grow: 1;
    white-space: normal;
    word-wrap: break-word;
    line-height: 1.4;
}

/* 查询详情 */
.query-details {
    flex-grow: 1;
    margin: 1rem 0;
}

.query-info {
    display: flex;
    align-items: center;
    margin-bottom: 0.75rem;
}

.info-label {
    color: #86868b;
    font-size: 0.875rem;
    margin-right: 0.5rem;
    flex-shrink: 0;
}

.info-value {
    color: #1d1d1f;
    font-weight: 500;
}

/* UDP文本使用省略号 */
.query-text.udp {
    color: #1d1d1f;
    font-size: 0.9375rem;
    margin: 0.5rem 0;
    line-height: 1.5;
    overflow: hidden;
    text-overflow: ellipsis;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    cursor: pointer;
}

/* Remark文本允许换行 */
.query-text.remark {
    color: #1d1d1f;
    font-size: 0.9375rem;
    margin: 0.5rem 0;
    line-height: 1.5;
    word-wrap: break-word;
    white-space: normal;
}

/* 添加展开状态的样式 */
.query-text.udp.expanded {
    -webkit-line-clamp: unset;
    max-height: none;
}

/* 查询按钮 */
.query-button {
    display: inline-block;
    background: #0066cc;
    color: white;
    padding: 0.75rem 1.5rem;
    border-radius: 20px;
    text-decoration: none;
    text-align: center;
    font-weight: 500;
    transition: all 0.2s ease;
    margin-top: auto;
}

.query-button:hover {
    background: #0056b3;
    color: white;
    text-decoration: none;
    transform: translateY(-1px);
}

/* 加载动画 */
.loading-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 3rem;
}

.loading-spinner {
    width: 40px;
    height: 40px;
    border: 3px solid #f5f5f7;
    border-top: 3px solid #0066cc;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin-bottom: 1rem;
}

.loading-text {
    color: #86868b;
    font-size: 0.9375rem;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* 响应式调整 */
@media (max-width: 768px) {
    .query-grid {
        grid-template-columns: 1fr;
    }

    .query-card-content {
        padding: 1.25rem;
    }
}
</style>

<script>
function toggleText(element) {
    element.classList.toggle('expanded');
}
</script>
{% endblock %}

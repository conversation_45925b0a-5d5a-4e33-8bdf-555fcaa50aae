{% load static %}
<!DOCTYPE html>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Packing Production Dashboard</title>
    <!-- ECharts -->
    <!-- <script src="https://cdnjs.cloudflare.com/ajax/libs/echarts/5.4.3/echarts.min.js"></script> -->
    <script src="{% static 'js/echarts.min.js' %}"></script>
    <!-- Font Awesome (Local) -->
    <link rel="stylesheet" href="{% static 'dashboard/css/all.min.css' %}">
    <style>
        :root {
            --bg-primary: #0d1117;
            --bg-secondary: #161b22;
            --bg-card: #21262d;
            --text-primary: #f0f6fc;
            --text-secondary: #c9d1d9;
            --text-muted: #8b949e;
            --border-color: #30363d;
            --green-primary: #39d353;
            --green-secondary: #2ea043;
            --green-dark: #238636;
            --red-primary: #f85149;
            --red-secondary: #da3633;
            --blue-primary: #58a6ff;
            --blue-secondary: #388bfd;
            --yellow-primary: #e3b341;
            --gray-light: rgba(201, 209, 217, 0.3);
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Helvetica, Arial, sans-serif;
            background-color: var(--bg-primary);
            color: var(--text-primary);
            line-height: 1.6;
            padding: 0;
            margin: 0;
        }

        .top-bar {
            background-color: var(--bg-secondary);
            padding: 1rem 2rem;
            border-bottom: 1px solid var(--border-color);
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .top-bar-title {
            font-size: 1.25rem;
            font-weight: 600;
            color: var(--text-primary);
        }

        .date-display {
            color: var(--text-secondary);
            font-size: 0.9rem;
        }

        .container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 2rem;
        }

        .dashboard-header {
            margin-bottom: 2rem;
            text-align: center;
        }

        .dashboard-title {
            font-size: 2rem;
            font-weight: 600;
            margin-bottom: 0.5rem;
            color: var(--text-primary);
            letter-spacing: -0.5px;
        }

        .dashboard-subtitle {
            color: var(--text-secondary);
            font-size: 1rem;
        }

        .card {
            background-color: var(--bg-card);
            border-radius: 12px;
            border: 1px solid var(--border-color);
            overflow: hidden;
            margin-bottom: 2rem;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            transition: transform 0.2s, box-shadow 0.2s;
        }

        .card:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 12px rgba(0, 0, 0, 0.15);
        }

        .card-header {
            padding: 1.25rem 1.5rem;
            border-bottom: 1px solid var(--border-color);
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .card-title {
            font-size: 1.25rem;
            font-weight: 600;
            color: var(--text-primary);
        }

        .card-body {
            padding: 1rem;
        }

        .chart-container {
            width: 100%;
            height: 500px;
        }

        .progress-cards {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
            gap: 1.5rem;
            margin-bottom: 2rem;
        }

        .progress-card {
            background-color: var(--bg-card);
            border-radius: 8px;
            border: 1px solid var(--border-color);
            padding: 1.25rem;
            transition: transform 0.2s;
        }

        .progress-card:hover {
            transform: translateY(-2px);
        }

        .progress-card-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 1rem;
        }

        .progress-card-title {
            font-size: 1rem;
            font-weight: 600;
            color: var(--text-primary);
        }

        .progress-card-icon {
            font-size: 1.25rem;
            color: var(--text-secondary);
            width: 32px;
            height: 32px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 50%;
            background-color: rgba(88, 166, 255, 0.1);
        }

        .progress-stats {
            display: flex;
            justify-content: space-between;
            margin-bottom: 0.75rem;
        }

        .progress-value {
            font-size: 1.75rem;
            font-weight: 600;
            color: var(--text-primary);
        }

        .progress-label {
            font-size: 0.875rem;
            color: var(--text-secondary);
        }

        .progress-percentage {
            font-size: 1rem;
            font-weight: 500;
        }

        .progress-percentage.positive {
            color: var(--green-primary);
        }

        .progress-percentage.negative {
            color: var(--red-primary);
        }

        .progress-bar-container {
            width: 100%;
            height: 8px;
            background-color: rgba(201, 209, 217, 0.1);
            border-radius: 4px;
            overflow: hidden;
        }

        .progress-bar {
            height: 100%;
            border-radius: 4px;
            transition: width 1s ease-in-out;
        }

        .progress-bar.success {
            background: linear-gradient(90deg, var(--green-dark) 0%, var(--green-primary) 100%);
        }

        .progress-bar.warning {
            background: linear-gradient(90deg, var(--yellow-primary) 0%, #ffd33d 100%);
        }

        .progress-bar.danger {
            background: linear-gradient(90deg, var(--red-secondary) 0%, var(--red-primary) 100%);
        }

        .legend {
            display: flex;
            justify-content: center;
            gap: 1.5rem;
            margin-bottom: 1rem;
        }

        .legend-item {
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .legend-color {
            width: 16px;
            height: 16px;
            border-radius: 4px;
        }

        .legend-color.actual {
            background: linear-gradient(180deg, #34D399 0%, #10B981 100%);
        }

        .legend-color.gap {
            background: linear-gradient(180deg, #60A5FA 0%, #3B82F6 100%);
        }

        .legend-color.gap-neg {
            background: linear-gradient(180deg, #F87171 0%, #EF4444 100%);
        }

        .legend-color.unmet {
            background-color: rgba(229, 231, 235, 0.6);
        }

        .legend-text {
            font-size: 0.875rem;
            color: var(--text-secondary);
        }

        .no-data {
            text-align: center;
            padding: 3rem;
            color: var(--text-muted);
            font-style: italic;
            background-color: rgba(0, 0, 0, 0.1);
            border-radius: 8px;
        }

        .error-message {
            color: var(--red-primary);
            background-color: rgba(248, 81, 73, 0.1);
            border: 1px solid var(--red-secondary);
            border-radius: 8px;
            padding: 1rem;
            margin-bottom: 1.5rem;
            text-align: center;
        }

        footer {
            text-align: center;
            padding: 1.5rem;
            background-color: var(--bg-secondary);
            color: var(--text-muted);
            font-size: 0.875rem;
            border-top: 1px solid var(--border-color);
            margin-top: 2rem;
        }

        /* Animation for loading charts */
        @keyframes pulse {
            0% { opacity: 0.6; }
            50% { opacity: 0.8; }
            100% { opacity: 0.6; }
        }

        .loading {
            animation: pulse 1.5s infinite;
            background-color: var(--bg-secondary);
            height: 100%;
            border-radius: 8px;
        }

        /* Responsive adjustments */
        @media (max-width: 768px) {
            .container {
                padding: 1rem;
            }
            
            .chart-container {
                height: 400px;
            }
            
            .progress-cards {
                grid-template-columns: 1fr;
            }
            
            .dashboard-title {
                font-size: 1.75rem;
            }
        }
    </style>
</head>
<body>
    <div class="top-bar">
        <div class="top-bar-title">Production Analytics</div>
        <div class="date-display" id="currentDate">Loading date...</div>
    </div>

    <div class="container">
        <div class="dashboard-header">
            <h1 class="dashboard-title">Packing Plan VS Actual By Line</h1>
            <p class="dashboard-subtitle">Real-time production monitoring and comparison</p>
        </div>

        <!-- Summary cards -->
        <div class="progress-cards">
            <!-- Hermes Summary Card -->
            <div class="progress-card" id="hermesSummary">
                <div class="progress-card-header">
                    <h3 class="progress-card-title">Hermes Packing</h3>
                    <div class="progress-card-icon">
                        <i class="fas fa-box"></i>
                    </div>
                </div>
                <div class="progress-stats">
                    <div>
                        <div class="progress-value" id="hermesActual">0</div>
                        <div class="progress-label">Actual Production</div>
                    </div>
                    <div>
                        <div class="progress-percentage" id="hermesPercentage">0%</div>
                        <div class="progress-label">of Target</div>
                    </div>
                </div>
                <div class="progress-bar-container">
                    <div class="progress-bar success" id="hermesProgressBar" style="width: 0%"></div>
                </div>
            </div>

            <!-- Alpha Summary Card -->
            <div class="progress-card" id="alphaSummary">
                <div class="progress-card-header">
                    <h3 class="progress-card-title">Alpha Packing</h3>
                    <div class="progress-card-icon">
                        <i class="fas fa-boxes-stacked"></i>
                    </div>
                </div>
                <div class="progress-stats">
                    <div>
                        <div class="progress-value" id="alphaActual">0</div>
                        <div class="progress-label">Actual Production</div>
                    </div>
                    <div>
                        <div class="progress-percentage" id="alphaPercentage">0%</div>
                        <div class="progress-label">of Target</div>
                    </div>
                </div>
                <div class="progress-bar-container">
                    <div class="progress-bar danger" id="alphaProgressBar" style="width: 0%"></div>
                </div>
            </div>

            <!-- Overall Summary Card -->
            <div class="progress-card">
                <div class="progress-card-header">
                    <h3 class="progress-card-title">Total Production</h3>
                    <div class="progress-card-icon">
                        <i class="fas fa-chart-line"></i>
                    </div>
                </div>
                <div class="progress-stats">
                    <div>
                        <div class="progress-value" id="totalActual">0</div>
                        <div class="progress-label">Total Units</div>
                    </div>
                    <div>
                        <div class="progress-percentage" id="totalPercentage">0%</div>
                        <div class="progress-label">Overall Completion</div>
                    </div>
                </div>
                <div class="progress-bar-container">
                    <div class="progress-bar warning" id="totalProgressBar" style="width: 0%"></div>
                </div>
            </div>
        </div>

        <!-- Hermes Packing Chart -->
        {% if hermes_packing_categories %}
            <div class="card">
                <div class="card-header">
                    <h2 class="card-title">{{ hermes_packing_page_title|default:"Hermes Packing Production Data" }}</h2>
                </div>
                <div class="card-body">
                    <div class="legend">
                        <div class="legend-item"><div class="legend-color actual"></div><span class="legend-text">Actual Production</span></div>
                        <div class="legend-item"><div class="legend-color gap"></div><span class="legend-text">Positive Gap</span></div>
                        <div class="legend-item"><div class="legend-color gap-neg"></div><span class="legend-text">Negative Gap</span></div>
                        <div class="legend-item"><div class="legend-color unmet"></div><span class="legend-text">Unmet Target</span></div>
                    </div>
                    <div id="chartHermesPacking" class="chart-container"></div>
                    {# Django data for Hermes Packing #}
                    {{ hermes_packing_categories|json_script:"hermes_categories_data" }}
                    {{ hermes_packing_planned_data|json_script:"hermes_planned_data" }}
                    {{ hermes_packing_actual_data|json_script:"hermes_actual_data" }}
                    {{ hermes_packing_gap_abs_data|json_script:"hermes_gap_abs_data" }}
                    {{ hermes_packing_unmet_data|json_script:"hermes_unmet_data" }}
                    {{ hermes_packing_gap_sign_data|json_script:"hermes_gap_sign_data" }}
                    {{ hermes_packing_y_axis_max|json_script:"hermes_y_axis_max_data" }}
                </div>
            </div>
        {% else %}
            <div class="card">
                <div class="card-header"><h2 class="card-title">Hermes Packing Production Data</h2></div>
                <div class="card-body"><div class="no-data">No data available for Hermes Packing.</div></div>
            </div>
        {% endif %}

        <!-- Alpha Packing Chart -->
        {% if alpha_packing_categories %}
            <div class="card">
                <div class="card-header">
                    <h2 class="card-title">{{ alpha_packing_page_title|default:"Alpha Packing Production Data" }}</h2>
                </div>
                <div class="card-body">
                     <div class="legend">
                        <div class="legend-item"><div class="legend-color actual"></div><span class="legend-text">Actual Production</span></div>
                        <div class="legend-item"><div class="legend-color gap"></div><span class="legend-text">Positive Gap</span></div>
                        <div class="legend-item"><div class="legend-color gap-neg"></div><span class="legend-text">Negative Gap</span></div>
                        <div class="legend-item"><div class="legend-color unmet"></div><span class="legend-text">Unmet Target</span></div>
                    </div>
                    <div id="chartAlphaPacking" class="chart-container"></div>
                    {# Django data for Alpha Packing #}
                    {{ alpha_packing_categories|json_script:"alpha_categories_data" }}
                    {{ alpha_packing_planned_data|json_script:"alpha_planned_data" }}
                    {{ alpha_packing_actual_data|json_script:"alpha_actual_data" }}
                    {{ alpha_packing_gap_abs_data|json_script:"alpha_gap_abs_data" }}
                    {{ alpha_packing_unmet_data|json_script:"alpha_unmet_data" }}
                    {{ alpha_packing_gap_sign_data|json_script:"alpha_gap_sign_data" }}
                    {{ alpha_packing_y_axis_max|json_script:"alpha_y_axis_max_data" }}
                </div>
            </div>
        {% else %}
             <div class="card">
                <div class="card-header"><h2 class="card-title">Alpha Packing Production Data</h2></div>
                <div class="card-body"><div id="alphaPackingData" class="no-data">No data available for Alpha Packing.</div></div>
            </div>
        {% endif %}
    </div>

    <footer>
        <p>Production Dashboard © {% now "Y" %} | Updated in real-time</p>
    </footer>

    <script>
        // Removed mockData, data will be loaded from Django context

        // Set current date
        document.getElementById('currentDate').textContent = new Date().toLocaleDateString('en-US', {
            weekday: 'long', 
            year: 'numeric', 
            month: 'long', 
            day: 'numeric'
        });

        // Function to safely parse JSON from script tag
        function getJsonData(id) {
            const element = document.getElementById(id);
            if (element) {
                try {
                    return JSON.parse(element.textContent);
                } catch (e) {
                    console.error(`Error parsing JSON from ${id}:`, e);
                    return null;
                }
            }
            return null;
        }

        // Initialize dashboard
        function initDashboard() {
            // Load Hermes data
            const hermesCategories = getJsonData('hermes_categories_data');
            const hermesPlanned = getJsonData('hermes_planned_data');
            const hermesActual = getJsonData('hermes_actual_data');
            const hermesGapAbs = getJsonData('hermes_gap_abs_data');
            const hermesUnmet = getJsonData('hermes_unmet_data');
            const hermesGapSign = getJsonData('hermes_gap_sign_data');
            const hermesYAxisMax = getJsonData('hermes_y_axis_max_data') || 3000; // Default if not provided

            // Load Alpha data
            const alphaCategories = getJsonData('alpha_categories_data');
            const alphaPlanned = getJsonData('alpha_planned_data');
            const alphaActual = getJsonData('alpha_actual_data');
            const alphaGapAbs = getJsonData('alpha_gap_abs_data');
            const alphaUnmet = getJsonData('alpha_unmet_data');
            const alphaGapSign = getJsonData('alpha_gap_sign_data');
            const alphaYAxisMax = getJsonData('alpha_y_axis_max_data') || 3000; // Default if not provided

            // Update summary cards
            updateSummaryCards(hermesActual, hermesPlanned, alphaActual, alphaPlanned);
            
            // Render Hermes chart if data exists
            if (hermesCategories && hermesCategories.length > 0) {
                renderProductionChart(
                    'chartHermesPacking',
                    hermesCategories,
                    hermesPlanned,
                    hermesActual,
                    hermesGapAbs,
                    hermesUnmet,
                    hermesGapSign,
                    hermesYAxisMax
                );
            }
            
            // Render Alpha chart if data exists
            if (alphaCategories && alphaCategories.length > 0) {
                 // Ensure the div for Alpha chart exists and is a chart container
                const alphaChartDiv = document.getElementById('chartAlphaPacking');
                if (alphaChartDiv) { // Check if the chart div exists (it should if alpha_packing_categories is true)
                    renderProductionChart(
                        'chartAlphaPacking',
                        alphaCategories,
                        alphaPlanned,
                        alphaActual,
                        alphaGapAbs,
                        alphaUnmet,
                        alphaGapSign,
                        alphaYAxisMax
                    );
                }
            }
        }

        // Update summary cards with data from backend
        function updateSummaryCards(hermesActualData, hermesPlannedData, alphaActualData, alphaPlannedData) {
            const hermesActual = hermesActualData || [];
            const hermesPlanned = hermesPlannedData || [];
            const alphaActual = alphaActualData || [];
            const alphaPlanned = alphaPlannedData || [];

            // Calculate totals for Hermes
            const hermesTotal = hermesActual.reduce((sum, val) => sum + (Number(val) || 0), 0);
            const hermesPlanTotal = hermesPlanned.reduce((sum, val) => sum + (Number(val) || 0), 0);
            const hermesPercentage = hermesPlanTotal > 0 ? Math.round((hermesTotal / hermesPlanTotal) * 100) : 0;
            
            document.getElementById('hermesActual').textContent = hermesTotal.toLocaleString();
            document.getElementById('hermesPercentage').textContent = `${hermesPercentage}%`;
            document.getElementById('hermesPercentage').className = `progress-percentage ${hermesPercentage >= 90 ? 'positive' : 'negative'}`;
            document.getElementById('hermesProgressBar').style.width = `${hermesPercentage}%`;
            document.getElementById('hermesProgressBar').className = `progress-bar ${getProgressBarClass(hermesPercentage)}`;
            
            // Calculate totals for Alpha
            const alphaTotal = alphaActual.reduce((sum, val) => sum + (Number(val) || 0), 0);
            const alphaPlanTotal = alphaPlanned.reduce((sum, val) => sum + (Number(val) || 0), 0);
            const alphaPercentage = alphaPlanTotal > 0 ? Math.round((alphaTotal / alphaPlanTotal) * 100) : 0;

            if (document.getElementById('alphaActual')) { // Check if Alpha summary card elements exist
                document.getElementById('alphaActual').textContent = alphaTotal.toLocaleString();
                document.getElementById('alphaPercentage').textContent = `${alphaPercentage}%`;
                document.getElementById('alphaPercentage').className = `progress-percentage ${alphaPercentage >= 90 ? 'positive' : 'negative'}`;
                document.getElementById('alphaProgressBar').style.width = `${alphaPercentage}%`;
                document.getElementById('alphaProgressBar').className = `progress-bar ${getProgressBarClass(alphaPercentage)}`;
            }
            
            // Calculate and update total production
            const totalActualVal = hermesTotal + alphaTotal;
            const totalPlanVal = hermesPlanTotal + alphaPlanTotal;
            const totalPercentageVal = totalPlanVal > 0 ? Math.round((totalActualVal / totalPlanVal) * 100) : 0;
            
            document.getElementById('totalActual').textContent = totalActualVal.toLocaleString();
            document.getElementById('totalPercentage').textContent = `${totalPercentageVal}%`;
            document.getElementById('totalPercentage').className = `progress-percentage ${totalPercentageVal >= 90 ? 'positive' : 'negative'}`;
            document.getElementById('totalProgressBar').style.width = `${totalPercentageVal}%`;
            document.getElementById('totalProgressBar').className = `progress-bar ${getProgressBarClass(totalPercentageVal)}`;
        }
        
        // Get progress bar class based on percentage
        function getProgressBarClass(percentage) {
            if (percentage >= 90) return 'success';
            if (percentage >= 70) return 'warning';
            return 'danger';
        }

        // Render production chart - now accepts yAxisMax as a parameter
        function renderProductionChart(chartId, categories, plannedData, actualData, gapAbsData, unmetData, gapSignData, yAxisMaxValue) {
            const chartElement = document.getElementById(chartId);
            if (!chartElement) {
                console.error("Chart element not found:", chartId);
                return;
            }
            
            const myChart = echarts.init(chartElement, null, { renderer: 'svg' }); // Using dark theme from CSS vars
            
            // Define colors and gradients (can be moved outside if static or use CSS vars)
            const colors = {
                actual: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                    { offset: 0, color: 'var(--green-primary)' },
                    { offset: 1, color: 'var(--green-dark)' }
                ]),
                gap_pos: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                    { offset: 0, color: 'var(--blue-primary)' },
                    { offset: 1, color: 'var(--blue-secondary)' }
                ]),
                gap_neg: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                    { offset: 0, color: 'var(--red-primary)' },
                    { offset: 1, color: 'var(--red-secondary)' }
                ]),
                unmet: 'var(--gray-light)'
            };
            
            const gapSeriesData = (gapAbsData || []).map((value, index) => {
                return {
                    value: value,
                    itemStyle: {
                        color: (gapSignData || [])[index] === 'positive' ? colors.gap_pos : colors.gap_neg,
                        shadowBlur: 3,
                        shadowColor: 'rgba(0, 0, 0, 0.2)',
                        shadowOffsetX: 1,
                        shadowOffsetY: 1
                    }
                };
            });
            
            const option = {
                backgroundColor: 'transparent',
                tooltip: {
                    trigger: 'axis',
                    axisPointer: { type: 'shadow' },
                    formatter: function (params) {
                        const categoryIndex = params[0].dataIndex;
                        const categoryName = (categories || [])[categoryIndex];
                        const plan = (plannedData || [])[categoryIndex];
                        const actual = (actualData || [])[categoryIndex];
                        const gap = actual - plan;
                        // const unmet = (unmetData || [])[categoryIndex]; // Unmet might be large, focus on completion
                        
                        let tooltipText = `<div style="font-weight:bold;font-size:14px;margin-bottom:8px;">${categoryName || 'N/A'}</div>`;
                        tooltipText += `<div style="display:flex;justify-content:space-between;margin:5px 0;"><span>Planned:</span><span style="font-weight:bold;">${plan !== undefined ? plan.toLocaleString() : 'N/A'}</span></div>`;
                        tooltipText += `<div style="display:flex;justify-content:space-between;margin:5px 0;"><span>Actual:</span><span style="font-weight:bold;color:${gap >= 0 ? 'var(--green-primary)' : 'var(--red-primary)'};">${actual !== undefined ? actual.toLocaleString() : 'N/A'}</span></div>`;
                        tooltipText += `<div style="display:flex;justify-content:space-between;margin:5px 0;"><span>Gap:</span><span style="font-weight:bold;color:${gap >= 0 ? 'var(--blue-primary)' : 'var(--red-primary)'};">${gap !== undefined ? gap.toLocaleString() : 'N/A'}</span></div>`;
                        if (yAxisMaxValue > 0 && actual !== undefined) {
                             tooltipText += `<div style="display:flex;justify-content:space-between;margin:5px 0;"><span>Completion:</span><span style="font-weight:bold;">${Math.round((actual/yAxisMaxValue)*100)}%</span></div>`;
                        }
                        
                        return tooltipText;
                    },
                    backgroundColor: 'var(--bg-card)', // Use CSS var
                    borderColor: 'var(--border-color)', // Use CSS var
                    textStyle: { color: 'var(--text-primary)' }, // Use CSS var
                    extraCssText: 'box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15); border-radius: 8px; padding: 12px;'
                },
                grid: { left: '3%', right: '4%', bottom: '3%', top: '10%', containLabel: true },
                xAxis: [{
                    type: 'category',
                    data: categories || [],
                    axisTick: { alignWithLabel: true, lineStyle: { color: 'var(--border-color)' } },
                    axisLine: { lineStyle: { color: 'var(--border-color)' } },
                    axisLabel: {
                        color: 'var(--text-secondary)',
                        interval: 0, rotate: 30, margin: 12, fontSize: 11,
                        formatter: function(value) {
                            return value && value.length > 15 ? value.substring(0, 15) + '...' : value;
                        }
                    }
                }],
                yAxis: [{
                    type: 'value',
                    name: 'Quantity',
                    max: yAxisMaxValue,
                    nameTextStyle: { color: 'var(--text-secondary)', fontSize: 12, padding: [0, 0, 0, -5] },
                    splitLine: { lineStyle: { color: 'var(--border-color)', type: 'dashed' } },
                    axisLine: { show: true, lineStyle: { color: 'var(--border-color)' } },
                    axisLabel: { color: 'var(--text-secondary)', fontSize: 11,
                        formatter: function(value) { return value.toLocaleString(); }
                    }
                }],
                series: [
                    {
                        name: 'Actual Production',
                        type: 'bar',
                        stack: 'Total',
                        itemStyle: {
                            color: colors.actual,
                            barBorderRadius: [0, 0, 4, 4],
                            shadowBlur: 3,
                            shadowColor: 'rgba(0, 0, 0, 0.2)',
                            shadowOffsetX: 1,
                            shadowOffsetY: 1
                        },
                        label: {
                            show: true,
                            position: 'inside',
                            formatter: '{c}',
                            color: '#ffffff',
                            fontSize: 12,
                            fontWeight: 'bold'
                        },
                        data: actualData,
                        barWidth: '60%',
                        emphasis: { focus: 'series' }
                    },
                    {
                        name: 'Gap',
                        type: 'bar',
                        stack: 'Total',
                        label: {
                            show: true,
                            position: 'inside',
                            formatter: function(params) {
                                return params.value > 0 ? Math.abs(params.value) : '';
                            },
                            color: '#ffffff',
                            fontSize: 12,
                            fontWeight: 'bold'
                        },
                        data: gapSeriesData,
                        emphasis: { focus: 'series' }
                    },
                    {
                        name: 'Unmet Target',
                        type: 'bar',
                        stack: 'Total',
                        itemStyle: {
                            color: colors.unmet,
                            barBorderRadius: [4, 4, 0, 0]
                        },
                        label: {
                            show: false
                        },
                        data: unmetData,
                        emphasis: { focus: 'series' }
                    }
                ],
                animation: true,
                animationDuration: 1000,
                animationEasing: 'cubicOut'
            };
            
            myChart.setOption(option);
            
            // Responsive chart
            window.addEventListener('resize', function() {
                myChart.resize();
            });
        }

        // Initialize dashboard when DOM is ready
        document.addEventListener('DOMContentLoaded', initDashboard);
    </script>
</body>
</html>
from django import forms
from ..models import FFPartdetail


class OnHoldDeviceForm(forms.Form):
    serial_numbers = forms.CharField(
        widget=forms.Textarea(attrs={'rows': 10, 'cols': 40}),
        help_text='Enter one serial number per line.',required=False,
    )
    remark = forms.CharField(max_length=200, required=False, widget=forms.TextInput(attrs={'size': 30}))
    retcode = forms.IntegerField(min_value=0, widget=forms.TextInput(attrs={'size': 30}))
    isStatus = forms.BooleanField(required=False)
    isEngravingAssy = forms.BooleanField(required=False)
    isHoldCarton = forms.BooleanField(required=False)
    isHoldQAapproved = forms.BooleanField(required=False)







from django import forms
from ..models import luPartDetailDef  # 确保从正确的位置导入模型

class PartDetailForm(forms.Form):
    part_numbers = forms.CharField(
        widget=forms.Textarea(attrs={
            'placeholder': 'Enter part numbers, separated by new lines.',
            'class': 'form-control form-control-sm'
        }),
        required=False,
        label="PartNumbers"
    )

    part_detail_def_id = forms.ModelChoiceField(
        queryset=luPartDetailDef.objects.all(),
        label="PartDetailDef",
        required=False,
        widget=forms.Select(attrs={
            'class': 'form-control form-control-sm'
        })

    )

    content = forms.CharField(
        widget=forms.Textarea(attrs={
            'placeholder': 'Enter content for the part number',
            'class': 'form-control form-control-sm'
        }),
        required=False,
        label="Content"
    )


    def clean(self):
        cleaned_data = super().clean()
        # Make sure the key matches the field name
        part_numbers = cleaned_data.get('part_numbers')
        content = cleaned_data.get('content')

        # Add custom validation logic if needed
        return cleaned_data


class PartDetailUpdateForm(forms.ModelForm):
    class Meta:
        model = FFPartdetail
        fields = ['PartID', 'PartDetailDefID', 'Content']  # 或者 '__all__' 来包含所有字段


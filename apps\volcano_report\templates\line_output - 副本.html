{% extends 'base1.html' %}
{% load static %}

{% block head %}
<!-- <link rel="stylesheet" href="{% static 'css/your-styles.css' %}"> -->
<link href="https://cdn.datatables.net/rowgroup/1.3.1/css/rowGroup.dataTables.min.css" rel="stylesheet">
<link href="https://cdn.bootcdn.net/ajax/libs/layer/3.5.1/theme/default/layer.css" rel="stylesheet">
<script src="https://cdn.bootcdn.net/ajax/libs/layer/3.5.1/layer.min.js"></script>
{% endblock %}

{% block content %}
<div class="app-container">
    <h1 class="page-title">Volcano生产计划与产出查询确认</h1>

    <div class="form-section">
        <form method="post" id="queryForm">
            {% csrf_token %}
            <div class="form-layout">
                <div class="form-group">
                    <label for="project_type">项目类型</label>
                    <select class="form-control" id="project_type" name="project_type" required>
                        <option value="Hermes">Hermes</option>
                        <option value="Kosmos">Kosmos</option>
                        <option value="SMT">SMT</option>
                    </select>
                </div>
                <div class="form-group">
                    <label for="start_time">开始时间</label>
                    <input type="datetime-local" class="form-control" id="start_time" name="start_time" required>
                </div>
                <div class="form-group">
                    <label for="end_time">结束时间</label>
                    <input type="datetime-local" class="form-control" id="end_time" name="end_time" required>
                </div>
            </div>

            <div class="action-buttons">
                <button type="submit" class="btn btn-primary">查询</button>
                <button type="button" class="btn btn-info" id="exportServerBtn">导出Excel</button>
            </div>
        </form>
    </div>

    <!-- 数据列表 -->
    <div class="table-section mt-4">
        <table class="table table-striped" id="outputTable">
            <thead>
                <tr>
                    <th>Type</th>
                    <th>Line</th>
                    <th>PartNumber</th>
                    <th>Station</th>
                    <th>Date</th>
                    <th>Shift</th>
                    <th>Output</th>
                    <th>PlanQty</th>
                    <th>Gap</th>
                    <th>UPH</th>
                    <th>DTGap</th>
                    <th>StopTime</th>
                    <th>StopCount</th>
                    <th>停线信息</th>
                    <th>确认</th>
                </tr>
            </thead>
        </table>
    </div>
</div>

<style>
.app-container {
    padding: 20px;
    position: relative;
    max-width: 100%;
    overflow-x: auto;
}

.form-section {
    background: #fff;
    padding: 30px;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    max-width: 800px;
    margin: 20px auto;
}

.form-layout {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 20px;
    align-items: end;
}

.form-group {
    margin: 0;
}

.form-group label {
    display: block;
    margin-bottom: 8px;
    font-weight: 500;
    color: #333;
    font-size: 15px;
}

.form-control {
    width: 100%;
    height: 38px;
    border: 1px solid #ddd;
    border-radius: 4px;
    padding: 8px 12px;
}

.action-buttons {
    margin-top: 30px;
    display: flex;
    gap: 15px;
    justify-content: center;
}

.btn {
    min-width: 120px;
    height: 38px;
    font-size: 15px;
}

.table-section {
    background: #fff;
    padding: 20px;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    margin-top: 20px;
    overflow-x: auto;
    width: 100%;
}

.gap-negative {
    color: red;
}

.gap-positive {
    color: green;
}

.incident-details {
    max-height: none;
    background-color: #fff;
    border-radius: 4px;
    padding: 8px;
    overflow-x: auto;
}

.incident-item {
    padding: 8px;
    border: 1px solid #eee;
    margin-bottom: 8px;
    border-radius: 4px;
}

.incident-header {
    display: flex;
    justify-content: space-between;
    border-bottom: 1px solid #eee;
    padding-bottom: 4px;
    margin-bottom: 8px;
}

.incident-time {
    color: #666;
    font-weight: bold;
}

.incident-stoptime {
    color: #e74c3c;
    font-weight: bold;
}

.incident-content {
    padding: 4px 0;
}

.incident-desc {
    color: #333;
    display: block;
    margin-bottom: 8px;
}

.incident-footer {
    display: flex;
    justify-content: flex-end;
    color: #666;
    font-size: 0.9em;
}

.incident-person {
    font-style: italic;
}

.total-duration {
    background-color: #f8f9fa;
    padding: 8px;
    margin-bottom: 8px;
    border-radius: 4px;
    font-weight: bold;
    color: #e74c3c;
}

.has-incidents {
    background-color: #fff3cd80 !important;
}

.incident-details {
    position: relative;
}

.incident-details:hover {
    z-index: 1000;
}

.incident-table {
    width: 100%;
    border-collapse: collapse;
    font-size: 0.9em;
    margin-bottom: 0;
}

.incident-table th,
.incident-table td {
    padding: 6px;
    border: 1px solid #ddd;
    text-align: left;
    white-space: normal;
    word-break: break-word;
}

.incident-table th {
    background-color: #f8f9fa;
    font-weight: bold;
    position: sticky;
    top: 0;
    z-index: 1;
}

.incident-table .stoptime {
    color: #e74c3c;
    font-weight: bold;
    text-align: center;
}

.incident-table .total-row {
    background-color: #f8f9fa;
    font-weight: bold;
}

.incident-table .total-row td {
    border-top: 2px solid #dee2e6;
}

.incident-popup {
    position: absolute;
    left: 50%;
    transform: translateX(-50%);
    background-color: white;
    border-radius: 8px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    padding: 15px;
    z-index: 1100;
    min-width: 1000px;
    width: auto;
    max-height: 80vh;
    overflow-y: auto;
}

.incident-popup:hover {
    box-shadow: 0 4px 20px rgba(0,0,0,0.15);
}

.incident-table {
    width: 100%;
    border-collapse: collapse;
    font-size: 0.9em;
    margin-bottom: 0;
    background-color: white;
}

.incident-table th,
.incident-table td {
    padding: 6px;
    border: 1px solid #ddd;
    text-align: left;
    white-space: normal;
    word-break: break-word;
}

.incident-table th {
    background-color: #f8f9fa;
    font-weight: bold;
    position: sticky;
    top: 0;
    z-index: 1;
    padding: 10px;
}

.incident-popup .close-btn {
    position: absolute;
    right: 10px;
    top: 10px;
    cursor: pointer;
    padding: 5px;
    font-size: 18px;
    color: #666;
}

.incident-popup .close-btn:hover {
    color: #333;
}

.incident-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(0,0,0,0.5);
    z-index: 999;
    display: none;
}

/* 合并单元格的样式 */
.table td.text-nowrap {
    white-space: nowrap;
    vertical-align: middle;
    background-color: #f8f9fa;  /* 给合并的单元格添加背景色 */
}

/* 合并单元格的边框样式 */
.table td[rowspan] {
    border-right: 2px solid #dee2e6;
    border-left: 2px solid #dee2e6;
}

/* 第一个合并单元格的上边框 */
.table td[rowspan]:first-child {
    border-top: 2px solid #dee2e6;
}

/* 最后一个合并单元格的下边框 */
.table tr:last-child td[rowspan] {
    border-bottom: 2px solid #dee2e6;
}

/* 加载动画样式 */
.loading-text {
    padding: 20px;
    color: #666;
    font-size: 14px;
    text-align: center;
}

/* 自定义layer样式 */
.layui-layer-loading .layui-layer-content {
    padding: 20px;
    background-color: rgba(255, 255, 255, 0.9);
    border-radius: 4px;
}

/* 确认按钮样式 */
.confirm-btn {
    min-width: 80px;
    margin-right: 10px;
    position: relative;
    z-index: 2;
}

/* 确认弹窗样式 */
#confirmModal .modal-body {
    padding: 20px;
}

#confirmModal .form-group {
    margin-bottom: 15px;
}

#confirmModal label {
    font-weight: bold;
    margin-right: 10px;
}

.info-group {
    margin-bottom: 10px;
}
.info-group label {
    font-weight: bold;
    margin-right: 10px;
    min-width: 80px;
    display: inline-block;
}
.modal-lg {
    max-width: 800px;
}

/* 修改弹窗样式 */
.modal {
    z-index: 1050 !important;
}

.incident-overlay {
    z-index: 1000;
}

.incident-popup {
    z-index: 1100;
}

.table-section {
    position: relative;
    z-index: 1;
}

.modal-backdrop {
    z-index: 1040 !important;
}

/* 已确认按钮样式 */
.btn-success:disabled {
    opacity: 1;
    position: relative;
    z-index: 2;
}

/* DataTables 相关样式 */
.dataTables_wrapper {
    position: relative;
    z-index: 1;
    padding-bottom: 60px;
}

.dataTables_length, 
.dataTables_filter {
    margin-bottom: 15px;
}

.dataTables_info, 
.dataTables_paginate {
    position: absolute;
    bottom: 0;
}

.dataTables_info {
    left: 0;
}

.dataTables_paginate {
    right: 0;
}

/* 确保分页控件在最上层 */
.dataTables_paginate .paginate_button {
    position: relative;
    z-index: 10;
}

/* 确保搜索框在最上层 */
.dataTables_filter {
    position: relative;
    z-index: 10;
}

/* 确保每页显示条数选择器在最上层 */
.dataTables_length {
    position: relative;
    z-index: 10;
}

/* 表格容器样式 */
.dataTables_scroll {
    margin-bottom: 15px;
    overflow: visible;
}

/* 表格行样式 */
.table tbody tr {
    position: relative;
    z-index: 1;
}

/* 确保表格内容不会被遮挡 */
.table tbody tr:hover {
    z-index: 5;
}

/* 调整最后两列的宽度和位置 */
.dataTables_wrapper table.dataTable td:nth-last-child(1),
.dataTables_wrapper table.dataTable td:nth-last-child(2) {
    min-width: 100px;
    position: relative;
    z-index: 2;
}

/* 确保按钮容器有足够空间 */
.dataTables_wrapper table.dataTable td:nth-last-child(1) {
    padding-right: 20px;
}

/* 调整查看详情按钮样式 */
.view-incidents {
    min-width: 80px;
    margin-right: 10px;
}

/* 修改表格容器样式 */
.table-section {
    background: #fff;
    padding: 20px;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    margin-top: 20px;
    overflow: visible;
    position: relative;
}

/* 调整表格样式确保右侧有足够空间 */
#outputTable {
    width: calc(100% - 60px) !important;
    margin-right: 60px;
}

/* 调整最后列的样式 */
#outputTable td:nth-last-child(1),
#outputTable td:nth-last-child(2) {
    position: relative;
    z-index: 10;
    background: #fff;
    min-width: 100px;
}

/* 调整按钮样式 */
.view-incidents.btn,
.confirm-btn,
.btn-success[disabled] {
    position: relative;
    z-index: 11;
    min-width: 80px;
    white-space: nowrap;
    margin: 2px;
}

/* 确保表格行在hover时不会被遮挡 */
.table tbody tr:hover {
    z-index: 9;
}

/* 移除之前可能冲突的样式 */
.dataTables_wrapper table.dataTable td:nth-last-child(1),
.dataTables_wrapper table.dataTable td:nth-last-child(2) {
    min-width: auto;
}

/* 调整表格样式以适应更大的弹窗 */
.incident-table {
    width: 100%;
    border-collapse: collapse;
    font-size: 0.9em;
    margin-bottom: 0;
    background-color: white;
}

/* 调整表格列宽度 */
.incident-table th[style*="min-width:150px"] {
    min-width: 200px;
}

.incident-table th[style*="min-width:300px"] {
    min-width: 500px;
}

/* 确保弹窗在滚动时表头固定 */
.incident-table thead {
    position: sticky;
    top: 0;
    background: white;
    z-index: 2;
}

/* 调整表格列宽 */
#outputTable {
    width: 100%;
    min-width: 1500px;  /* 确保表有最小宽度 */
}

#outputTable th,
#outputTable td {
    white-space: nowrap;  /* 防止文本换行 */
    padding: 8px 12px;    /* 增加单元格内边距 */
}

/* 设置各列的最小宽度 */
#outputTable th:nth-child(1) { min-width: 100px; }  /* Type */
#outputTable th:nth-child(2) { min-width: 120px; }  /* Line */
#outputTable th:nth-child(3) { min-width: 150px; }  /* PartNumber */
#outputTable th:nth-child(4) { min-width: 150px; }  /* Station */
#outputTable th:nth-child(5) { min-width: 100px; }  /* Date */
#outputTable th:nth-child(6) { min-width: 80px; }   /* Shift */
#outputTable th:nth-child(7) { min-width: 80px; }   /* Output */
#outputTable th:nth-child(8) { min-width: 80px; }   /* PlanQty */
#outputTable th:nth-child(9) { min-width: 80px; }   /* Gap */
#outputTable th:nth-child(10) { min-width: 80px; }  /* UPH */
#outputTable th:nth-child(11) { min-width: 80px; }  /* DTGap */
#outputTable th:nth-child(12) { min-width: 100px; } /* StopTime */
#outputTable th:nth-child(13) { min-width: 100px; } /* StopCount */
#outputTable th:nth-child(14) { min-width: 100px; } /* Incidents */
#outputTable th:nth-child(15) { min-width: 80px; }  /* 确认 */

/* 调整表格高度 */
.dataTables_scrollBody {
    max-height: 70vh !important;  /* 设置为视窗高度的70% */
    overflow-y: auto !important;
}

/* 确保表头固定 */
.dataTables_scrollHead {
    position: sticky;
    top: 0;
    z-index: 1;
    background: white;
}

/* 美化滚动条 */
.dataTables_scrollBody::-webkit-scrollbar {
    width: 8px;
    height: 8px;
}

.dataTables_scrollBody::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 4px;
}

.dataTables_scrollBody::-webkit-scrollbar-thumb {
    background: #888;
    border-radius: 4px;
}

.dataTables_scrollBody::-webkit-scrollbar-thumb:hover {
    background: #555;
}

/* 页面标题样式 */
.page-title {
    text-align: center;
    margin-bottom: 30px;
    font-size: 24px;
    color: #333;
    font-weight: 500;
}

/* 表格部分样式调整 */
.table-section {
    margin-top: 30px;
    padding: 25px;
}

/* 调整表格上方的间距 */
.mt-4 {
    margin-top: 2rem !important;
}

/* 调整表格整体样式 */
#outputTable {
    font-size: 12px;  /* 减小字体大小 */
    width: 100% !important;  /* 确保表格占满容器 */
    margin-right: 0;  /* 移除右侧边距 */
}

/* 调整表格单元格样式 */
#outputTable th,
#outputTable td {
    padding: 4px 6px;  /* 减小单元格内边距 */
    white-space: nowrap;
    vertical-align: middle;
}

/* 设置各列的最小宽度，调整为更小的值 */
#outputTable th:nth-child(1) { min-width: 80px; }   /* Type */
#outputTable th:nth-child(2) { min-width: 100px; }  /* Line */
#outputTable th:nth-child(3) { min-width: 120px; }  /* PartNumber */
#outputTable th:nth-child(4) { min-width: 120px; }  /* Station */
#outputTable th:nth-child(5) { min-width: 80px; }   /* Date */
#outputTable th:nth-child(6) { min-width: 60px; }   /* Shift */
#outputTable th:nth-child(7) { min-width: 60px; }   /* Output */
#outputTable th:nth-child(8) { min-width: 60px; }   /* PlanQty */
#outputTable th:nth-child(9) { min-width: 60px; }   /* Gap */
#outputTable th:nth-child(10) { min-width: 60px; }  /* UPH */
#outputTable th:nth-child(11) { min-width: 60px; }  /* DTGap */
#outputTable th:nth-child(12) { min-width: 80px; }  /* StopTime */
#outputTable th:nth-child(13) { min-width: 80px; }  /* StopCount */
#outputTable th:nth-child(14) { min-width: 80px; }  /* Incidents */
#outputTable th:nth-child(15) { min-width: 70px; }  /* 确认 */

/* 调整按钮样式 */
.view-incidents.btn,
.confirm-btn,
.btn-success[disabled] {
    padding: 2px 8px;  /* 减小按钮内边距 */
    font-size: 12px;   /* 减小按钮字体 */
    min-width: 60px;   /* 减小按钮最小宽度 */
    margin: 1px;       /* 减小按钮间距 */
}

/* 调整表格容器样式 */
.table-section {
    padding: 10px;    /* 减小容器内边距 */
    margin-top: 15px;
}

/* 调整滚动区域样式 */
.dataTables_scrollBody {
    max-height: 75vh !important;  /* 增加可视区域高度 */
}

/* 优化表格响应式布局 */
@media screen and (max-width: 1400px) {
    #outputTable {
        font-size: 11px;  /* 在小屏幕上进一步减小字体 */
    }
    
    #outputTable th,
    #outputTable td {
        padding: 3px 4px;  /* 在小屏幕上进一步减小内边距 */
    }
}
</style>

<!-- 添加确认弹窗 -->
<div class="modal fade" id="confirmModal" tabindex="-1" role="dialog">
    <div class="modal-dialog modal-lg" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">确认产出信息</h5>
                <button type="button" class="close" data-dismiss="modal">
                    <span>&times;</span>
                </button>
            </div>
            <div class="modal-body">
                <div class="row">
                    <div class="col-md-6">
                        <div class="info-group">
                            <label>线别：</label>
                            <span id="confirmLine" class="font-weight-bold"></span>
                        </div>
                        <div class="info-group">
                            <label>日期：</label>
                            <span id="confirmDate" class="font-weight-bold"></span>
                        </div>
                        <div class="info-group">
                            <label>班别：</label>
                            <span id="confirmShift" class="font-weight-bold"></span>
                        </div>
                        <div class="info-group">
                            <label>类型：</label>
                            <span id="confirmType" class="font-weight-bold"></span>
                        </div>
                        <div class="info-group">
                            <label>料号：</label>
                            <span id="confirmPartNumber" class="font-weight-bold"></span>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="info-group">
                            <label>站别：</label>
                            <span id="confirmStation" class="font-weight-bold"></span>
                        </div>
                        <div class="info-group">
                            <label>产出：</label>
                            <span id="confirmOutput" class="font-weight-bold"></span>
                        </div>
                        <div class="info-group">
                            <label>计划数量：</label>
                            <span id="confirmPlanQty" class="font-weight-bold"></span>
                        </div>
                        <div class="info-group">
                            <label>差异：</label>
                            <span id="confirmGap" class="font-weight-bold"></span>
                        </div>
                        <div class="info-group">
                            <label>停线时长：</label>
                            <span id="confirmStopTime" class="font-weight-bold"></span>
                        </div>
                        <div class="info-group">
                            <label>停线次数：</label>
                            <span id="confirmStopCount" class="font-weight-bold"></span>
                        </div>
                        <div class="info-group">
                            <label>UPH：</label>
                            <span id="confirmUPH" class="font-weight-bold"></span>
                        </div>
                        <div class="info-group">
                            <label>DTGap：</label>
                            <span id="confirmDTGap" class="font-weight-bold"></span>
                        </div>
                    </div>
                </div>
                
                <!-- 停线详情 -->
                <div class="mt-3" id="confirmIncidentsSection" style="display: none;">
                    <h6>停线详情：</h6>
                    <div id="confirmIncidents" class="border p-2" style="max-height: 200px; overflow-y: auto;">
                    </div>
                </div>

                <div class="form-group mt-3">
                    <label for="confirmNote">备注：<span class="text-danger">*</span></label>
                    <textarea class="form-control" id="confirmNote" rows="3" required></textarea>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-dismiss="modal">取消</button>
                <button type="button" class="btn btn-primary" id="saveConfirm">确认</button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
// 将 formatDateTime 函数移到全局作用域
function formatDateTime(dateTimeStr) {
    var dt = new Date(dateTimeStr);
    return dt.getFullYear() + '-' + 
        String(dt.getMonth() + 1).padStart(2, '0') + '-' + 
        String(dt.getDate()).padStart(2, '0') + ' ' + 
        String(dt.getHours()).padStart(2, '0') + ':' + 
        String(dt.getMinutes()).padStart(2, '0') + ':00';
}

$(document).ready(function() {
    // 设置默认时间范围（今天8点到明天8点）
    function setDefaultTimeRange() {
        var today = new Date();
        var tomorrow = new Date(today);
        tomorrow.setDate(today.getDate() + 1);
        
        // 设置时间 08:00
        var startDate = new Date(today.getFullYear(), today.getMonth(), today.getDate(), 8, 0);
        var endDate = new Date(tomorrow.getFullYear(), tomorrow.getMonth(), tomorrow.getDate(), 8, 0);
        
        // 格式化日期时间字符串
        var startStr = startDate.getFullYear() + '-' + 
                      String(startDate.getMonth() + 1).padStart(2, '0') + '-' + 
                      String(startDate.getDate()).padStart(2, '0') + 'T08:00';
        
        var endStr = endDate.getFullYear() + '-' + 
                    String(endDate.getMonth() + 1).padStart(2, '0') + '-' + 
                    String(endDate.getDate()).padStart(2, '0') + 'T08:00';
        
        $('#start_time').val(startStr);
        $('#end_time').val(endStr);
    }
    
    setDefaultTimeRange();

    // 初始化DataTable
    var table = $('#outputTable').DataTable({
        processing: true,
        serverSide: false,
        searching: false,  // 禁用搜索
        ordering: true,
        paging: false,    // 禁用分页
        info: false,      // 禁用信息显示
        dom: 't',         // 只显示表格,移除所有其他控件
        columns: [
            { data: 'Type' },
            { data: 'Line' },
            { data: 'PartNumber' },
            { data: 'Station' },
            { data: 'Date' },
            { data: 'shift' },
            { data: 'Output' },
            { data: 'PlanQty' },
            { 
                data: 'Gap',
                render: function(data, type, row) {
                    if (type === 'display') {
                        var className = parseInt(data) < 0 ? 'gap-negative' : 'gap-positive';
                        return '<span class="' + className + '">' + data + '</span>';
                    }
                    return data;
                }
            },
            { data: 'UPH' },
            { 
                data: 'DTGap',
                render: function(data, type, row) {
                    return data ? data.toFixed(2) : '0.00';
                }
            },
            {
                data: 'incidents',
                render: function(data, type, row) {
                    if (!data || data.length === 0) return '0分钟';
                    var totalStopTime = 0;
                    data.forEach(function(incident) {
                        totalStopTime += parseInt(incident.StopTime) || 0;
                    });
                    return `<span class="stoptime">${totalStopTime}分钟</span>`;
                }
            },
            {
                data: 'incidents',
                render: function(data, type, row) {
                    return data ? data.length : 0;
                }
            },
            {
                data: 'incidents',
                render: function(data, type, row) {
                    if (!data || data.length === 0) return '';
                    
                    return `
                        <a href="#" class="view-incidents btn btn-sm btn-info">查看详情</a>
                        <div class="incident-popup" style="display:none;">
                            <span class="close-btn">&times;</span>
                            <table class="incident-table">
                                <thead>
                                    <tr>
                                        <th style="min-width:150px">时间段</th>
                                        <th style="min-width:80px">时长</th>
                                        <th style="min-width:300px">问题描述</th>
                                        <th style="min-width:100px">责任人</th>
                                    </tr>
                                </thead>
                                <tbody>
                                ${data.map(incident => `
                                    <tr>
                                        <td>${incident.StartTime.substr(11, 5)} - ${incident.EndTime.substr(11, 5)}</td>
                                        <td class="stoptime">${incident.StopTime}分钟</td>
                                        <td>${incident.IssueDescription}</td>
                                        <td>${incident.ResponsiblePerson}</td>
                                    </tr>
                                `).join('')}
                                </tbody>
                            </table>
                        </div>
                    `;
                }
            },
            {
                data: null,
                title: '确认',
                orderable: false,
                render: function(data, type, row) {
                    if (row.isConfirmed) {
                        return '<button class="btn btn-success" disabled>已确认</button>';
                    } else {
                        return '<button class="btn btn-primary confirm-btn">确认</button>';
                    }
                }
            }
        ],
        scrollY: '70vh',  // 改为使用视窗高度的70%
        scrollX: true,    // 启用水平滚动
        scrollCollapse: true,
        fixedHeader: true,  // 固定表头
        autoWidth: false,   // 禁用自动宽度计算
        order: [
            [1, 'asc'],  // Line
            [4, 'asc'],  // Date
            [5, 'asc']   // shift
        ],
    });

    // 表单提交
    $('#queryForm').on('submit', function(e) {
        e.preventDefault();
        
        var loadingIndex = layer.load(1, {
            shade: [0.3, '#fff'],
            content: '<div class="loading-text">正在查询数据，请稍候...</div>'
        });
        
        var startTime = formatDateTime($('#start_time').val());
        var endTime = formatDateTime($('#end_time').val());
        var projectType = $('#project_type').val();
        
        $.ajax({
            url: '{% url "volcanoreport:line_output_data" %}',
            type: 'POST',
            data: {
                'start_time': startTime,
                'end_time': endTime,
                'project_type': projectType,
                'csrfmiddlewaretoken': '{{ csrf_token }}'
            },
            success: function(response) {
                layer.close(loadingIndex);
                console.log('返回的数据:', response.data);
                if(response.data) {
                    table.clear().rows.add(response.data).draw();
                    layer.msg('查询成功', {icon: 1});
                } else {
                    layer.msg('查询失败：' + response.error, {icon: 2});
                }
            },
            error: function() {
                layer.close(loadingIndex);
                layer.msg('系统错误，请稍后重试', {icon: 2});
            }
        });
    });

    // 修改出Excel按钮的处理逻辑
    $('#exportBtn').on('click', function() {
        if (typeof XLSX === 'undefined') {
            layer.msg('导出组件未加载，请刷新页面重试');
            return;
        }
        
        // 获取当前DataTable中的所有数据
        var data = table.data().toArray();
        
        if (data.length === 0) {
            layer.msg('没有数据可供导出');
            return;
        }
        
        // 创建一个工作簿
        var wb = XLSX.utils.book_new();
        
        // 将数据转换为工作表
        var ws = XLSX.utils.json_to_sheet(data);
        
        // 将工作表加到工作簿
        XLSX.utils.book_append_sheet(wb, ws, "Sheet1");
        
        // 生成文件名
        var fileName = 'LineOutput_' + moment().format('YYYYMMDD_HHmmss') + '.xlsx';
        
        // 导出Excel文件
        XLSX.writeFile(wb, fileName);
    });

    // 服务器端导出按钮处理
    $('#exportServerBtn').on('click', function() {
        var startTime = formatDateTime($('#start_time').val());
        var endTime = formatDateTime($('#end_time').val());
        var projectType = $('#project_type').val();
        
        window.location.href = '{% url "volcanoreport:line_output_export" %}?' + 
            'start_time=' + encodeURIComponent(startTime) + 
            '&end_time=' + encodeURIComponent(endTime) + 
            '&project_type=' + encodeURIComponent(projectType);
    });

    // 添加点击事件处理
    $(document).on('click', '.view-incidents', function(e) {
        e.preventDefault();
        // 先关闭所有其他打开的弹窗
        $('.incident-popup').hide();
        $('.incident-overlay').hide();
        
        // 只显示当前点击的弹窗
        $('.incident-overlay').show();
        $(this).next('.incident-popup').show();
    });

    $(document).on('click', '.close-btn', function(e) {
        e.stopPropagation();
        $('.incident-overlay').hide();
        $('.incident-popup').hide();
    });

    $(document).on('click', '.incident-overlay', function() {
        $('.incident-overlay').hide();
        $('.incident-popup').hide();
    });

    // 修改确认按钮的点击事件处理
    $(document).on('click', '.confirm-btn', function(e) {
        e.preventDefault();
        var btn = $(this);
        var currentRow = table.row(btn.closest('tr'));
        var rowData = currentRow.data();
        
        // 保存当前行的用，用于后续保存时使用
        $('#confirmModal').data('currentRow', currentRow);
        
        // 填充确认窗口信息
        $('#confirmLine').text(rowData.Line);
        $('#confirmDate').text(rowData.Date);
        $('#confirmShift').text(rowData.shift);
        $('#confirmType').text(rowData.Type || '-');
        $('#confirmPartNumber').text(rowData.PartNumber || '-');
        $('#confirmStation').text(rowData.Station || '-');
        $('#confirmOutput').text(rowData.Output || '0');
        $('#confirmPlanQty').text(rowData.PlanQty || '0');
        $('#confirmGap').text(rowData.Gap || '0');
        $('#confirmUPH').text(rowData.UPH || '0');
        $('#confirmDTGap').text(rowData.DTGap || '0.00');
        
        // 处理停线信息
        if (rowData.incidents && rowData.incidents.length > 0) {
            var totalStopTime = rowData.incidents.reduce((sum, inc) => sum + parseInt(inc.StopTime), 0);
            $('#confirmStopTime').text(totalStopTime + '分钟');
            $('#confirmStopCount').text(rowData.incidents.length);
            
            // 显示停线详情
            var incidentDetails = rowData.incidents.map(inc => 
                `<div class="incident-item">
                    <div class="incident-time">时间: ${inc.StartTime.substr(11, 5)}-${inc.EndTime.substr(11, 5)}</div>
                    <div class="incident-duration">时长: ${inc.StopTime}分钟</div>
                    <div class="incident-desc">问题: ${inc.IssueDescription}</div>
                    <div class="incident-person">责任人: ${inc.ResponsiblePerson}</div>
                </div>`
            ).join('');
            
            $('#confirmIncidents').html(incidentDetails);
            $('#confirmIncidentsSection').show();
        } else {
            $('#confirmStopTime').text('0分钟');
            $('#confirmStopCount').text('0');
            $('#confirmIncidentsSection').hide();
        }
        
        $('#confirmNote').val('');
        
        // 显示弹窗
        $('#confirmModal').modal('show');
    });

    // 修改保存确认信息的处理
    $('#saveConfirm').on('click', function() {
        var note = $('#confirmNote').val().trim();
        if (!note) {
            layer.msg('请输入备注信息', {icon: 2});
            return;
        }
        
        // 取保存的行数据
        var currentRow = $('#confirmModal').data('currentRow');
        var rowData = currentRow.data();
        
        var data = {
            line: rowData.Line,
            date: rowData.Date,
            shift: rowData.shift,
            note: note,
            type: rowData.Type || '',
            partNumber: rowData.PartNumber || '',
            station: rowData.Station || '',
            output: parseInt(rowData.Output) || 0,
            planqty: parseInt(rowData.PlanQty) || 0,
            gap: parseInt(rowData.Gap) || 0,
            UPH: parseInt(rowData.UPH) || 0,  // 添加UPH参数
            stopTime: rowData.incidents ? (rowData.incidents.reduce((sum, inc) => sum + parseInt(inc.StopTime), 0) + '分钟') : '0分钟',
            stopCount: rowData.incidents ? rowData.incidents.length : '0',
            incidentsDetail: rowData.incidents ? rowData.incidents.map(inc => 
                `时间: ${inc.StartTime.substr(11, 5)}-${inc.EndTime.substr(11, 5)}, ` +
                `时长: ${inc.StopTime}分钟, ` +
                `问题: ${inc.IssueDescription}, ` +
                `责任人: ${inc.ResponsiblePerson}`
            ).join('\n') : '',
            csrfmiddlewaretoken: '{{ csrf_token }}'
        };
        
        // 在发送前打印数据以便调试
        console.log('保存确认数据:', data);
        
        $.ajax({
            url: '{% url "volcanoreport:save_confirmation" %}',
            type: 'POST',
            data: data,
            success: function(response) {
                if (response.success) {
                    layer.msg('确认信息已保存', {icon: 1});
                    $('#confirmModal').modal('hide');
                    
                    // 更新行数据中的确认状态
                    var rowData = currentRow.data();
                    rowData.isConfirmed = true;
                    currentRow.data(rowData).draw(false);  // 更新行数据并重绘
                    
                } else {
                    layer.msg(response.error || '保存失败', {icon: 2});
                }
            },
            error: function() {
                layer.msg('系统错误，请稍后重试', {icon: 2});
            }
        });
    });

    // 添加窗口大小改变时的处理
    $(window).on('resize', function() {
        table.columns.adjust();
    });
});
</script>
{% endblock %} 
{% extends 'base1.html' %}
{% load static %}

{% block content %}
<div class="app-container">
    <h1 class="page-title">生产计划管理</h1>

    <div class="form-section">
        <div class="alert alert-info" role="alert">
            <i class="fas fa-info-circle"></i> 提示：
            <ul class="mb-0">
                <li>保存后将保留当前输入的信息，方便继续录入</li>
                <li>在计划数量输入框中按回车键可快速保存</li>
                <li>输入UPH后会自动计算建议的计划数量</li>
            </ul>
        </div>
        <form method="post" id="productionPlanForm">
            {% csrf_token %}
            <div class="form-layout">
                <div class="form-column">
                    <!-- 料号选择 -->
                    <div class="form-group">
                        <label for="id_Partnumber">料号<span class="text-danger">*</span></label>
                        <select class="form-control select2" id="id_Partnumber" name="Partnumber" style="width: 100%" required>
                            <option value="">请选择料号</option>
                        </select>
                    </div>
                    
                    <!-- 日期选择 -->
                    <div class="form-group">
                        <label for="id_Date">计划日期</label>
                        <input type="date" class="form-control" id="id_Date" name="Date" value="{% now 'Y-m-d' %}">
                    </div>
                </div>

                <div class="form-column">
                    <!-- 项目名称(自动填充) -->
                    <div class="form-group">
                        <label for="id_Project">项目</label>
                        <input type="text" class="form-control" id="id_Project" name="Project" readonly>
                    </div>
                    
                    <!-- 线别选择 -->
                    <div class="form-group">
                        <label for="id_LineName">线别</label>
                        <select class="form-control select2" id="id_LineName" name="LineName" style="width: 100%" required>
                            <option value="">请选择线别</option>
                        </select>
                    </div>
                </div>
            </div>

            <!-- 班次和计划数量 -->
            <div class="form-layout">
                <div class="form-column">
                    <div class="form-group">
                        <label for="id_Shift">班次</label>
                        <select class="form-control" id="id_Shift" name="Shift">
                            <option value="Day">白班</option>
                            <option value="Night">夜班</option>
                        </select>
                    </div>
                </div>
                <div class="form-column">
                    <div class="form-group">
                        <label for="id_PlanQty">计划数量<span class="text-danger">*</span></label>
                        <input type="number" class="form-control" id="id_PlanQty" name="PlanQty" required>
                    </div>
                </div>
            </div>

            <div class="action-buttons">
                <button type="submit" class="btn btn-primary">保存</button>
                <button type="button" class="btn btn-secondary" onclick="window.history.back()">返回</button>
            </div>
        </form>
    </div>

    <!-- 数据列表 -->
    <div class="table-section mt-4">
        <table class="table table-striped" id="planTable">
            <thead>
                <tr>
                    <th>ID</th>
                    <th>项目</th>
                    <th>料号</th>
                    <th>线别</th>
                    <th>日期</th>
                    <th>班次</th>
                    <th>计划数量</th>
                    <th>创建时间</th>
                    <th>创建人</th>
                    <th>操作</th>
                </tr>
            </thead>
        </table>
    </div>
</div>

<style>
.app-container {
    padding: 20px;
}

.form-section {
    background: #fff;
    padding: 20px;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.form-layout {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 20px;
}

.action-buttons {
    margin-top: 20px;
}

.table-section {
    background: #fff;
    padding: 20px;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}
</style>
{% endblock %}

{% block extra_js %}
<script>
$(document).ready(function() {
    // 初始化Select2
    $('#id_Partnumber').select2({
        ajax: {
            url: '{% url "volcanoreport:get_partnumbers" %}',
            dataType: 'json',
            delay: 250,
            data: function(params) {
                return {
                    q: params.term || ''
                };
            },
            processResults: function(data) {
                return {
                    results: data.items
                };
            },
            cache: true
        },
        placeholder: '请输入或选择料号',
        allowClear: true,
        minimumInputLength: 0,
        templateResult: formatPartNumber,
        templateSelection: formatPartNumberSelection
    }).trigger('select2:open');

    function formatPartNumber(partnumber) {
        if (!partnumber.id) return partnumber.text;
        return $('<span>' + partnumber.text + 
                (partnumber.project ? ' (' + partnumber.project + ')' : '') + 
                '</span>');
    }

    function formatPartNumberSelection(partnumber) {
        return partnumber.text || partnumber.id;
    }

    // 当选择料号时自动填充项目
    $('#id_Partnumber').on('select2:select', function(e) {
        var data = e.params.data;
        if(data.project) {
            $('#id_Project').val(data.project);
        }
    });

    // 初始化线别选择
    $('#id_LineName').select2({
        ajax: {
            url: '{% url "volcanoreport:get_lines" %}',
            dataType: 'json',
            delay: 250,
            data: function(params) {
                return {
                    q: params.term || ''
                };
            },
            processResults: function(data) {
                return {
                    results: data.items
                };
            },
            cache: true
        },
        placeholder: '请选择线别',
        allowClear: true,
        minimumInputLength: 0
    }).on('select2:open', function() {
        // 打开下拉框时自动加载所有选项
        if (!$(this).data('loaded')) {
            $.ajax({
                url: '{% url "volcanoreport:get_lines" %}',
                dataType: 'json',
                success: function(data) {
                    var defaultOption = new Option('请选择线别', '', true, true);
                    $('#id_LineName').append(defaultOption);
                    
                    // 添加所有线别
                    data.items.forEach(function(item) {
                        var option = new Option(item.text, item.id, false, false);
                        $('#id_LineName').append(option);
                    });
                    
                    $('#id_LineName').data('loaded', true);
                }
            });
        }
    });

    // 页面载时立即加载所有线别
    $.ajax({
        url: '{% url "volcanoreport:get_lines" %}',
        dataType: 'json',
        success: function(data) {
            var defaultOption = new Option('请选择线别', '', true, true);
            $('#id_LineName').append(defaultOption);
            
            // 添加所有线别
            data.items.forEach(function(item) {
                var option = new Option(item.text, item.id, false, false);
                $('#id_LineName').append(option);
            });
        }
    });

    // 初始化DataTable
    var table = $('#planTable').DataTable({
        ajax: '{% url "volcanoreport:production_plan_list" %}',
        columns: [
            { data: 'ID' },
            { data: 'Project' },
            { data: 'Partnumber' },
            { data: 'LineName' },
            { data: 'Date' },
            { data: 'Shift' },
            { data: 'PlanQty' },
            { data: 'CreateTime' },
            { data: 'CreateBy' },
            {
                data: null,
                render: function(data, type, row) {
                    return '<button class="btn btn-sm btn-danger" onclick="deletePlan(' + row.ID + ')">删除</button>';
                }
            }
        ],
        order: [[4, 'desc']],
        pageLength: 25
    });

    // 设置日期默认值为今天
    function setDefaultDate() {
        var today = new Date();
        var year = today.getFullYear();
        var month = (today.getMonth() + 1).toString().padStart(2, '0');
        var day = today.getDate().toString().padStart(2, '0');
        var formattedDate = `${year}-${month}-${day}`;
        $('#id_Date').val(formattedDate);
    }

    // 如果日期为空，设置为今天
    if (!$('#id_Date').val()) {
        setDefaultDate();
    }

    // 在表单重置时也设置为今天
    $('#productionPlanForm').on('reset', function() {
        setTimeout(function() {
            setDefaultDate();
        }, 0);
    });

    // 表单提交的代码修改
    $('#productionPlanForm').on('submit', function(e) {
        e.preventDefault();
        
        // 添加表单验证
        var partnumber = $('#id_Partnumber').val();
        var lineName = $('#id_LineName').val();
        
        if (!partnumber) {
            layer.msg('请选择料号');
            return false;
        }
        
        if (!lineName) {
            layer.msg('请选择线别');
            return false;
        }
        
        var formData = $(this).serialize();
        console.log('提交的数据:', formData);
        $.ajax({
            url: '{% url "volcanoreport:production_plan_create" %}',
            type: 'POST',
            data: formData,
            success: function(response) {
                if(response.success) {
                    table.ajax.reload();
                    // 移除之前的重置代码，改为显示成功消息
                    layer.msg('保存成功');
                    
                    // 可选：清空计划数量，因为新计划可能需要不同数量
                    $('#id_PlanQty').val('');
                    
                    // 让用户聚焦到计划数量输入框，方便继续输入
                    $('#id_PlanQty').focus();
                } else {
                    layer.msg(response.error || '保存失败');
                }
            },
            error: function() {
                layer.msg('系统错误');
            }
        });
    });

    // 页面加载时立即加载默认数据
    $.ajax({
        url: '{% url "volcanoreport:get_partnumbers" %}',
        dataType: 'json',
        success: function(data) {
            // 添加默认选项
            var defaultOption = new Option('请选择料号', '', true, true);
            $('#id_Partnumber').append(defaultOption);
            
            // 添加最近20条料号
            data.items.forEach(function(item) {
                var option = new Option(item.text + ' (' + item.project + ')', item.id, false, false);
                $('#id_Partnumber').append(option);
            });
        }
    });

    // 添加快捷键支持：按回车键提交表单
    $(document).on('keydown', function(e) {
        if (e.keyCode === 13 && !e.shiftKey) {  // 回车键，但不是和shift一起按
            if ($('#id_PlanQty').is(':focus')) {  // 如果焦点在计划数量输入框
                $('#productionPlanForm').submit();
                e.preventDefault();
            }
        }
    });
    
    // 添加自动计算功能
    $('#id_UPH').on('input', function() {
        calculatePlanQty();
    });
    
    $('#id_Shift').on('change', function() {
        calculatePlanQty();
    });
    
    function calculatePlanQty() {
        var uph = parseInt($('#id_UPH').val()) || 0;
        var shift = $('#id_Shift').val();
        var hours = (shift === 'Day' || shift === 'Night') ? 12 : 8; // 默认12小时制
        if (uph > 0) {
            var planQty = uph * hours;
            $('#id_PlanQty').val(planQty);
        }
    }
});

function deletePlan(id) {
    layer.confirm('确定要删除这条记录吗？', {
        btn: ['确定','取消']
    }, function(){
        $.ajax({
            url: '{% url "volcanoreport:production_plan_delete" %}',
            type: 'POST',
            data: {
                'id': id,
                'csrfmiddlewaretoken': '{{ csrf_token }}'
            },
            success: function(response) {
                if(response.success) {
                    $('#planTable').DataTable().ajax.reload();
                    layer.msg('删除成功');
                } else {
                    layer.msg(response.error || '删除失败');
                }
            },
            error: function() {
                layer.msg('系统错误');
            }
        });
    });
}
</script>
{% endblock %} 
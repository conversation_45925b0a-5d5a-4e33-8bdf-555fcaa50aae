from django.db import models
from django.core.validators import MinValueValidator, MaxValueValidator
from django.utils import timezone # 导入 timezone

class MyManager(models.Manager):
  def get_queryset(self):
    return super().get_queryset().using('VolcanoReporDB')
  

class ffLine(models.Model):
   objects = MyManager() 
   ID = models.AutoField(primary_key=True) 
   Description = models.CharField(max_length=50)

   def __str__(self):
       return f" {self.Description}"

   class Meta:
       db_table = 'ffLine' 
       managed = False 

class UDTProductionPlanByHour(models.Model):
    objects = MyManager() 

    line = models.ForeignKey(
        ffLine, 
        on_delete=models.DO_NOTHING, 
        db_column='LineID', 
        verbose_name="产线"
    )

    stationid = models.CharField(max_length=50, null=True, blank=True, verbose_name="工位标识")

    PlanDate = models.DateField(verbose_name="计划日期")

    PlanHour = models.PositiveSmallIntegerField(
        verbose_name="计划小时",
        validators=[MinValueValidator(0), MaxValueValidator(23)]
    )

    PlannedQuantity = models.IntegerField(
   
        verbose_name="计划产出数量",
        validators=[MinValueValidator(0)]
    )

    ImportBatchID = models.CharField(max_length=100, null=True, blank=True, verbose_name="导入批次号")

    createdat = models.DateTimeField(verbose_name="记录创建时间", default=timezone.now) # 添加 default
    updatedat = models.DateTimeField(verbose_name="记录最后更新时间", default=timezone.now) # 添加 default

    class Meta:
        verbose_name = "小时生产计划"
        verbose_name_plural = "小时生产计划"
        db_table = 'udtProductionPlanbyHour'  
        managed = False  
        # unique_together = (('line', 'stationid', 'PlanDate', 'PlanHour'),) 

    def __str__(self):
        return f"{self.PlanDate} {self.PlanHour}:00 - {self.line.Description if self.line else 'N/A'} - Qty: {self.PlannedQuantity}"
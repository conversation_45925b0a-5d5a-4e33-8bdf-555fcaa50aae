{% extends "dashboard/base_dashboard.html" %}
{% load static %}

{% block title %}Packing Production Dashboard{% endblock %}

{% block page_styles %}
<style>
    /* Override card-header style for this page */
    .card-header {
        padding: 1.25rem 1.5rem;
        border-bottom: 1px solid var(--border-color);
        display: flex;
        justify-content: center !important;
        align-items: center;
        text-align: center;
        width: 100%;
    }

    .card-header .card-title {
        width: 100%;
        text-align: center;
    }

    /* New CSS for chart grid layout - specific to this page or can be moved to base if used elsewhere */
    .chart-grid,
    .progress-cards {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(400px, 1fr)); /* Responsive columns */
        gap: 2rem; /* Space between charts/cards */
        align-items: start; /* Align cards to the top */
        margin-bottom: 2rem; /* Add margin below the grid */
        width: 100%;
        box-sizing: border-box;
    }

    /* Responsive adjustments for chart grid and progress cards */
    @media (max-width: 900px) {
        .chart-grid,
        .progress-cards {
            grid-template-columns: 1fr; /* Stack on smaller screens */
            gap: 1rem;
        }
    }

    /* Styles for the Clock Card */
    .clock-card {
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        padding: 1.5rem; /* Increased padding for better spacing */
        text-align: center;
        height: 100%; /* Ensure it takes full height of the grid cell */
    }

    .clock-time {
        font-size: 2.8rem; /* Larger font for time */
        font-weight: 600;
        color: var(--text-primary);
        margin-bottom: 0.5rem;
        line-height: 1.2;
    }

    .clock-seconds {
        font-size: 1.5rem; /* Slightly smaller for seconds */
        color: var(--blue-primary); /* Use a theme color for seconds */
        margin-left: 0.3rem;
    }
    
    .clock-date {
        font-size: 0.9rem; /* Smaller font for date */
        color: var(--text-secondary);
    }
    
    .clock-card .progress-card-icon { /* Re-use icon style if needed or make a new one */
        font-size: 1.75rem; /* Larger icon for clock */
        margin-bottom: 0.75rem;
        color: var(--blue-primary); /* Match seconds color or choose another */
    }

</style>
{% endblock %}

{% block content %}
<div class="dashboard-header">
    <h1 class="dashboard-title">Packing Plan VS Actual</h1>
    <!-- <p class="dashboard-subtitle">Real-time production monitoring and comparison</p> -->
</div>

<!-- Summary cards -->
<div class="progress-cards">
    <!-- Hermes Summary Card -->
    <div class="progress-card" id="hermesSummary">
        <div class="progress-card-header">
            <h3 class="progress-card-title">Hermes Packing</h3>
            <div class="progress-card-icon">
                <i class="fas fa-box"></i>
            </div>
        </div>
        <div class="progress-stats">
            <div>
                <div class="progress-value" id="hermesActual">0</div>
                <div class="progress-label">Actual Production</div>
            </div>
            <div>
                <div class="progress-percentage" id="hermesPercentage">0%</div>
                <div class="progress-label">of Target</div>
            </div>
        </div>
        <div class="progress-bar-container">
            <div class="progress-bar success" id="hermesProgressBar" style="width: 0%"></div>
        </div>
    </div>

    <!-- Alpha Summary Card -->
    <div class="progress-card" id="alphaSummary">
        <div class="progress-card-header">
            <h3 class="progress-card-title">Alpha Packing</h3>
            <div class="progress-card-icon">
                <i class="fas fa-boxes-stacked"></i>
            </div>
        </div>
        <div class="progress-stats">
            <div>
                <div class="progress-value" id="alphaActual">0</div>
                <div class="progress-label">Actual</div>
            </div>
            <div>
                <div class="progress-percentage" id="alphaPercentage">0%</div>
                <div class="progress-label">of Target</div>
            </div>
        </div>
        <div class="progress-bar-container">
            <div class="progress-bar danger" id="alphaProgressBar" style="width: 0%"></div>
        </div>
    </div>

    <!-- Overall Summary Card -->
    <div class="progress-card">
        <div class="progress-card-header">
            <h3 class="progress-card-title">Total Production</h3>
            <div class="progress-card-icon">
                <i class="fas fa-chart-line"></i>
            </div>
        </div>
        <div class="progress-stats">
            <div>
                <div class="progress-value" id="totalActual">0</div>
                <div class="progress-label">Total Units</div>
            </div>
            <div>
                <div class="progress-percentage" id="totalPercentage">0%</div>
                <div class="progress-label">Overall Completion</div>
            </div>
        </div>
        <div class="progress-bar-container">
            <div class="progress-bar warning" id="totalProgressBar" style="width: 0%"></div>
        </div>
    </div>

    <!-- Clock Card -->
    <div class="progress-card clock-card" id="clockCard">
        <div class="progress-card-icon">
            <i class="fas fa-clock"></i>
        </div>
        <div id="clockTimeDisplay" class="clock-time">00:00<span id="clockSecondsDisplay" class="clock-seconds">:00</span></div>
        <div id="clockDateDisplay" class="clock-date">Loading date...</div>
    </div>
</div>

<!-- Chart Grid Container -->
<div class="chart-grid">
    <!-- Hermes Packing Chart -->
    {% if hermes_packing_categories %}
        <div class="card"> {# This card is now a grid item #}
            <div class="card-header" style="text-align: center;">
                <h2 class="card-title">{{ hermes_packing_page_title|default:"Hermes Packing Plan VS Actual By Line" }}</h2>
            </div>
            <div class="card-body">
                <div class="legend">
                    <div class="legend-item"><div class="legend-color actual"></div><span class="legend-text">Actual</span></div>
                    <div class="legend-item"><div class="legend-color gap"></div><span class="legend-text">Gap</span></div>
                    <div class="legend-item"><div class="legend-color gap-neg"></div><span class="legend-text">-Gap</span></div>
                    <div class="legend-item"><div class="legend-color unmet"></div><span class="legend-text">Remain Plan</span></div>
                </div>
                <div id="chartHermesPacking" class="chart-container"></div>
                {# Django data for Hermes Packing #}
                {{ hermes_packing_categories|json_script:"hermes_categories_data" }}
                {{ hermes_packing_planned_data|json_script:"hermes_planned_data" }}
                {{ hermes_packing_actual_data|json_script:"hermes_actual_data" }}
                {{ hermes_packing_gap_abs_data|json_script:"hermes_gap_abs_data" }}
                {{ hermes_packing_unmet_data|json_script:"hermes_unmet_data" }}
                {{ hermes_packing_gap_sign_data|json_script:"hermes_gap_sign_data" }}
                {{ hermes_packing_y_axis_max|json_script:"hermes_y_axis_max_data" }}
            </div>
        </div>
    {% else %}
        <div class="card"> {# This card is now a grid item #}
            <div class="card-header" style="text-align: center;"><h2 class="card-title">Hermes Packing Plan VS Actual</h2></div>
            <div class="card-body"><div class="no-data">No data available for Hermes Packing.</div></div>
        </div>
    {% endif %}

    <!-- Alpha Packing Chart -->
    {% if alpha_packing_categories %}
        <div class="card"> {# This card is now a grid item #}
            <div class="card-header" style="text-align: center;">
                <h2 class="card-title">{{ alpha_packing_page_title|default:"Alpha Packing Plan VS Actual By Line" }}</h2>
            </div>
            <div class="card-body">
                 <div class="legend">
                    <div class="legend-item"><div class="legend-color actual"></div><span class="legend-text">Actual</span></div>
                    <div class="legend-item"><div class="legend-color gap"></div><span class="legend-text">Gap</span></div>
                    <div class="legend-item"><div class="legend-color gap-neg"></div><span class="legend-text">-Gap</span></div>
                    <div class="legend-item"><div class="legend-color unmet"></div><span class="legend-text">Remain Plan</span></div>
                </div>
                <div id="chartAlphaPacking" class="chart-container"></div>
                {# Django data for Alpha Packing #}
                {{ alpha_packing_categories|json_script:"alpha_categories_data" }}
                {{ alpha_packing_planned_data|json_script:"alpha_planned_data" }}
                {{ alpha_packing_actual_data|json_script:"alpha_actual_data" }}
                {{ alpha_packing_gap_abs_data|json_script:"alpha_gap_abs_data" }}
                {{ alpha_packing_unmet_data|json_script:"alpha_unmet_data" }}
                {{ alpha_packing_gap_sign_data|json_script:"alpha_gap_sign_data" }}
                {{ alpha_packing_y_axis_max|json_script:"alpha_y_axis_max_data" }}
            </div>
        </div>
    {% else %}
         <div class="card"> {# This card is now a grid item #}
            <div class="card-header" style="text-align: center;"><h2 class="card-title">Alpha Packing Plan VS Actual By Line</h2></div>
            <div class="card-body"><div id="alphaPackingData" class="no-data">No data available for Alpha Packing.</div></div>
        </div>
    {% endif %}
</div> <!-- End of chart-grid -->
{% endblock %}

{% block page_scripts %}
<script>
    // Initialize dashboard
    function initDashboard() {
        // Load Hermes data
        const hermesCategories = getJsonData('hermes_categories_data');
        const hermesPlanned = getJsonData('hermes_planned_data');
        const hermesActual = getJsonData('hermes_actual_data');
        const hermesGapAbs = getJsonData('hermes_gap_abs_data');
        const hermesUnmet = getJsonData('hermes_unmet_data');
        const hermesGapSign = getJsonData('hermes_gap_sign_data');
        const hermesYAxisMax = getJsonData('hermes_y_axis_max_data') || 3000; // Default if not provided

        // Load Alpha data
        const alphaCategories = getJsonData('alpha_categories_data');
        const alphaPlanned = getJsonData('alpha_planned_data');
        const alphaActual = getJsonData('alpha_actual_data');
        const alphaGapAbs = getJsonData('alpha_gap_abs_data');
        const alphaUnmet = getJsonData('alpha_unmet_data');
        const alphaGapSign = getJsonData('alpha_gap_sign_data');
        const alphaYAxisMax = getJsonData('alpha_y_axis_max_data') || 3000; // Default if not provided

        // Update summary cards
        updateSummaryCards(hermesActual, hermesPlanned, alphaActual, alphaPlanned);
        
        // Render Hermes chart if data exists
        if (hermesCategories && hermesCategories.length > 0) {
            renderProductionChart(
                'chartHermesPacking',
                hermesCategories,
                hermesPlanned,
                hermesActual,
                hermesGapAbs,
                hermesUnmet,
                hermesGapSign,
                hermesYAxisMax
            );
        }
        
        // Render Alpha chart if data exists
        if (alphaCategories && alphaCategories.length > 0) {
             // Ensure the div for Alpha chart exists and is a chart container
            const alphaChartDiv = document.getElementById('chartAlphaPacking');
            if (alphaChartDiv) { // Check if the chart div exists (it should if alpha_packing_categories is true)
                renderProductionChart(
                    'chartAlphaPacking',
                    alphaCategories,
                    alphaPlanned,
                    alphaActual,
                    alphaGapAbs,
                    alphaUnmet,
                    alphaGapSign,
                    alphaYAxisMax
                );
            }
        }
    }

    // Update summary cards with data from backend
    function updateSummaryCards(hermesActualData, hermesPlannedData, alphaActualData, alphaPlannedData) {
        const hermesActual = hermesActualData || [];
        const hermesPlanned = hermesPlannedData || [];
        const alphaActual = alphaActualData || [];
        const alphaPlanned = alphaPlannedData || [];

        // Calculate totals for Hermes
        const hermesTotal = hermesActual.reduce((sum, val) => sum + (Number(val) || 0), 0);
        const hermesPlanTotal = hermesPlanned.reduce((sum, val) => sum + (Number(val) || 0), 0);
        const hermesPercentage = hermesPlanTotal > 0 ? Math.round((hermesTotal / hermesPlanTotal) * 100) : 0;
        
        document.getElementById('hermesActual').textContent = hermesTotal.toLocaleString();
        document.getElementById('hermesPercentage').textContent = `${hermesPercentage}%`;
        document.getElementById('hermesPercentage').className = `progress-percentage ${hermesPercentage >= 90 ? 'positive' : 'negative'}`;
        document.getElementById('hermesProgressBar').style.width = `${hermesPercentage}%`;
        document.getElementById('hermesProgressBar').className = `progress-bar ${getProgressBarClass(hermesPercentage)}`;
        
        // Calculate totals for Alpha
        const alphaTotal = alphaActual.reduce((sum, val) => sum + (Number(val) || 0), 0);
        const alphaPlanTotal = alphaPlanned.reduce((sum, val) => sum + (Number(val) || 0), 0);
        const alphaPercentage = alphaPlanTotal > 0 ? Math.round((alphaTotal / alphaPlanTotal) * 100) : 0;

        if (document.getElementById('alphaActual')) { // Check if Alpha summary card elements exist
            document.getElementById('alphaActual').textContent = alphaTotal.toLocaleString();
            document.getElementById('alphaPercentage').textContent = `${alphaPercentage}%`;
            document.getElementById('alphaPercentage').className = `progress-percentage ${alphaPercentage >= 90 ? 'positive' : 'negative'}`;
            document.getElementById('alphaProgressBar').style.width = `${alphaPercentage}%`;
            document.getElementById('alphaProgressBar').className = `progress-bar ${getProgressBarClass(alphaPercentage)}`;
        }
        
        // Calculate and update total production
        const totalActualVal = hermesTotal + alphaTotal;
        const totalPlanVal = hermesPlanTotal + alphaPlanTotal;
        const totalPercentageVal = totalPlanVal > 0 ? Math.round((totalActualVal / totalPlanVal) * 100) : 0;
        
        document.getElementById('totalActual').textContent = totalActualVal.toLocaleString();
        document.getElementById('totalPercentage').textContent = `${totalPercentageVal}%`;
        document.getElementById('totalPercentage').className = `progress-percentage ${totalPercentageVal >= 90 ? 'positive' : 'negative'}`;
        document.getElementById('totalProgressBar').style.width = `${totalPercentageVal}%`;
        document.getElementById('totalProgressBar').className = `progress-bar ${getProgressBarClass(totalPercentageVal)}`;
    }
    
    // Get progress bar class based on percentage
    function getProgressBarClass(percentage) {
        if (percentage >= 90) return 'success';
        if (percentage >= 70) return 'warning';
        return 'danger';
    }

    // Render production chart - now accepts yAxisMax as a parameter
    function renderProductionChart(chartId, categories, plannedData, actualData, gapAbsData, unmetData, gapSignData, yAxisMaxValue) {
        const chartElement = document.getElementById(chartId);
        if (!chartElement) {
            console.error("Chart element not found:", chartId);
            return;
        }
        
        const myChart = echarts.init(chartElement, null, { renderer: 'svg' });
        
        const colors = {
            actual: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                { offset: 0, color: 'var(--green-primary)' },
                { offset: 1, color: 'var(--green-dark)' }
            ]),
            gap_pos: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                { offset: 0, color: 'var(--blue-primary)' },
                { offset: 1, color: 'var(--blue-secondary)' }
            ]),
            gap_neg: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                { offset: 0, color: 'var(--red-primary)' },
                { offset: 1, color: 'var(--red-secondary)' }
            ]),
            unmet: 'var(--gray-light)'
        };
        
        const gapSeriesData = (gapAbsData || []).map((value, index) => {
            return {
                value: value,
                itemStyle: {
                    color: (gapSignData || [])[index] === 'positive' ? colors.gap_pos : colors.gap_neg,
                    shadowBlur: 3,
                    shadowColor: 'rgba(0, 0, 0, 0.2)',
                    shadowOffsetX: 1,
                    shadowOffsetY: 1
                }
            };
        });
        
        const option = {
            backgroundColor: 'transparent',
            tooltip: {
                trigger: 'axis',
                axisPointer: { type: 'shadow' },
                formatter: function (params) {
                    const categoryIndex = params[0].dataIndex;
                    const categoryName = (categories || [])[categoryIndex];
                    const plan = (plannedData || [])[categoryIndex];
                    const actual = (actualData || [])[categoryIndex];
                    const gap = actual - plan;
                    
                    let tooltipText = `<div style="font-weight:bold;font-size:14px;margin-bottom:8px;">${categoryName || 'N/A'}</div>`;
                    tooltipText += `<div style="display:flex;justify-content:space-between;margin:5px 0;"><span>Planned:</span><span style="font-weight:bold;">${plan !== undefined ? plan.toLocaleString() : 'N/A'}</span></div>`;
                    tooltipText += `<div style="display:flex;justify-content:space-between;margin:5px 0;"><span>Actual:</span><span style="font-weight:bold;color:${gap >= 0 ? 'var(--green-primary)' : 'var(--red-primary)'};">${actual !== undefined ? actual.toLocaleString() : 'N/A'}</span></div>`;
                    tooltipText += `<div style="display:flex;justify-content:space-between;margin:5px 0;"><span>Gap:</span><span style="font-weight:bold;color:${gap >= 0 ? 'var(--blue-primary)' : 'var(--red-primary)'};">${gap !== undefined ? gap.toLocaleString() : 'N/A'}</span></div>`;
                    if (yAxisMaxValue > 0 && actual !== undefined) {
                         tooltipText += `<div style="display:flex;justify-content:space-between;margin:5px 0;"><span>Completion:</span><span style="font-weight:bold;">${Math.round((actual/yAxisMaxValue)*100)}%</span></div>`;
                    }
                    
                    return tooltipText;
                },
                backgroundColor: 'var(--bg-card)',
                borderColor: 'var(--border-color)',
                textStyle: { color: 'var(--text-primary)' },
                extraCssText: 'box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15); border-radius: 8px; padding: 12px;'
            },
            grid: { left: '3%', right: '4%', bottom: '3%', top: '10%', containLabel: true },
            xAxis: [{
                type: 'category',
                data: categories || [],
                axisTick: { alignWithLabel: true, lineStyle: { color: 'var(--border-color)' } },
                axisLine: { lineStyle: { color: 'var(--border-color)' } },
                axisLabel: {
                    color: 'var(--text-secondary)',
                    interval: 0, rotate: 30, margin: 12, fontSize: 11,
                    formatter: function(value) {
                        return value && value.length > 15 ? value.substring(0, 15) + '...' : value;
                    }
                }
            }],
            yAxis: [{
                type: 'value',
                name: 'Quantity',
                max: yAxisMaxValue,
                nameTextStyle: { color: 'var(--text-secondary)', fontSize: 12, padding: [0, 0, 0, -5] },
                splitLine: { lineStyle: { color: 'var(--border-color)', type: 'dashed' } },
                axisLine: { show: true, lineStyle: { color: 'var(--border-color)' } },
                axisLabel: { color: 'var(--text-secondary)', fontSize: 11,
                    formatter: function(value) { return value.toLocaleString(); }
                }
            }],
            series: [
                {
                    name: 'Actual',
                    type: 'bar',
                    stack: 'Total',
                    itemStyle: {
                        color: colors.actual,
                        barBorderRadius: [0, 0, 4, 4],
                        shadowBlur: 3,
                        shadowColor: 'rgba(0, 0, 0, 0.2)',
                        shadowOffsetX: 1,
                        shadowOffsetY: 1
                    },
                    label: {
                        show: true,
                        position: 'inside',
                        formatter: '{c}',
                        color: '#ffffff',
                        fontSize: 12,
                        fontWeight: 'bold'
                    },
                    data: actualData,
                    barWidth: '60%',
                    emphasis: { focus: 'none' }
                },
                {
                    name: 'Gap',
                    type: 'bar',
                    stack: 'Total',
                    label: {
                        show: true,
                        position: 'inside',
                        formatter: function(params) {
                            return params.value > 0 ? Math.abs(params.value) : '';
                        },
                        color: '#ffffff',
                        fontSize: 12,
                        fontWeight: 'bold'
                    },
                    data: gapSeriesData,
                    emphasis: { focus: 'none' }
                },
                {
                    name: 'Unmet Target',
                    type: 'bar',
                    stack: 'Total',
                    itemStyle: {
                        color: colors.unmet,
                        barBorderRadius: [4, 4, 0, 0]
                    },
                    label: {
                        show: false
                    },
                    data: unmetData,
                    emphasis: { focus: 'none' }
                }
            ],
            animation: true,
            animationDuration: 1000,
            animationEasing: 'cubicOut'
        };
        
        myChart.setOption(option);
        
        // Responsive chart
        window.addEventListener('resize', function() {
            myChart.resize();
        });
    }

    // Initialize dashboard when DOM is ready
    document.addEventListener('DOMContentLoaded', function() {
        initDashboard();
        updateClock(); // Initial call to display clock immediately
        setInterval(updateClock, 1000); // Update clock every second
    });

    // Function to update the clock
    function updateClock() {
        const now = new Date();
        const hours = String(now.getHours()).padStart(2, '0');
        const minutes = String(now.getMinutes()).padStart(2, '0');
        const seconds = String(now.getSeconds()).padStart(2, '0');
        
        const clockTimeElement = document.getElementById('clockTimeDisplay');
        if (clockTimeElement) {
            clockTimeElement.childNodes[0].nodeValue = `${hours}:${minutes}`; // Update hours and minutes
        }
        const clockSecondsElement = document.getElementById('clockSecondsDisplay');
        if (clockSecondsElement) {
            clockSecondsElement.textContent = `:${seconds}`; // Update seconds
        }

        const options = { weekday: 'long', year: 'numeric', month: 'long', day: 'numeric' };
        const currentDate = now.toLocaleDateString('en-US', options);
        const clockDateElement = document.getElementById('clockDateDisplay');
        if (clockDateElement) {
            clockDateElement.textContent = currentDate;
        }
    }
</script>
{% endblock %}
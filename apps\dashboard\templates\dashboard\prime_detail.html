{% load static %}
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{{ page_title|default:"Prime Charger Detail" }}</title>
    <!-- <link rel="stylesheet" href="https://cdn.datatables.net/1.11.5/css/jquery.dataTables.min.css"> -->
    <script src="{% static 'plugins/datatables/jquery.dataTables.min.js' %}"></script>
    <!-- <link rel="stylesheet" href="https://cdn.datatables.net/fixedheader/3.2.2/css/fixedHeader.dataTables.min.css">
    <link rel="stylesheet" href="https://cdn.datatables.net/fixedcolumns/4.0.2/css/fixedColumns.dataTables.min.css"> -->
    <link rel="stylesheet" type="text/css" href="{% static 'plugins/datatables/fixedHeader.dataTables.min.css' %}">
    <link rel="stylesheet" type="text/css" href="{% static 'plugins/datatables/fixedColumns.dataTables.min.css' %}">
    <script src="{% static 'js/jquery-3.6.1.min.js' %}"></script>
    <script src="{% static 'plugins/datatables/jquery.dataTables.min.js' %}"></script>
    <script src="{% static 'plugins/datatables/dataTables.fixedHeader.min.js' %}"></script>
    <script src="{% static 'plugins/datatables/dataTables.fixedColumns.min.js' %}"></script>
    <script src="{% static 'js/xlsx.full.min.js' %}"></script>
    <style>
        body {
            background-color: #121212;
            color: #e0e0e0;
            font-family: 'Montserrat', sans-serif;
            margin: 0;
            padding: 0;
            background-image:
                radial-gradient(circle at 20% 30%, rgba(41, 98, 255, 0.03) 0%, transparent 50%),
                radial-gradient(circle at 80% 70%, rgba(255, 41, 112, 0.03) 0%, transparent 50%);
        }

        /* 直接覆盖 DataTables 默认样式 */
        .dataTable td {
            background-color: #252525 !important;
            color: #e0e0e0 !important;
        }

        .dataTable tr.odd td {
            background-color: #2a2a2a !important;
        }

        .dataTable tr.even td {
            background-color: #252525 !important;
        }

        header {
            text-align: center;
            padding: 20px 0;
            position: relative;
            overflow: hidden;
            background: linear-gradient(to right, rgba(30, 30, 30, 0.9), rgba(40, 40, 40, 0.9));
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
        }

        h1 {
            color: #ffffff;
            font-weight: 600;
            margin: 0;
            font-size: 28px;
            letter-spacing: 1px;
            position: relative;
            text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
        }

        .container {
            max-width: 95%;
            margin: 20px auto;
            padding: 20px;
            background: linear-gradient(145deg, #1e1e1e, #252525);
            border-radius: 16px;
            box-shadow: 0 0 18px 3px rgba(74, 144, 226, 0.4);
        }

        .back-button {
            position: absolute;
            left: 20px;
            top: 50%;
            transform: translateY(-50%);
            background: linear-gradient(145deg, #4a90e2, #3a7bc9);
            color: white;
            border: none;
            border-radius: 50%;
            width: 40px;
            height: 40px;
            font-size: 18px;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.3);
            transition: all 0.3s ease;
        }

        .back-button:hover {
            transform: translateY(-50%) scale(1.1);
        }

        table.dataTable {
            width: 100% !important;
            color: #e0e0e0;
            background-color: #252525;
            border-collapse: separate;
            border-spacing: 0;
            border-radius: 8px;
            overflow: hidden;
            margin: 20px 0;
        }

        table.dataTable thead th {
            background-color: #333;
            color: #fff;
            border-bottom: 2px solid #4a90e2;
            padding: 12px 10px;
            font-weight: 600;
            text-align: left;
        }

        table.dataTable tbody td {
            padding: 10px;
            border-bottom: 1px solid #333;
            vertical-align: middle;
            color: #e0e0e0;
            background-color: #252525;
        }

        table.dataTable tbody tr:hover {
            background-color: rgba(74, 144, 226, 0.1);
        }

        /* 确保表格中的所有元素都使用深色主题 */
        .dataTables_wrapper {
            color: #e0e0e0;
            background-color: #1e1e1e;
        }

        /* 修复奇偶行颜色 */
        table.dataTable.stripe tbody tr.odd {
            background-color: #2a2a2a !important;
        }

        table.dataTable.stripe tbody tr.even {
            background-color: #252525 !important;
        }

        /* 确保所有单元格都有正确的背景色 */
        table.dataTable tbody td {
            background-color: inherit !important;
        }

        /* 特别处理 QTY 列 */
        table.dataTable tbody td:nth-child(6) {
            background-color: inherit !important;
            font-weight: bold;
        }

        /* 修复排序箭头颜色 */
        table.dataTable thead .sorting:after,
        table.dataTable thead .sorting_asc:after,
        table.dataTable thead .sorting_desc:after {
            color: #4a90e2;
        }

        .dataTables_wrapper .dataTables_length,
        .dataTables_wrapper .dataTables_filter,
        .dataTables_wrapper .dataTables_info,
        .dataTables_wrapper .dataTables_processing,
        .dataTables_wrapper .dataTables_paginate {
            color: #e0e0e0;
            margin: 10px 0;
        }

        .dataTables_wrapper .dataTables_filter input {
            background-color: #333;
            color: #fff;
            border: 1px solid #555;
            border-radius: 4px;
            padding: 5px 10px;
            margin-left: 10px;
        }

        .dataTables_wrapper .dataTables_length select {
            background-color: #333;
            color: #fff;
            border: 1px solid #555;
            border-radius: 4px;
            padding: 5px 10px;
        }

        .dataTables_wrapper .dataTables_paginate .paginate_button {
            color: #e0e0e0 !important;
            border: 1px solid #333;
            border-radius: 4px;
            background: linear-gradient(to bottom, #333 0%, #222 100%);
            margin: 0 2px;
            padding: 6px 12px;
            text-shadow: none;
        }

        .dataTables_wrapper .dataTables_paginate .paginate_button:hover {
            background: linear-gradient(to bottom, #4a90e2 0%, #3a7bc9 100%);
            color: white !important;
            border: 1px solid #3a7bc9;
            box-shadow: 0 0 5px rgba(74, 144, 226, 0.5);
        }

        .dataTables_wrapper .dataTables_paginate .paginate_button.current,
        .dataTables_wrapper .dataTables_paginate .paginate_button.current:hover {
            background: linear-gradient(to bottom, #4a90e2 0%, #3a7bc9 100%);
            color: white !important;
            border: 1px solid #3a7bc9;
            font-weight: bold;
            box-shadow: 0 0 8px rgba(74, 144, 226, 0.7);
        }

        /* 修复禁用的分页按钮 */
        .dataTables_wrapper .dataTables_paginate .paginate_button.disabled,
        .dataTables_wrapper .dataTables_paginate .paginate_button.disabled:hover {
            color: #666 !important;
            background: #222;
            border: 1px solid #333;
            cursor: default;
            box-shadow: none;
        }

        .color-indicator {
            display: inline-block;
            width: 20px;
            height: 20px;
            border-radius: 50%;
            margin-right: 10px;
            vertical-align: middle;
        }

        .summary-info {
            background-color: rgba(74, 144, 226, 0.1);
            border-left: 4px solid #4a90e2;
            padding: 15px;
            margin-bottom: 20px;
            border-radius: 4px;
        }

        @media (max-width: 768px) {
            .container {
                padding: 10px;
            }

            h1 {
                font-size: 22px;
            }
        }
    </style>
</head>
<body>
    <header>
        <button class="back-button" onclick="window.history.back()">←</button>
        <h1>{{ page_title|default:"Prime Charger Detail" }}</h1>
    </header>

    <div class="container">
        <div class="summary-info">
            <h2>{{ chart_title }} - {{ color }}</h2>
            <p>Total Quantity: {{ total_qty }}</p>
        </div>
        <button id="exportExcelBtn" class="btn btn-success mb-3" style="margin-bottom: 20px;">下载 Excel</button>
        <script id="detail-data-all" type="application/json">{{ detail_data|safe }}</script>

        {% if error_message %}
            <div style="background-color: rgba(255, 0, 0, 0.1); border-left: 4px solid #ff5252; padding: 15px; margin: 20px; border-radius: 4px;">
                <p style="color: #ff5252; margin: 0;">{{ error_message }}</p>
            </div>
        {% endif %}

        <table id="detailTable" class="display" style="width:100%">
            <thead>
                <tr>
                    <th>PartFamily</th>
                    <th>PartNumber</th>
                    <th>Color</th>
                    <th>FW Version</th>
                    <th>Battery Type</th>
                    <th>QTY</th>
                    <th>Supplier</th>
                    <th>LC</th>
                </tr>
            </thead>
            <tbody>
                {% for item in detail_data %}
                <tr>
                    <td>{{ item.PartFamily|default:"-" }}</td>
                    <td>{{ item.PartNumber|default:"-" }}</td>
                    <td>
                        <span class="color-indicator" style="background-color: {{ item.color_code|default:'#000000' }}"></span>
                        {{ item.Color|default:"-" }}
                    </td>
                    <td>{{ item.FWVersion|default:"-" }}</td>
                    <td>{{ item.BatteryType|default:"-" }}</td>
                    <td>{{ item.Qty|default:"0" }}</td>
                    <td>{{ item.Supplier|default:"-" }}</td>
                    <td>{{ item.LC|default:"-" }}</td>
                </tr>
                {% endfor %}
            </tbody>
        </table>
    </div>

    <script>
        $(document).ready(function() {
            $('#detailTable').DataTable({
                dom: '<"top"lf>rt<"bottom"ip><"clear">',
                language: {
                    search: "搜索：",
                    zeroRecords: "没有找到匹配的记录",
                    info: "显示第 _START_ 至 _END_ 项结果，共 _TOTAL_ 项",
                    infoEmpty: "显示第 0 至 0 项结果，共 0 项",
                    infoFiltered: "(由 _MAX_ 项结果过滤)",
                    lengthMenu: "显示 _MENU_ 项结果",
                    paginate: {
                        first: "首页",
                        previous: "上页",
                        next: "下页",
                        last: "末页"
                    }
                },
                paging: false,
                ordering: true,
                order: [[5, 'desc']],
                scrollY: '60vh',
                scrollX: true,
                scrollCollapse: true,
                fixedHeader: true,
                stripeClasses: ['odd', 'even'],
                rowCallback: function(row, data, index) {
                    $(row).css('background-color', index % 2 === 0 ? '#252525' : '#2a2a2a');
                    $('td', row).css({
                        'color': '#e0e0e0',
                        'background-color': index % 2 === 0 ? '#252525' : '#2a2a2a'
                    });
                    $('td:eq(5)', row).css({
                        'background-color': index % 2 === 0 ? '#252525' : '#2a2a2a',
                        'color': '#e0e0e0',
                        'font-weight': 'bold'
                    });
                }
            });

            $('.dataTables_length, .dataTables_filter, .dataTables_info, .dataTables_paginate').css({
                'color': '#e0e0e0',
                'background-color': 'transparent'
            });

            $('.dataTables_filter input, .dataTables_length select').css({
                'background-color': '#333',
                'color': '#fff',
                'border': '1px solid #555',
                'border-radius': '4px',
                'padding': '5px 10px'
            });

            setTimeout(function() {
                $('#detailTable td').css({
                    'background-color': 'inherit',
                    'color': '#e0e0e0'
                }).attr('style', function(i, style) {
                    return style + 'background-color: inherit !important;';
                });
            }, 100);

            setTimeout(function() {
                $('#detailTable td:nth-child(6)').css({
                    'background-color': 'inherit',
                    'color': '#e0e0e0',
                    'font-weight': 'bold'
                }).attr('style', function(i, style) {
                    return style + 'background-color: inherit !important;';
                });
            }, 200);
        });
        document.getElementById('exportExcelBtn').addEventListener('click', function() {
            var table = document.getElementById('detailTable');
            var wb = XLSX.utils.table_to_book(table, {sheet: 'Sheet1'});
            var pageTitle = document.title || 'PrimeDetail';
            var now = new Date();
            var ts = now.getFullYear() + ('0'+(now.getMonth()+1)).slice(-2) + ('0'+now.getDate()).slice(-2) + '_' + ('0'+now.getHours()).slice(-2) + ('0'+now.getMinutes()).slice(-2) + ('0'+now.getSeconds()).slice(-2);
            var filename = pageTitle.replace(/\s+/g, '_') + '_' + ts + '.xlsx';
            XLSX.writeFile(wb, filename);
        });
    </script>
</body>
</html>

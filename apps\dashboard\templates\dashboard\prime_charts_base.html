{% load static %}
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta http-equiv="refresh" content="600"> {# 10 分钟自动刷新 #}
    <title>{{ page_title|default:"Prime Supermarket Inventory Dashboard" }}</title>
    <script src="{% static 'js/echarts.min.js' %}"></script>
    <link rel="stylesheet" type="text/css" href="{% static 'css/fonts.css' %}">
    <style>
        /* @import url('https://fonts.googleapis.com/css2?family=Montserrat:wght@300;400;500;600;700&display=swap'); */ /* Removed Google Fonts import */

        body {
            background-color: #121212;
            color: #e0e0e0;
            font-family: 'Montserrat', sans-serif;
            margin: 0;
            padding: 0;
            background-image:
                radial-gradient(circle at 20% 30%, rgba(41, 98, 255, 0.03) 0%, transparent 50%),
                radial-gradient(circle at 80% 70%, rgba(255, 41, 112, 0.03) 0%, transparent 50%);
            overflow-x: hidden;
        }

        header {
            text-align: center;
            padding: 30px 0;
            position: relative;
            overflow: hidden;
            background: linear-gradient(to right, rgba(30, 30, 30, 0.9), rgba(40, 40, 40, 0.9));
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
        }

        h1 {
            color: #ffffff;
            font-weight: 600;
            margin: 0;
            font-size: 36px;
            letter-spacing: 1px;
            position: relative;
            text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
        }

        /* h1:after {
            content: '';
            position: absolute;
            bottom: -10px;
            left: 50%;
            transform: translateX(-50%);
            width: 80px;
            height: 3px;
            background: linear-gradient(to right, #4a90e2, #63b3ed);
            border-radius: 3px;
        } */

        .charts-grid {
            display: flex;
            flex-wrap: wrap;
            justify-content: space-around;
            padding: 30px 10px; /* 减小左右内边距 */
            max-width: 96%;    /* 使用百分比宽度，并保留少量边缘 */
            margin: 0 auto;
        }

        .chart-container {
            width: 48%;
            min-width: 300px;
            height: 420px; /* 减小图表容器高度 */
            margin-bottom: 25px; /* 减小图表容器下外边距 */
            border-radius: 16px;
            box-sizing: border-box;
            position: relative;
            transition: all 0.3s ease;
            overflow: hidden;
            background: linear-gradient(145deg, #1e1e1e, #252525);
            border: 2px solid transparent; /* 透明边框，用于显示动画效果 */
            box-shadow:
                0 0 18px 3px rgba(74, 144, 226, 0.4), /* 外部蓝色辉光 */
                inset 0 0 10px rgba(74, 144, 226, 0.2); /* 内部蓝色辉光 */
        }

        /* 添加边框动画效果 */
        .chart-container::before {
            content: '';
            position: absolute;
            top: -2px;
            left: -2px;
            right: -2px;
            bottom: -2px;
            border: 2px solid #4a90e2;
            border-radius: 16px;
            animation: borderLight 4s linear infinite;
            z-index: 2;
            pointer-events: none;
            filter: drop-shadow(0 0 2px rgba(74, 144, 226, 0.8));
        }

        @keyframes borderLight {
            0% {
                clip-path: inset(0 0 calc(100% - 3px) 0);
                border-color: #4a90e2;
                box-shadow: 0 0 10px #4a90e2, 0 0 20px rgba(74, 144, 226, 0.5);
            }
            25% {
                clip-path: inset(0 0 0 calc(100% - 3px));
                border-color: #63b3ed;
                box-shadow: 0 0 10px #63b3ed, 0 0 20px rgba(99, 179, 237, 0.5);
            }
            50% {
                clip-path: inset(calc(100% - 3px) 0 0 0);
                border-color: #4a90e2;
                box-shadow: 0 0 10px #4a90e2, 0 0 20px rgba(74, 144, 226, 0.5);
            }
            75% {
                clip-path: inset(0 calc(100% - 3px) 0 0);
                border-color: #63b3ed;
                box-shadow: 0 0 10px #63b3ed, 0 0 20px rgba(99, 179, 237, 0.5);
            }
            100% {
                clip-path: inset(0 0 calc(100% - 3px) 0);
                border-color: #4a90e2;
                box-shadow: 0 0 10px #4a90e2, 0 0 20px rgba(74, 144, 226, 0.5);
            }
        }

        .chart-container:hover {
            transform: translateY(-5px);
            box-shadow: 0 15px 30px rgba(0, 0, 0, 0.3),
                        0 1px 5px rgba(255, 255, 255, 0.08) inset;
        }

        /* .chart-container:before {  // 注释掉原有的顶部线条
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 3px;
            background: linear-gradient(to right, #4a90e2, #63b3ed);
            opacity: 0.8;
            z-index: 5;
        } */

        /* 定位并修改图表头部、标题和控制按钮的样式 */


        .loading {
            display: flex;
            align-items: center;
            justify-content: center;
            height: 100%;
            width: 100%;
            position: absolute;
            top: 0;
            left: 0;
            background-color: rgba(30, 30, 30, 0.9);
            z-index: 10;
            transition: opacity 0.5s ease, visibility 0.5s ease;
        }

        .loading.hidden {
            opacity: 0;
            visibility: hidden;
        }

        .spinner {
            width: 50px;
            height: 50px;
            border: 3px solid rgba(255, 255, 255, 0.1);
            border-top-color: #4a90e2;
            border-radius: 50%;
            animation: spin 1s ease-in-out infinite;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .chart-header {
            display: flex;
            justify-content: center; /* 修改为居中对齐标题 */
            align-items: center;
            padding: 10px 20px; /* 减小头部垂直内边距 */
            border-bottom: 1px solid rgba(255, 255, 255, 0.05);
            position: relative;
        }

        .chart-title {
            font-size: 28px; /* 增大字体 */
            font-weight: 600; /* 加粗 */
            color: #e0e0e0;
        }

        .chart-controls {
            position: absolute; /* 保持绝对定位 */
            right: 15px;
            top: 50%;
            transform: translateY(-50%);
            display: flex;
            gap: 10px;
        }

        .chart-control {
            background: none;
            border: none;
            color: #777;
            cursor: pointer;
            font-size: 20px;
            padding: 3px 5px;
            transition: color 0.2s;
        }

        .chart-control:hover {
            color: #4a90e2;
        }

        .chart-area {
            height: calc(100% - 51px); /* Approx height of chart-header */
        }

        .no-data {
            text-align: center;
            padding-top: 40%;
            color: #777;
            font-style: italic;
        }

        .refresh-button {
            position: fixed;
            right: 30px;
            bottom: 30px;
            width: 60px;
            height: 60px;
            border-radius: 50%;
            background: linear-gradient(145deg, #4a90e2, #3a7bc9);
            box-shadow: 0 4px 15px rgba(74, 144, 226, 0.4);
            border: none;
            color: white;
            font-size: 24px;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            transition: all 0.3s ease;
            z-index: 100;
        }

        .refresh-button:hover {
            transform: rotate(30deg);
            background: linear-gradient(145deg, #5a9aea, #4a90e2);
        }

        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(20px); }
            to { opacity: 1; transform: translateY(0); }
        }

        .chart-container {
            animation: fadeIn 0.6s ease forwards;
            opacity: 0;
        }

        .chart-container:nth-child(1) { animation-delay: 0.1s; }
        .chart-container:nth-child(2) { animation-delay: 0.2s; }
        .chart-container:nth-child(3) { animation-delay: 0.3s; }
        .chart-container:nth-child(4) { animation-delay: 0.4s; }

        @keyframes pulse {
            0% { transform: scale(1); }
            50% { transform: scale(1.05); }
            100% { transform: scale(1); }
        }

        .pulse {
            animation: pulse 1.5s ease infinite;
        }

        @media (max-width: 768px) {
            .chart-container {
                width: 100%;
            }

            h1 {
                font-size: 22px;
            }
        }
    </style>
</head>
<body>
    <header>
        <h1>{{ page_title|default:"Prime Supermarket Inventory Dashboard" }}</h1>
    </header>

    {% if error_message %}
        <div style="background-color: rgba(255, 0, 0, 0.1); border-left: 4px solid #ff5252; padding: 15px; margin: 20px; border-radius: 4px;">
            <p style="color: #ff5252; margin: 0;">{{ error_message }}</p>
        </div>
    {% endif %}

    {{ charger_waiting_laser_data|json_script:"charger-waiting-laser-data" }}
    {{ charger_after_laser_data|json_script:"charger-after-laser-data" }}
    {{ holder_waiting_laser_data|json_script:"holder-waiting-laser-data" }}
    {{ holder_after_laser_data|json_script:"holder-after-laser-data" }}

    <div class="charts-grid">
        <div class="chart-container">
            <div class="loading">
                <div class="spinner"></div>
            </div>
            <div class="chart-header">
                <div class="chart-title">Charger Waiting Laser</div>
                <div class="chart-controls">
                    <button class="chart-control toggle-view" title="切换视图">⊙</button>
                    <button class="chart-control reset-zoom" title="重置缩放">↺</button>
                </div>
            </div>
            <div id="chargerWaitingLaserChart" class="chart-area"></div>
        </div>

        <div class="chart-container">
            <div class="loading">
                <div class="spinner"></div>
            </div>
            <div class="chart-header">
                <div class="chart-title">Charger After Laser</div>
                <div class="chart-controls">
                    <button class="chart-control toggle-view" title="切换视图">⊙</button>
                    <button class="chart-control reset-zoom" title="重置缩放">↺</button>
                </div>
            </div>
            <div id="chargerAfterLaserChart" class="chart-area"></div>
        </div>

        <div class="chart-container">
            <div class="loading">
                <div class="spinner"></div>
            </div>
            <div class="chart-header">
                <div class="chart-title">Holder Waiting Laser</div>
                <div class="chart-controls">
                    <button class="chart-control toggle-view" title="切换视图">⊙</button>
                    <button class="chart-control reset-zoom" title="重置缩放">↺</button>
                </div>
            </div>
            <div id="holderWaitingLaserChart" class="chart-area"></div>
        </div>

        <div class="chart-container">
            <div class="loading">
                <div class="spinner"></div>
            </div>
            <div class="chart-header">
                <div class="chart-title">Holder After Laser</div>
                <div class="chart-controls">
                    <button class="chart-control toggle-view" title="切换视图">⊙</button>
                    <button class="chart-control reset-zoom" title="重置缩放">↺</button>
                </div>
            </div>
            <div id="holderAfterLaserChart" class="chart-area"></div>
        </div>
    </div>

    <!-- <button class="refresh-button" title="刷新数据">↻</button> -->

    <script type="text/javascript">
        document.addEventListener('DOMContentLoaded', function () {
            // 加载动画
            setTimeout(() => {
                document.querySelectorAll('.loading').forEach(loader => {
                    loader.classList.add('hidden');
                });
            }, 1000);

            // 数据获取
            const chargerWaitingLaserDataElement = document.getElementById('charger-waiting-laser-data');
            const chargerWaitingLaserData = chargerWaitingLaserDataElement ? JSON.parse(chargerWaitingLaserDataElement.textContent) : [];

            const chargerAfterLaserDataElement = document.getElementById('charger-after-laser-data');
            const chargerAfterLaserData = chargerAfterLaserDataElement ? JSON.parse(chargerAfterLaserDataElement.textContent) : [];

            const holderWaitingLaserDataElement = document.getElementById('holder-waiting-laser-data');
            const holderWaitingLaserData = holderWaitingLaserDataElement ? JSON.parse(holderWaitingLaserDataElement.textContent) : [];

            const holderAfterLaserDataElement = document.getElementById('holder-after-laser-data');
            const holderAfterLaserData = holderAfterLaserDataElement ? JSON.parse(holderAfterLaserDataElement.textContent) : [];

            let chargerWaitingLaserChart, chargerAfterLaserChart, holderWaitingLaserChart, holderAfterLaserChart;
            let animationIntervals = {};

            const colorMap = {
                'Breeze Blue': { normal: '#87CEEB', active: '#A7EEEB', gradient: new echarts.graphic.LinearGradient(0, 0, 0, 1, [{offset: 0, color: '#87CEEB'}, {offset: 1, color: '#5EAEBB'}]) },
                'Digital Violet': { normal: '#8A2BE2', active: '#9A4BF2', gradient: new echarts.graphic.LinearGradient(0, 0, 0, 1, [{offset: 0, color: '#9A4BF2'}, {offset: 1, color: '#7A1BD2'}]) },
                'Leaf Green': { normal: '#90EE90', active: '#A0FFA0', gradient: new echarts.graphic.LinearGradient(0, 0, 0, 1, [{offset: 0, color: '#90EE90'}, {offset: 1, color: '#60CE60'}]) },
                'Midnight Black': { normal: '#777777', active: '#999999', gradient: new echarts.graphic.LinearGradient(0, 0, 0, 1, [{offset: 0, color: '#999999'}, {offset: 1, color: '#555555'}]) },
                'Vivid Terracotta': { normal: '#E2725B', active: '#F2927B', gradient: new echarts.graphic.LinearGradient(0, 0, 0, 1, [{offset: 0, color: '#E2725B'}, {offset: 1, color: '#C2523B'}]) },
                'Aspen Green': { normal: '#A2D1A0', active: '#B2E1B0', gradient: new echarts.graphic.LinearGradient(0, 0, 0, 1, [{offset: 0, color: '#A2D1A0'}, {offset: 1, color: '#82B180'}]) },
                'Garnet Red': { normal: '#981E32', active: '#A82E42', gradient: new echarts.graphic.LinearGradient(0, 0, 0, 1, [{offset: 0, color: '#981E32'}, {offset: 1, color: '#780E12'}]) }
            };

            function applyCustomColors(data, isBar = false) {
                return data.map(item => {
                    const colorDetails = colorMap[item.name] || {
                        normal: '#5470c6', active: '#7490e6',
                        gradient: new echarts.graphic.LinearGradient(0, 0, 0, 1, [{offset: 0, color: '#5470c6'}, {offset: 1, color: '#3450a6'}])
                    };
                    return {
                        ...item,
                        itemStyle: {
                            color: isBar ? colorDetails.gradient : colorDetails.normal,
                            borderRadius: isBar ? [4, 4, 0, 0] : 0,
                        },
                        emphasis: {
                            itemStyle: {
                                shadowBlur: 20,
                                shadowColor: 'rgba(255, 255, 255, 0.7)',
                                borderColor: colorDetails.active,
                                borderWidth: 2
                            },
                            label: {
                                show: true,
                                fontSize: 22,
                                fontWeight: 'bold',
                                color: '#FFFFFF',
                                textBorderColor: '#000000',
                                textBorderWidth: 2,
                            }
                        }
                    };
                });
            }

            function createPieOption(titleText, data, chartType = 'pie', chartDomId = '') {
                const total = data.reduce((sum, item) => sum + item.value, 0);
                let seriesConfig;

                if (chartType === 'pie') {
                    seriesConfig = {
                        type: 'pie',
                        radius: ['45%', '70%'],
                        center: ['50%', '55%'],
                        avoidLabelOverlap: true,
                        itemStyle: {
                            borderColor: '#1e1e1e',
                            borderWidth: 2
                        },
                        label: {
                            show: true,
                            formatter: '{b}: {c}\n({d}%)',
                            color: '#e0e0e0',
                            fontSize: 18,
                            fontWeight: '500',
                            textBorderColor: '#000',
                            textBorderWidth: 2,
                            textShadowColor: 'rgba(0, 0, 0, 0.5)',
                            textShadowBlur: 3
                        },
                        labelLine: {
                            lineStyle: { color: '#aaa', width: 1.5 },
                            smooth: 0.2, length: 20, length2: 30
                        },
                        emphasis: { itemStyle: { shadowBlur: 10, shadowOffsetX: 0, shadowColor: 'rgba(0, 0, 0, 0.5)' } },
                        animationType: 'scale',
                        animationEasing: 'elasticOut',
                        animationDelay: idx => Math.random() * 200,
                        universalTransition: true,
                        data: applyCustomColors(data)
                    };
                } else {
                    seriesConfig = {
                        type: 'bar',
                        barWidth: '60%',
                        backgroundStyle: { color: 'rgba(180, 180, 180, 0.05)' },
                        label: { show: true, position: 'top', formatter: '{c}', color: '#ccc', fontSize: 16 },
                        emphasis: {
                            itemStyle: {
                                shadowBlur: 10,
                                shadowColor: 'rgba(255,255,255,0.3)'
                            }
                        },
                        animationDuration: 1000,
                        animationEasing: 'elasticOut',
                        animationDelay: idx => idx * 100,
                        universalTransition: true,
                        data: applyCustomColors(data, true)
                    };
                }

                // 计算chart_type参数
                let chartTypeParam = '';
                switch(chartDomId) {
                    case 'chargerWaitingLaserChart': chartTypeParam = 'charger_waiting'; break;
                    case 'chargerAfterLaserChart': chartTypeParam = 'charger_after'; break;
                    case 'holderWaitingLaserChart': chartTypeParam = 'holder_waiting'; break;
                    case 'holderAfterLaserChart': chartTypeParam = 'holder_after'; break;
                    default: chartTypeParam = '';
                }

                return {
                    backgroundColor: 'transparent',
                    title: [
                        {
                            text: '',
                            left: 'center',
                            top: '5%',
                            textStyle: { color: '#ccc', fontSize: 16, fontWeight: 'normal' }
                        },
                        {
                            text: total.toString(),
                            left: '50%',
                            top: chartType === 'pie' ? '53%' : '88%',
                            textAlign: 'center',
                            textStyle: { fontSize: 22, fontWeight: 'bold', color: '#fff', textBorderColor: '#000', textBorderWidth: 3, textShadowBlur: 2 },
                            link: chartTypeParam ? `/dashboard/prime-detail/?color=all&chart_type=${chartTypeParam}` : undefined,
                            target: chartTypeParam ? '_blank' : undefined
                        }
                    ],
                    tooltip: {
                        trigger: chartType === 'pie' ? 'item' : 'axis',
                        formatter: chartType === 'pie' ? '{a} <br/>{b} : {c} ({d}%)' : '{b} : {c}',
                        backgroundColor: 'rgba(50, 50, 50, 0.9)', borderColor: '#555', borderWidth: 1,
                        textStyle: { color: '#eee', fontSize: 14 },
                        extraCssText: 'box-shadow: 0 4px 8px rgba(0, 0, 0, 0.3);'
                    },
                    legend: {
                        show: false,
                        orient: 'vertical', left: '5%', top: '15%',
                        data: data.map(item => item.name),
                        textStyle: { color: '#e0e0e0', fontSize: 18 },
                        itemGap: 15, itemWidth: 20, itemHeight: 14, icon: 'roundRect',
                        formatter: name => {
                            const item = data.find(d => d.name === name);
                            if (!item) return name;
                            const percent = total === 0 ? 0 : ((item.value / total) * 100).toFixed(1);
                            return `${name} (${percent}%)`;
                        }
                    },
                    grid: chartType === 'bar' ? { left: '12%', right: '5%', top: '20%', bottom: '15%', containLabel: true } : undefined,
                    xAxis: chartType === 'bar' ? { type: 'category', data: data.map(item => item.name), axisLine: { lineStyle: { color: '#555' } }, axisTick: { alignWithLabel: true, lineStyle: { color: '#555' } }, axisLabel: { color: '#ccc', rotate: 30, fontSize: 12 } } : undefined,
                    yAxis: chartType === 'bar' ? { type: 'value', axisLine: { show: false }, axisTick: { show: false }, splitLine: { lineStyle: { color: '#333', type: 'dashed' } }, axisLabel: { color: '#ccc', fontSize: 12 } } : undefined,
                    series: [seriesConfig]
                };
            }

            // 移除了饼图的跑马灯效果，改为使用边框动画
            function startHighlightAnimation(chart, chartId, data) {
                // 清除任何现有的动画间隔
                if (animationIntervals[chartId]) {
                    clearInterval(animationIntervals[chartId]);
                    delete animationIntervals[chartId];
                }
                // 不再需要饼图段落的高亮动画
            }

            function initChartInstance(chartVarName, chartDomId, data, title) {
                let chartInstance = null;
                if (document.getElementById(chartDomId)) {
                    const chartDom = document.getElementById(chartDomId);
                    if (data && data.length > 0) {
                        chartInstance = echarts.init(chartDom, 'dark');
                        chartInstance._chartType = 'pie';
                        chartInstance.setOption(createPieOption(title, data, chartInstance._chartType, chartDomId));
                        startHighlightAnimation(chartInstance, chartDomId, data);

                        // 添加点击事件处理
                        chartInstance.on('click', function(params) {
                            // 只处理饼图的点击事件
                            if (chartInstance._chartType === 'pie') {
                                const color = params.name;
                                let chartType = '';

                                // 确定图表类型
                                switch(chartDomId) {
                                    case 'chargerWaitingLaserChart': chartType = 'charger_waiting'; break;
                                    case 'chargerAfterLaserChart': chartType = 'charger_after'; break;
                                    case 'holderWaitingLaserChart': chartType = 'holder_waiting'; break;
                                    case 'holderAfterLaserChart': chartType = 'holder_after'; break;
                                }

                                // 构建详情页URL
                                const detailUrl = `/dashboard/prime-detail/?color=${encodeURIComponent(color)}&chart_type=${chartType}`;

                                // 在新窗口中打开详情页
                                window.open(detailUrl, '_blank');
                            }
                        });
                    } else if (chartDom) {
                        chartDom.innerHTML = `<div class="no-data">${title} 无数据</div>`;
                    }
                }
                return chartInstance;
            }

            function initCharts() {
                chargerWaitingLaserChart = initChartInstance('chargerWaitingLaserChart', 'chargerWaitingLaserChart', chargerWaitingLaserData, 'Charger WaitingLaser');
                chargerAfterLaserChart = initChartInstance('chargerAfterLaserChart', 'chargerAfterLaserChart', chargerAfterLaserData, 'Charger AfterLaser');
                holderWaitingLaserChart = initChartInstance('holderWaitingLaserChart', 'holderWaitingLaserChart', holderWaitingLaserData, 'Holder WaitingLaser');
                holderAfterLaserChart = initChartInstance('holderAfterLaserChart', 'holderAfterLaserChart', holderAfterLaserData, 'Holder AfterLaser');
            }

            initCharts();

            document.querySelectorAll('.toggle-view').forEach(button => {
                button.addEventListener('click', function() {
                    const container = this.closest('.chart-container');
                    const chartId = container.querySelector('.chart-area').id;
                    let chartInstance, chartData, chartTitleText;

                    const chartTitleDiv = container.querySelector('.chart-title');
                    chartTitleText = chartTitleDiv ? chartTitleDiv.textContent : 'Chart';

                    switch(chartId) {
                        case 'chargerWaitingLaserChart': chartInstance = chargerWaitingLaserChart; chartData = chargerWaitingLaserData; break;
                        case 'chargerAfterLaserChart': chartInstance = chargerAfterLaserChart; chartData = chargerAfterLaserData; break;
                        case 'holderWaitingLaserChart': chartInstance = holderWaitingLaserChart; chartData = holderWaitingLaserData; break;
                        case 'holderAfterLaserChart': chartInstance = holderAfterLaserChart; chartData = holderAfterLaserData; break;
                    }

                    if (!chartInstance || !chartData || chartData.length === 0) return;

                    const newType = chartInstance._chartType === 'pie' ? 'bar' : 'pie';
                    chartInstance._chartType = newType;

                    // 清除任何现有的动画间隔（已不再需要）
                    if (animationIntervals[chartId]) {
                        clearInterval(animationIntervals[chartId]);
                        delete animationIntervals[chartId];
                    }

                    chartInstance.setOption(createPieOption(chartTitleText, chartData, newType, chartId), true);

                    this.textContent = newType === 'pie' ? '⊙' : '▭';
                    container.classList.add('pulse');
                    setTimeout(() => container.classList.remove('pulse'), 1500);
                });
            });

            document.querySelectorAll('.reset-zoom').forEach(button => {
                button.addEventListener('click', function() {
                    const container = this.closest('.chart-container');
                    const chartId = container.querySelector('.chart-area').id;
                    let chartInstance;
                    switch(chartId) {
                        case 'chargerWaitingLaserChart': chartInstance = chargerWaitingLaserChart; break;
                        case 'chargerAfterLaserChart': chartInstance = chargerAfterLaserChart; break;
                        case 'holderWaitingLaserChart': chartInstance = holderWaitingLaserChart; break;
                        case 'holderAfterLaserChart': chartInstance = holderAfterLaserChart; break;
                    }
                    if (!chartInstance) return;
                    chartInstance.dispatchAction({ type: 'restore' });
                    container.classList.add('pulse');
                    setTimeout(() => container.classList.remove('pulse'), 1500);
                });
            });

            document.querySelector('.refresh-button').addEventListener('click', function() {
                document.querySelectorAll('.loading').forEach(loader => loader.classList.remove('hidden'));
                this.style.transition = 'transform 0.5s ease';
                this.style.transform = 'rotate(360deg)';

                setTimeout(() => {
                    this.style.transform = 'rotate(0deg)';
                    // 清除所有动画间隔
                    Object.values(animationIntervals).forEach(clearInterval);
                    animationIntervals = {};

                    [chargerWaitingLaserChart, chargerAfterLaserChart, holderWaitingLaserChart, holderAfterLaserChart].forEach(chart => {
                        if (chart && !chart.isDisposed()) chart.dispose();
                    });

                    setTimeout(() => {
                        initCharts();
                        document.querySelectorAll('.loading').forEach(loader => loader.classList.add('hidden'));
                    }, 300);
                }, 1000);
            });

            window.addEventListener('resize', function(){
                if (chargerWaitingLaserChart && !chargerWaitingLaserChart.isDisposed()) chargerWaitingLaserChart.resize();
                if (chargerAfterLaserChart && !chargerAfterLaserChart.isDisposed()) chargerAfterLaserChart.resize();
                if (holderWaitingLaserChart && !holderWaitingLaserChart.isDisposed()) holderWaitingLaserChart.resize();
                if (holderAfterLaserChart && !holderAfterLaserChart.isDisposed()) holderAfterLaserChart.resize();
            });

            window.addEventListener('beforeunload', () => {
                // 清除所有动画间隔
                Object.values(animationIntervals).forEach(clearInterval);
            });
        });
    </script>
</body>
</html>
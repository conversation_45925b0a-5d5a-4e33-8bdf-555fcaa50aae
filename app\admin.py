from django.contrib import admin

# Register your models here.

admin.site.site_header = 'B15_MESTools'  # 设置header
admin.site.site_title = 'B15_MESTools'   # 设置title
admin.site.index_title = 'B15_MESTools'
 
from .models import Task



@admin.register(Task)
class TakeAdmin(admin.ModelAdmin):
    list_display = ('title','description', 'status','created_at','updated_at')
    list_filter = ('status',)
    search_fields = ('title',)
    ordering = ('-id',)


# @admin.register(Article)
# class ArticleAdmin(admin.ModelAdmin):
#     list_display = ('title', 'author', 'created_time')
#     list_filter = ('author',)
#     search_fields = ('title',)
#     empty_value_display = '-empty-'



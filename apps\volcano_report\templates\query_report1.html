{% extends 'base1.html' %}
{% load static %}

{% block content %}
<div class="bg-light text-dark py-3 mb-4 shadow-sm">
    <div class="container">
        <h3 class="display-6">ID: {{ id }} - {{ Reason }}</h3>
    </div>
</div>

<div class="container">
    <nav aria-label="breadcrumb">
        <ol class="breadcrumb">
            <li class="breadcrumb-item"><a href="/volcanoreport/query/">返回首页</a></li>
            <li class="breadcrumb-item active" aria-current="page">ID: {{ id }} - {{ Reason }}</li>
        </ol>
    </nav>

    <form method="post" id="queryForm" class="mb-4">
        {% csrf_token %}
        {{ form.as_p }}
        <button type="submit" name="action" value="submit" class="btn btn-primary">Submit Query</button>
        <button type="submit" name="action" value="export" class="btn btn-success">Export to Excel</button>
    </form>

    <div id="loadingSpinner" class="spinner-border text-primary" role="status" style="display: none;">
        <span class="sr-only">Loading...</span>
    </div>

    {% if form.errors %}
    <div class="alert alert-danger" role="alert">
        <ul>
        {% for field, errors in form.errors.items %}
            {% for error in errors %}
                <li>{{ error }}</li>
            {% endfor %}
        {% endfor %}
        </ul>
    </div>
    {% endif %}
<div class="container ">

    {% if query_results %}
    <h4>查询结果:</h4>
    <div class="table-responsive">
        <h6>{{ query_counts }} 条记录，此页面最多显示前100条，要全部数据，请导出为Excel。</h6>
        <table class="table table-striped table-bordered table-hover">
            <thead class="thead-dark">
                <tr>
                    {% for column in columns %}
                    <th>{{ column }}</th>
                    {% endfor %}
                </tr>
            </thead>
            <tbody>
                {% for row in query_results %}
                <tr>
                    {% for cell in row %}
                    <td>{{ cell }}</td>
                    {% endfor %}
                </tr>
                {% endfor %}
            </tbody>
        </table>
    </div>
    {% endif %}
</div>
</div>
<style>
    .bg-light {
        background-color: #f8f9fa !important;
        color: #343a40;
    }

    .breadcrumb {
        background-color: #f8f9fa;
        padding: 10px 20px;
        border-radius: 0.375rem;
        margin-bottom: 1rem;
    }

    .breadcrumb a {
        text-decoration: none;
        color: #007bff;
    }

    .breadcrumb a:hover {
        text-decoration: underline;
        color: #0056b3;
    }

    .container {
        background-color: #ffffff;
        padding: 20px;
        border-radius: 0.375rem;
        box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
    }

    .table-responsive {
        overflow-x: auto;
    }

    table {
        table-layout: auto;
        white-space: nowrap;
    }

    th, td {
        padding: 10px;
        text-align: left;
        vertical-align: top;
    }

    .btn-primary {
        background-color: #007bff;
        border-color: #007bff;
    }

    .btn-primary:hover {
        background-color: #0056b3;
        border-color: #0056b3;
    }

    .btn-success {
        background-color: #28a745;
        border-color: #28a745;
    }

    .btn-success:hover {
        background-color: #218838;
        border-color: #218838;
    }
</style>

{% endblock %}

{% block extra_js %}
<script>
    document.addEventListener("DOMContentLoaded", function() {
        const form = document.getElementById("queryForm");
        const spinner = document.getElementById("loadingSpinner");

        form.addEventListener("submit", function(event) {
            spinner.style.display = "block";

            // Check if the action is export and prevent default form submission
            const action = event.submitter.getAttribute('value');
            if (action === 'export') {
                event.preventDefault();
                const formData = new FormData(form);

                fetch(window.location.href, {
                    method: 'POST',
                    body: formData,
                    headers: {
                        'X-CSRFToken': form.querySelector('[name="csrfmiddlewaretoken"]').value
                    }
                })
                .then(response => {
                    if (response.ok) {
                        return response.blob();
                    } else {
                        throw new Error('Network response was not ok.');
                    }
                })
                .then(blob => {
                    const url = window.URL.createObjectURL(blob);
                    const a = document.createElement('a');
                    a.style.display = 'none';
                    a.href = url;
                    a.download = 'export.xlsx';
                    document.body.appendChild(a);
                    a.click();
                    window.URL.revokeObjectURL(url);
                })
                .catch(error => {
                    console.error('There has been a problem with your fetch operation:', error);
                })
                .finally(() => {
                    spinner.style.display = "none";
                });
            }
        });

        window.addEventListener('load', function() {
            spinner.style.display = "none";
        });
    });
</script>
{% endblock %}

from django.shortcuts import render
from django.db import connections
from django.shortcuts import get_object_or_404, render, HttpResponse, redirect
from django.http import HttpResponseRedirect, JsonResponse
from django.contrib import messages
from django.contrib.admin.views.decorators import staff_member_required
from django.contrib.auth.decorators import login_required
from django.views.decorators.http import require_http_methods
from openpyxl.workbook import Workbook

from .forms import QueryForm
import pandas as pd
from datetime import datetime, date
from openpyxl.utils import get_column_letter
from openpyxl.styles import Alignment, Font, Border, Side
import openpyxl
from django.db import OperationalError
from time import sleep

# Create your views here.

from django.shortcuts import render
from .models import UDTGetQueryCondition,UDTGetQueryConditiondetail
from .tools import insertrptReportSN,get_Linelist,get_StationTypelist,format_parameters
def query_conditions_view(request):
    #conditions = UDTGetQueryConditiondetail.objects.all().order_by('ID')
    conditions = UDTGetQueryConditiondetail.objects.filter(status=1).order_by('ID')
    context = {
        'conditions': conditions
    }

    # partfamilyID = 6
    # get_StationTypelist(6)

    # Example usage:



    return render(request, 'query_conditions.html', context)
#
#
#
# '''
# @PartNumber Nvarchar(max)='',
# @ReportID Nvarchar(50)='0',
# @StationTypeList Nvarchar(max)='',
# @PartFamilyTypeID int ='0',
# @QueryCondition Nvarchar(100)='',
# @StartTime datetime='1999-1-1',
# @EndTime datetime='1999-1-1',
# @MPN Nvarchar(100)='',
# @LotCode Nvarchar(100)='',
# @DateCode Nvarchar(100)='',
# @Supplier Nvarchar(100)=''  ,
# @LineName Nvarchar(max)=''
# '''
#
from .tools import insertrptReportSN
from django.shortcuts import render
from .forms import QueryForm
from .models import rptReportSN, UDTGetQueryConditiondetail,ffLine,ffStationType
from django.db import transaction, connections
from django.db.models import Max
import openpyxl
from django.http import HttpResponse


from django.http import JsonResponse
from .dbtools import get_all_lines, get_complex_station_types

def get_station_line_data(request):
    print('get_station_line_data')
    partfamilytypeid = request.GET.get('partfamilytypeid')
    station_types = get_complex_station_types(partfamilytypeid)
    #print(station_types)
    line_names = get_all_lines(partfamilytypeid)

    return JsonResponse({
        'station_types': station_types,
        'line_names': line_names
    })

def query_page(request, id):
    instance = UDTGetQueryConditiondetail.objects.get(ID=id)
    
    # 解析存储过程的参数来确定哪些字段需要显示
    procedure_params = instance.UDP.split(',')  # 假设UDP中的参数是用逗号分隔的
    visible_fields = {
        'sn_textarea': True,  # SN输入框始终显示
        'part_number': '@PartNumber' in instance.UDP,
        'part_family_type': '@PartFamilyTypeID' in instance.UDP,
        'station_type_list': '@StationTypeList' in instance.UDP,
        'line_name': '@LineName' in instance.UDP,
        'start_time': '@StartTime' in instance.UDP,
        'end_time': '@EndTime' in instance.UDP,
        'mpn': '@MPN' in instance.UDP,
        'lot_code': '@LotCode' in instance.UDP,
        'date_code': '@DateCode' in instance.UDP,
        'supplier': '@Supplier' in instance.UDP
    }
    
    context = {
        'form': None,
        'id': instance.ID,
        'Reason': instance.Reason,
        'visible_fields': visible_fields,
        'columns': [],
        'query_results': [],
        'error_message': None,
    }

    if request.method == 'POST':
        line_name_ids = []
        station_type_ids = []
        form = QueryForm(request.POST)
        action = request.POST.get('action', 'submit')

        sn_textarea = request.POST.get('SNTextarea', '')
        part_number = request.POST.get('PartNumber', '')
        station_type_list = request.POST.getlist('station_type_list', [])


        part_family_type = request.POST.get('PartFamilyType', 0)

        start_time = request.POST.get('StartTime', None)
        # print('start_time',start_time)
        end_time = request.POST.get('EndTime', None)

        mpn = request.POST.get('MPN', '')
        lot_code = request.POST.get('LotCode', '')
        date_code = request.POST.get('DateCode', '')
        supplier = request.POST.get('Supplier', '')
        line_name = request.POST.getlist('line_name', [])

        udp = instance.UDP

        for line in line_name:
            line_objs = ffLine.objects.filter(Description=line)
            if line_objs.exists():
                line_name_ids.append(str(line_objs.first().ID))

        for station_type in station_type_list:
            station_type_objs = ffStationType.objects.filter(Description=station_type)
            if station_type_objs.exists():
                station_type_ids.append(str(station_type_objs.first().ID))

        if not station_type_list:
            station_type_ids = None
        else:
            station_type_ids = ','.join(station_type_ids)

        if line_name_ids:
            line_name_ids = ','.join(line_name_ids)
        else:
            line_name_ids = None
        # print('line_name_ids',line_name_ids)
        # print('station_type_ids',station_type_ids)

        SNS = sn_textarea.split('\n')
        SNS = [sn.strip() for sn in SNS if sn.strip()]

        new_report_id = insertrptReportSN(SNS)

        procedure_call = f"""
        EXEC {udp}
            @PartNumber={format_parameters(part_number)},
            @ReportID={new_report_id},
            @StationTypeList={format_param(station_type_ids)},
            @PartFamilyTypeID={part_family_type},
            @QueryCondition={id},
            @StartTime={format_datetime(start_time)},
            @EndTime={format_datetime(end_time)},
            @mpn={format_param(mpn)},
            @lotcode={format_param(lot_code)},
            @datecode={format_param(date_code)},
            @supplier={format_param(supplier)},
            @LineName={format_param(line_name_ids)}
        """
        print("Executing SQL:", procedure_call)

        try:
            with connections['VolcanoReporDB'].cursor() as cursor:
                cursor.execute(procedure_call)
                results = cursor.fetchall()

                columns = [col[0] for col in cursor.description]
                context['columns'] = columns
                context['query_results'] = results
                # context['query_results'] = results[:100]
                context['query_counts'] = len(results)

                #record = UDTGetQueryConditiondetail()
                instance.increment_use_count(id)



        except Exception as e:
            print("Error executing stored procedure:", e)
            context['error_message'] = str(e)

            # Recreate the form instance with the submitted data
        initial_data = {
            'SNTextarea': sn_textarea,
            'PartNumber': part_number,
            'StationTypeList': station_type_list,
            'PartFamilyType': part_family_type,
            'StartTime': start_time,
            'EndTime': end_time,
            'MPN': mpn,
            'LotCode': lot_code,
            'DateCode': date_code,
            'Supplier': supplier,
            'LineName': line_name,
        }
        form = QueryForm.create_query_form(instance, initial=initial_data)
        context['form'] = form
        if action == 'export':
            return export_to_excel(columns, results)
        return render(request, 'query_report3.html', context)
    else:
        form = QueryForm.create_query_form(instance)
        context['form'] = form

    return render(request, 'query_report3.html', context)

def insertrptReportSN(SNS):
    with transaction.atomic():
        max_report_id = rptReportSN.objects.aggregate(Max('ReportID'))['ReportID__max'] or 0
        new_report_id = max_report_id + 1

        new_records = [rptReportSN(ReportID=new_report_id, Serialnumber=sn) for sn in SNS]
        rptReportSN.objects.bulk_create(new_records)
        return new_report_id

def format_param(param):
    if param is None:
        return 'NULL'
    if isinstance(param, str):
        return f"'{param}'"
    return str(param)


import openpyxl
from django.http import HttpResponse

def export_to_excel(columns, results):
    workbook = openpyxl.Workbook()
    sheet = workbook.active

    for col_num, column_title in enumerate(columns, 1):
        sheet.cell(row=1, column=col_num, value=column_title)

    for row_num, row_data in enumerate(results, 2):
        for col_num, cell_value in enumerate(row_data, 1):
            sheet.cell(row=row_num, column=col_num, value=cell_value)

    current_time = datetime.now().strftime('%Y%m%d_%H%M%S')

    # Set the filename with the current time
    filename = f'query_results_{current_time}.xlsx'

    response = HttpResponse(content_type='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet')
    response['Content-Disposition'] = f'attachment; filename={filename}'
    workbook.save(response)
    return response

from datetime import datetime
def format_datetime(datetime_str):
    if datetime_str:
        try:
            # 首先尝试处理带T的ISO格式
            if 'T' in datetime_str:
                datetime_obj = datetime.strptime(datetime_str, '%Y-%m-%dT%H:%M')
            else:
                # 如果不是ISO格式，尝试标准格式
                datetime_obj = datetime.strptime(datetime_str, '%Y-%m-%d %H:%M:%S')
            
            # 统一输出为SQL Server需要的格式
            formatted_datetime = datetime_obj.strftime("'%Y-%m-%d %H:%M:%S'")
            return formatted_datetime
        except ValueError as e:
            print(f"Date parsing error: {e}")
            return 'NULL'
    return 'NULL'


from django.http import JsonResponse
from django.db import connections


import time
from django.db import connections
from django.http import JsonResponse
from .dbtools  import *

from .dbtools import execute_query_with_embedded_param


def get_part_info(request):
    start_time = time.time()
    serialnumber = request.GET.get('serialnumber', '')

    query = """
        SELECT TOP 1  p.PartNumber,po.ProductionOrderNumber FROM dbo.ffSerialNumber(nolock) sn
JOIN dbo.ffUnit(nolock) u ON u.ID=sn.UnitID
LEFT JOIN dbo.ffUnit(nolock) u1 ON u1.ID=u.PanelID
LEFT JOIN dbo.ffPart(nolock) p ON p.id=ISNULL(u1.PartID,u.PartID)
LEFT JOIN dbo.ffProductionOrder(nolock) po ON po.ID=ISNULL( u1.ProductionOrderID,u.ProductionOrderID)
WHERE sn.value= :param_0
    """

    columns, rows = execute_query_with_embedded_param(query, serialnumber)

    end_time = time.time()
    execution_time = end_time - start_time
    #print(f"总执行时间: {execution_time:.4f} 秒")

    if rows:
        result = {'partnumber': rows[0][0], 'ponumber': rows[0][1]}
        return JsonResponse(result)
    else:
        return JsonResponse({'error': '未找到对应信息'}, status=404)

from django.http import JsonResponse
from .models import FFPart

def get_partnumbers(request):
    search = request.GET.get('q', '')
    query = FFPart.objects.filter(
        Status=1,
        IsUnit=1,
        PartFamilyID__isnull=False
    ).select_related('PartFamilyID')

    if search:
        # 如果有搜索词按搜索词过滤
        query = query.filter(PartNumber__icontains=search)
    else:
        # 如果没有搜索词，返回最近的20条记录
        query = query.order_by('-ID')  # 按ID倒序，假设最新的记录ID最大

    # 限制返回数量
    partnumbers = query[:20]

    items = [
        {
            'id': p.PartNumber,
            'text': p.PartNumber,
            'project': p.PartFamilyID.Name if p.PartFamilyID else ''
        }
        for p in partnumbers
    ]

    return JsonResponse({'items': items})

from django.shortcuts import render
from django.http import JsonResponse
from django.contrib.auth.decorators import login_required
from django.views.decorators.http import require_http_methods
from .models import RdtProductionPlan, FFPart
from django.db import transaction

@login_required
def production_plan_view(request):
    return render(request, 'production_plan.html')

@login_required
def production_plan_list(request):
    plans = RdtProductionPlan.objects.all().order_by('-Date', '-ID')
    data = [{
        'ID': plan.ID,
        'Project': plan.Project,
        'Partnumber': plan.Partnumber,
        'LineName': plan.LineName,
        'Date': plan.Date.strftime('%Y-%m-%d'),
        'Shift': plan.get_Shift_display(),
        'UPH': plan.UPH,
        'PlanQty': plan.PlanQty,
        'CreateTime': plan.CreateTime.strftime('%Y-%m-%d %H:%M:%S'),
        'CreateBy': plan.CreateBy
    } for plan in plans]
    return JsonResponse({'data': data})

@login_required
@require_http_methods(['POST'])
def production_plan_create(request):
    try:
        partnumber = request.POST.get('Partnumber')
        line_name = request.POST.get('LineName')
        date = request.POST.get('Date')
        shift = request.POST.get('Shift')
        plan_qty = request.POST.get('PlanQty')
        uph = request.POST.get('UPH')  # UPH可能为空

        # 验证必填字段
        if not all([partnumber, line_name, date, shift, plan_qty]):
            return JsonResponse({'success': False, 'error': '请填写所有必填字段'})

        # 转换数据类型
        plan_qty = int(plan_qty)
        # 只有当UPH不为空时才转换
        uph = int(uph) if uph else None

        # 检查是否已存在相同的计划
        existing_plan = RdtProductionPlan.objects.filter(
            Partnumber=partnumber,
            LineName=line_name,
            Date=date,
            Shift=shift
        ).first()

        if existing_plan:
            # 更新现有计划
            existing_plan.PlanQty = plan_qty
            existing_plan.UPH = uph  # UPH可以为None
            existing_plan.UpdateBy = request.user.username
            existing_plan.save()
        else:
            # 创建新计划
            RdtProductionPlan.objects.create(
                Partnumber=partnumber,
                LineName=line_name,
                Date=date,
                Shift=shift,
                PlanQty=plan_qty,
                UPH=uph,  # UPH可以为None
                CreateBy=request.user.username,
                UpdateBy=request.user.username
            )

        return JsonResponse({'success': True})
    except Exception as e:
        return JsonResponse({'success': False, 'error': str(e)})

@login_required
@require_http_methods(['POST'])
def production_plan_delete(request):
    try:
        plan_id = request.POST.get('id')
        plan = RdtProductionPlan.objects.get(ID=plan_id)
        plan.delete()
        return JsonResponse({'success': True})
    except Exception as e:
        return JsonResponse({'success': False, 'error': str(e)})

def get_lines(request):
    search = request.GET.get('q', '')
    query = ffLine.objects.all()

    if search:
        query = query.filter(Description__icontains=search)
    
    # 获所有线别，按Description排序
    lines = query.order_by('Description').all()  # 移除[:20]限制

    items = [
        {
            'id': line.Description.strip(),  # 去除可能的空格
            'text': line.Description.strip()
        }
        for line in lines
    ]

    return JsonResponse({'items': items})

from django.db import connections
import pandas as pd
from datetime import datetime
from django.http import HttpResponse

@login_required
def line_output_view(request):
    return render(request, 'line_output.html')

def execute_with_retry(cursor, sql, params=None, max_retries=3):
    for attempt in range(max_retries):
        try:
            return cursor.execute(sql, params)
        except OperationalError as e:
            if attempt == max_retries - 1:  # 最后一次尝试
                raise
            sleep(1)  # 等待1秒后重试
            cursor.close()
            cursor = connections[cursor.db.alias].cursor()

# 添加缓存相关的导入
from django.core.cache import cache
import uuid
from pathlib import Path
import os
from django.conf import settings

# 在views.py中添加一个新的函数来生成和保存Excel文件
def generate_excel_file(data, cache_key):
    """生成Excel文件并返回文件路径"""
    workbook = openpyxl.Workbook()
    sheet = workbook.active
    
    # 设置表头
    headers = [
        'Type', 'Line', 'PartNumber', 'Station', 'Date', 'Shift',
        'Output', 'PlanQty', 'Gap',  'StopTime',
        'StopCount', 'Incidents'
    ]
    
    for col_num, header in enumerate(headers, 1):
        sheet.cell(row=1, column=col_num, value=header)
    
    # 写入数据
    for row_num, row_data in enumerate(data, 2):
        # Type
        sheet.cell(row=row_num, column=1, value=row_data.get('Type'))
        # Line
        sheet.cell(row=row_num, column=2, value=row_data.get('Line'))
        # PartNumber
        sheet.cell(row=row_num, column=3, value=row_data.get('PartNumber'))
        # Station
        sheet.cell(row=row_num, column=4, value=row_data.get('Station'))
        # Date
        sheet.cell(row=row_num, column=5, value=row_data.get('Date'))
        # Shift
        sheet.cell(row=row_num, column=6, value=row_data.get('shift'))
        # Output
        sheet.cell(row=row_num, column=7, value=row_data.get('Output'))
        # PlanQty
        sheet.cell(row=row_num, column=8, value=row_data.get('PlanQty'))
        # Gap
        sheet.cell(row=row_num, column=9, value=row_data.get('Gap'))
     
        
        # StopTime
        total_stop_time = sum(int(inc['StopTime']) for inc in row_data.get('incidents', []))
        sheet.cell(row=row_num, column=12, value=f"{total_stop_time}分钟")
        
        # StopCount
        sheet.cell(row=row_num, column=13, value=len(row_data.get('incidents', [])))
        
        # Incidents
        incidents_text = '\n'.join([
            f"时间: {inc['StartTime'][11:16]}-{inc['EndTime'][11:16]}, "
            f"时长: {inc['StopTime']}分钟, "
            f"问题: {inc['IssueDescription']}, "
            f"责任人: {inc['ResponsiblePerson']}"
            for inc in row_data.get('incidents', [])
        ])
        sheet.cell(row=row_num, column=14, value=incidents_text)

    # 创建临时文件目录（如果不存在）
    temp_dir = Path(settings.MEDIA_ROOT) / 'temp_excel'
    temp_dir.mkdir(parents=True, exist_ok=True)
    
    # 生成文件路径
    file_path = temp_dir / f"line_output_{cache_key}.xlsx"
    
    # 保存文件
    workbook.save(file_path)
    return str(file_path)

from django.core.management import call_command
from datetime import timedelta
import os

def cleanup_temp_files(force=False):
    """
    清理临时文件
    :param force: 是否强制删除（即使文件可能被占用）
    """
    temp_dir = Path(settings.MEDIA_ROOT) / 'temp_excel'
    if not temp_dir.exists():
        return
        
    current_time = datetime.now()
    for file in temp_dir.glob('*.xlsx'):
        try:
            # 获取文件修改时间
            file_time = datetime.fromtimestamp(os.path.getmtime(file))
            # 如果文件超过1小时就删除，或强制删除
            if force or current_time - file_time > timedelta(hours=1):
                try:
                    os.remove(file)
                except PermissionError:
                    # 如果文件被占用，可以使用系统命令强制删除
                    if force:
                        import subprocess
                        try:
                            if os.name == 'nt':  # Windows
                                subprocess.run(['del', '/F', str(file)], shell=True)
                            else:  # Linux/Unix
                                subprocess.run(['rm', '-f', str(file)])
                        except Exception as e:
                            print(f"强制删除文件失败 {file}: {e}")
                except Exception as e:
                    print(f"删除文件失败 {file}: {e}")
        except Exception as e:
            print(f"处理文件失败 {file}: {e}")

@login_required
@require_http_methods(['POST'])
def line_output_data(request):
    try:
        # 先清理旧文件
        cleanup_temp_files()
        
        start_time = request.POST.get('start_time')
        end_time = request.POST.get('end_time')
        project_type = request.POST.get('project_type')
        
        # 获取认记录
        confirmed_records = udtLineOutputConfirmation.objects.filter(
            Date__range=[start_time.split()[0], end_time.split()[0]]
        ).values('Line', 'Date', 'Shift', 'PartNumber')
        
        # 创建一个集合来存储已确认的记录的唯一标识
        confirmed_set = {
            f"{record['Line']}_{record['Date'].strftime('%Y-%m-%d')}_{record['Shift']}_{record['PartNumber']}" 
            for record in confirmed_records
        }
        
        # 生成唯一的缓存键
        cache_key = str(uuid.uuid4())
        
        # 根据项目类型选择存储过程
        if project_type == "Hermes":
            sp_name = "[dbo].[ruspgetoutput_HermesByLine_rw]"
        elif project_type == "Alpha":
            sp_name = "[dbo].[ruspgetoutput_AlphaByLine_rw]"
        elif project_type == "Kosmos":
            sp_name = "[dbo].[ruspgetoutput_KosmosByLine_rw]"
        elif project_type == "Mono":
            sp_name = "[dbo].[ruspgetoutput_Mono_rw]"
        else:
            sp_name = "[dbo].[ruspgetoutput_SMTByLine_rw]"
        
        # 获取生产数据
        with connections['VolcanoReporDB'].cursor() as cursor:
            cursor.execute("SET NOCOUNT ON")
            cursor.execute(f"""
                DECLARE @StartTime DATETIME = %s
                DECLARE @EndTime DATETIME = %s
                EXEC {sp_name} @StartTime, @EndTime
            """, [start_time, end_time])
            
            results = []
            columns = [col[0] for col in cursor.description]
            rows = cursor.fetchall()
            
            for row in rows:
                row_dict = {}
                for i, value in enumerate(row):
                    if isinstance(value, datetime):
                        value = value.strftime('%Y-%m-%d %H:%M:%S')
                    elif isinstance(value, str):
                        value = value.strip()
                    row_dict[columns[i]] = value
                results.append(row_dict)

        # 获取停线事件数据
        with connections['B15_DWH'].cursor() as cursor:
            execute_with_retry(cursor, "SET NOCOUNT ON")
            execute_with_retry(cursor, """
                DECLARE @StartTime DATETIME = %s
                DECLARE @EndTime DATETIME = %s
                EXEC [dbo].[usp_GetIncidentDetails_rw] @StartTime, @EndTime
            """, [start_time, end_time])
            
            incident_results = []
            incident_columns = [col[0] for col in cursor.description]
            incident_rows = cursor.fetchall()
            
            for row in incident_rows:
                row_dict = {}
                for i, value in enumerate(row):
                    if isinstance(value, datetime):
                        value = value.strftime('%Y-%m-%d %H:%M:%S')
                    elif isinstance(value, str):
                        value = value.strip()
                    row_dict[incident_columns[i]] = value
                incident_results.append(row_dict)

        # 合并数据
        merged_results = []
        # 创建一个临时字典来存储分组数据
        line_shift_totals = {}

        # 第一次循环：计算每个线别每个班次的汇总数据
        for prod in results:
            line_key = (prod['Line'], prod['Date'], prod['shift'])
            
            if line_key not in line_shift_totals:
                line_shift_totals[line_key] = {
                    'total_output': 0,
                    'total_plan': 0,
                    'total_stop_time': 0,
                    'uph': int(prod['UPH']) if 'UPH' in prod else 0
                }
            
            line_shift_totals[line_key]['total_output'] += int(prod['Output']) if 'Output' in prod else 0
            line_shift_totals[line_key]['total_plan'] += int(prod['PlanQty']) if 'PlanQty' in prod else 0

        # 第二次循环：计算停线时（按线别和班次）
        for inc in incident_results:
            line_key = (inc['LineName'], inc['Date'], inc['Shift'])
            if line_key in line_shift_totals:
                line_shift_totals[line_key]['total_stop_time'] += int(inc['StopTime'])

        # 第三次循环：用汇总数据计算每个记录的DTGap
        for prod in results:
            line_key = (prod['Line'], prod['Date'], prod['shift'])
            line_totals = line_shift_totals[line_key]
            
            # 计算DTGap
            if line_totals['uph'] > 0:
                from decimal import Decimal
                total_gap = line_totals['total_plan'] - line_totals['total_output']
                total_gap_abs = abs(Decimal(total_gap))
                dt_gap = Decimal(line_totals['total_stop_time']) - (total_gap_abs / Decimal(line_totals['uph']) * Decimal('60'))
                dt_gap = float(dt_gap.quantize(Decimal('0.01')))
            else:
                dt_gap = 0

            # 获取该记录的停线事件
            incidents = [inc for inc in incident_results 
                       if (inc['Date'], inc['LineName'], inc['Shift']) == (prod['Date'], prod['Line'], prod['shift'])]
            
            # 添加确认状态 - 修复日期处理
            date_str = prod['Date']
            if isinstance(date_str, datetime):
                date_str = date_str.strftime('%Y-%m-%d')
            elif isinstance(date_str, str):
                date_str = date_str.split()[0]
                
            key = f"{prod['Line']}_{date_str}_{prod['shift']}_{prod['PartNumber']}"
            prod['isConfirmed'] = key in confirmed_set
            
            prod['DTGap'] = dt_gap
            prod['incidents'] = incidents
            merged_results.append(prod)

        # 异步生成Excel文件
        excel_file_path = generate_excel_file(merged_results, cache_key)
        
        # 将数据和文件路径存入缓存
        cache_data = {
            'data': merged_results,
            'excel_path': excel_file_path,
            'timestamp': datetime.now().timestamp()
        }
        cache.set(cache_key, cache_data, timeout=3600)  # 缓存1小时
        
        # 返回数据和缓存键
        return JsonResponse({
            'data': merged_results,
            'cache_key': cache_key
        })
                
    except Exception as e:
        print(f"Error in line_output_data: {str(e)}")
        import traceback
        print(traceback.format_exc())
        return JsonResponse({'error': str(e)})

@login_required
def line_output_export(request):
    try:
        cache_key = request.GET.get('cache_key')
        if cache_key:
            cached_data = cache.get(cache_key)
            if cached_data and cached_data.get('excel_path'):
                file_path = cached_data['excel_path']
                if os.path.exists(file_path):
                    # 读取文件内容到内存
                    file_content = None
                    with open(file_path, 'rb') as f:
                        file_content = f.read()
                    
                    # 关闭文件后再尝试删除
                    try:
                        os.remove(file_path)
                    except Exception as e:
                        print(f"删除文件失败 {file_path}: {e}")
                        # 如果删除失败，记录下来以便后续清理
                        
                    # 创建响应
                    if file_content:
                        response = HttpResponse(
                            file_content,
                            content_type='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
                        )
                        response['Content-Disposition'] = f'attachment; filename=LineOutput_{datetime.now().strftime("%Y%m%d_%H%M%S")}.xlsx'
                        return response
        
        return HttpResponse("导出数据已过期，请重新查询", status=400)
        
    except Exception as e:
        print(f"Error in line_output_export: {str(e)}")
        import traceback
        print(traceback.format_exc())
        return HttpResponse(f"导出失败: {str(e)}", status=500)

from .models import udtLineOutputConfirmation
@login_required
@require_http_methods(['POST'])
def save_confirmation(request):
    try:
        with transaction.atomic():
            line = request.POST.get('line')
            date = request.POST.get('date')
            shift = request.POST.get('shift')
            part_number = request.POST.get('partNumber')
            
            # 检查同一条线同一班次是否已有记录
            existing_record = udtLineOutputConfirmation.objects.filter(
                Line=line,
                Date=datetime.strptime(date, '%Y-%m-%d').date(),
                Shift=shift
            ).first()
            
            # 计算DTGap
            uph = int(request.POST.get('UPH', 0))
            gap = int(request.POST.get('gap', 0))
            
            # 如果同一条线同一班次已有记录，则停线相关信息置为0或空
            if existing_record:
                stop_time = 0
                stop_count = 0
                incidents_detail = ''
            else:
                # 如果是该线该班次的第一条记录，则使用传入的停线信息
                stop_time = int(request.POST.get('stopTime', '0').replace('分钟', ''))
                stop_count = int(request.POST.get('stopCount', 0))
                incidents_detail = request.POST.get('incidentsDetail', '')
            
            # 计算DTGap
            if uph > 0:
                from decimal import Decimal
                gap_abs = abs(Decimal(gap))
                dt_gap = Decimal(stop_time) - (gap_abs / Decimal(uph) * Decimal('60'))
                dt_gap = float(dt_gap.quantize(Decimal('0.01')))
            else:
                dt_gap = 0
                
            # 创建确认记录
            confirmation = udtLineOutputConfirmation(
                Line=line,
                Date=datetime.strptime(date, '%Y-%m-%d').date(),
                Shift=shift,
                Type=request.POST.get('type') or None,
                PartNumber=part_number,
                Station=request.POST.get('station'),
                UPH=uph,
                Output=int(request.POST.get('output', 0)),
                PlanQty=int(request.POST.get('planqty', 0)),
                Gap=int(request.POST.get('gap', 0)),
                DTGap=dt_gap,
                StopTime=f"{stop_time}分钟",
                StopCount=stop_count,
                IncidentsDetail=incidents_detail,
                Note=request.POST.get('note'),
                ConfirmedBy=request.user.username
            )
            confirmation.save()
            
            # 1. 保存到 RdtProductionPlan 表
            existing_plan = RdtProductionPlan.objects.filter(
                LineName=line,
                Date=datetime.strptime(date, '%Y-%m-%d').date(),
                Shift=shift,
                Partnumber=part_number
            ).first()
            
            if existing_plan:
                # 如果存在则更新
                existing_plan.PlanQty = int(request.POST.get('planqty', 0))
                existing_plan.UPH = uph
                existing_plan.UpdateBy = request.user.username
                existing_plan.UpdateTime = datetime.now()
                existing_plan.save()
            else:
                # 如果不存在则创建新记录
                RdtProductionPlan.objects.create(
                    Partnumber=part_number,
                    Project=request.POST.get('type'),
                    LineName=line,
                    Date=datetime.strptime(date, '%Y-%m-%d').date(),
                    Shift=shift,
                    PlanQty=int(request.POST.get('planqty', 0)),
                    UPH=uph,
                    CreateBy=request.user.username,
                    UpdateBy=request.user.username
                )
            
            return JsonResponse({'success': True})
            
    except Exception as e:
        print(f"Error in save_confirmation: {str(e)}")
        import traceback
        print(traceback.format_exc())
        return JsonResponse({'success': False, 'error': str(e)})

@login_required
def week_summary_view(request):
    # 获取可选的周列表
    week_choices = udtLineOutputConfirmation.get_week_choices()
    
    # 获取选中的日期范围
    selected_range = request.GET.get('week_range', '')
    if selected_range:
        start_date, end_date = selected_range.split(',')
    else:
        # 默认显示当前周
        today = datetime.now()
        start_date = (today - timedelta(days=today.weekday())).strftime('%Y-%m-%d')
        end_date = (today + timedelta(days=6-today.weekday())).strftime('%Y-%m-%d')
    
    # 获取汇总数据
    summary_data = udtLineOutputConfirmation.get_week_summary(start_date, end_date)
    # print("Debug - 原始汇总数据:", summary_data)
    
    # 添加详细的数据检查
    # for item in summary_data:
    #     print(f"Debug - 行数据详情:")
    #     print(f"  Line: {item['Line']}")
    #     print(f"  StopTime (raw): {item['StopTime']}")
    #     print(f"  StopTime (type): {type(item['StopTime'])}")
        
    # 处理每条记录中的停线时间
    for item in summary_data:
        # StopCount 已经在表中，不需要处理
        # 处理 StopTime 字段，去除"分钟"字样
        if 'StopTime' in item:
            try:
                # 移除"分钟"字样并转换为整数
                stop_time = item['StopTime']
                if isinstance(stop_time, str):
                    stop_time = stop_time.replace('分钟', '').strip()
                item['StopTime'] = int(stop_time) if stop_time else 0
            except (ValueError, AttributeError) as e:
                print(f"Debug - StopTime 转换错误: {e}, 原始值: {item['StopTime']}")
                item['StopTime'] = 0
        else:
            item['StopTime'] = 0
            
    # print("Debug - 处理后的汇总数据:", summary_data)
    
    context = {
        'week_choices': week_choices,
        'selected_range': f"{start_date},{end_date}",
        'summary_data': summary_data,
        'start_date': start_date,
        'end_date': end_date,
    }
    
    return render(request, 'week_summary.html', context)

@login_required
def export_week_summary(request):
    start_date = request.GET.get('start_date')
    end_date = request.GET.get('end_date')
    
    if not start_date or not end_date:
        return HttpResponse("请选择日期范围", status=400)
        
    # 获取汇总数据
    summary_data = udtLineOutputConfirmation.get_week_summary(start_date, end_date)

    # print(f"Debug - 汇总数据: {summary_data}")
    
    # 处理每条记录中的停线时间
    for item in summary_data:
        # 处理 StopTime 字段，去除"分钟"字样
        if 'StopTime' in item:
            try:
                stop_time = item['StopTime'].replace('分钟', '').strip()
                item['StopTime'] = int(stop_time) if stop_time else 0
            except (ValueError, AttributeError):
                item['StopTime'] = 0
    
    # 创建Excel文件
    wb = openpyxl.Workbook()
    ws = wb.active
    ws.title = "周汇总数据"
    
    # 写入表头
    headers = ['线别', '类型', '站别', '料号', '实际产出', '计划产出', '差异', '停线次数', '停线时长(分钟)']
    for col, header in enumerate(headers, 1):
        ws.cell(row=1, column=col, value=header)
    
    # 写入数据
    for row, data in enumerate(summary_data, 2):
        ws.cell(row=row, column=1, value=data['Line'])
        ws.cell(row=row, column=2, value=data['Type'])
        ws.cell(row=row, column=3, value=data['Station'])
        ws.cell(row=row, column=4, value=data['PartNumber'])
        ws.cell(row=row, column=5, value=data['total_output'])
        ws.cell(row=row, column=6, value=data['total_planqty'])
        ws.cell(row=row, column=7, value=data['gap'])
        ws.cell(row=row, column=8, value=data['StopCount'])
        ws.cell(row=row, column=9, value=data['StopTime'])
    
    # 调整列宽
    for col in range(1, 10):
        ws.column_dimensions[get_column_letter(col)].width = 15
    ws.column_dimensions[get_column_letter(9)].width = 20  # 停线时长列加宽
    
    # 生成响应
    response = HttpResponse(
        content_type='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
    )
    response['Content-Disposition'] = f'attachment; filename=week_summary_{start_date}_{end_date}.xlsx'
    wb.save(response)
    
    return response

@login_required
def incident_details_view(request):
    return render(request, 'incident_details.html')

@login_required
@require_http_methods(['POST'])
def incident_details_data(request):
    try:
        start_time = request.POST.get('start_time')
        end_time = request.POST.get('end_time')
        
        print(f"Debug - 开始查询停线事件，时间范围: {start_time} 到 {end_time}")
        
        # 确保日期时间格式正确
        try:
            start_dt = datetime.strptime(start_time, '%Y-%m-%d %H:%M:%S')
            end_dt = datetime.strptime(end_time, '%Y-%m-%d %H:%M:%S')
            # 转换为SQL Server可接受的格式
            start_time = start_dt.strftime('%Y-%m-%d %H:%M:%S.000')
            end_time = end_dt.strftime('%Y-%m-%d %H:%M:%S.000')
        except ValueError as e:
            return JsonResponse({'error': f'日期时间格式错误: {str(e)}'})
        
        with connections['B15_DWH'].cursor() as cursor:
            execute_with_retry(cursor, "SET NOCOUNT ON")
            # 先执行一个测试查询来查看原始数据
            # test_sql = """
            #     SELECT TOP 10 
            #         l.LineName,    
            #         a.StartTime,    
            #         a.EndTime,    
            #         CASE 
            #             WHEN a.EndTime IS NULL THEN 
            #                 DATEDIFF(minute, a.StartTime, GETDATE())
            #             ELSE 
            #                 DATEDIFF(minute, a.StartTime, a.EndTime)
            #         END AS StopTime,    
            #         a.IssueDescription,    
            #         a.ResponsiblePerson, 
            #         c.Name AS RelatedDept,
            #         CASE     
            #             WHEN DATEPART(HOUR, a.StartTime) >= 7 THEN CAST(a.StartTime AS DATE)    
            #             ELSE DATEADD(DAY, -1, CAST(a.StartTime AS DATE))    
            #         END AS [Date],    
            #         CASE     
            #             WHEN DATEPART(HOUR, a.StartTime) >= 7 AND DATEPART(HOUR, a.StartTime) < 20 THEN 'Day'    
            #             ELSE 'Night'    
            #         END AS [Shift]    
            #     FROM udtTlsIncident a    
            #     INNER JOIN udtEdbLine l ON l.LineID = a.LineID AND l.PjID = a.PjID    
            #     JOIN udtTlsDept c ON c.ID = a.RelatedDept
            #     WHERE a.Status = 3     
            #         AND a.StartTime BETWEEN %s AND %s    
            #     ORDER BY l.LineName, a.StartTime
            # """
            # execute_with_retry(cursor, test_sql, (start_time, end_time))
            # test_rows = cursor.fetchall()
            # test_columns = [col[0] for col in cursor.description]
            # print("Debug - 测试查询结果:")
            # for row in test_rows:
            #     print(f"Row data: {dict(zip(test_columns, row))}")
            
            # 执行实际的存储过程
            sql = """
                DECLARE @StartTime DATETIME = %s
                DECLARE @EndTime DATETIME = %s
                EXEC [dbo].[usp_GetIncidentDetails_rw_new] @StartTime, @EndTime
            """
            execute_with_retry(cursor, sql, (start_time, end_time))
            
            columns = [col[0] for col in cursor.description]
            # print(f"Debug - 返回的列名: {columns}")
            rows = cursor.fetchall()
            
            data = []
            for row in rows:
                # print(f"Debug - 原始行数据: {row}")
                row_dict = {}
                for i, value in enumerate(row):
                    if isinstance(value, datetime):
                        value = value.strftime('%Y-%m-%d %H:%M:%S')
                    elif isinstance(value, str):
                        value = value.strip()
                        if ';' in value:
                            print(f"Debug - 发现包含分号的值: {value} in column {columns[i]}")
                    elif value is None:
                        value = ''
                    row_dict[columns[i]] = value
                data.append(row_dict)
                
        return JsonResponse({'data': data})
        
    except Exception as e:
        print(f"Error in incident_details_data: {str(e)}")
        import traceback
        print(traceback.format_exc())
        return JsonResponse({'error': str(e)})

@login_required
def incident_details_export(request):
    try:
        start_time = request.GET.get('start_time')
        end_time = request.GET.get('end_time')
        
        # 确保日期时间格式正确
        try:
            start_dt = datetime.strptime(start_time, '%Y-%m-%d %H:%M:%S')
            end_dt = datetime.strptime(end_time, '%Y-%m-%d %H:%M:%S')
            # 转换为SQL Server可接受的格式
            start_time = start_dt.strftime('%Y-%m-%d %H:%M:%S.000')
            end_time = end_dt.strftime('%Y-%m-%d %H:%M:%S.000')
        except ValueError as e:
            return HttpResponse(f'日期时间格式错误: {str(e)}', status=400)
        
        with connections['B15_DWH'].cursor() as cursor:
            execute_with_retry(cursor, "SET NOCOUNT ON")
            sql = """
                DECLARE @StartTime DATETIME = %s
                DECLARE @EndTime DATETIME = %s
                EXEC [dbo].[usp_GetIncidentDetails_rw_new] @StartTime, @EndTime
            """
            execute_with_retry(cursor, sql, (start_time, end_time))
            
            # 获取列名
            columns = [col[0] for col in cursor.description]
            
            # 创建Excel工作簿
            wb = openpyxl.Workbook()
            ws = wb.active
            ws.title = "停线事件详情"
            
            # 定义要导出的列及其对应的中文表头
            column_mapping = {
                'LineName': '线别',
                'Shift': '班次',
                'RelatedDept': '相关部门',
                'StartTime': '开始时间',
                'EndTime': '结束时间',
                'StopTime': '停线时长',
                'IssueDescription': '问题描述',
                'ResponsiblePerson': '责任人'
            }
            
            # 写入表头
            for col, (_, header) in enumerate(column_mapping.items(), 1):
                ws.cell(row=1, column=col, value=header)
            
            # 写入数据
            rows = cursor.fetchall()
            for row_idx, row in enumerate(rows, 2):
                row_dict = dict(zip(columns, row))
                for col, (col_name, _) in enumerate(column_mapping.items(), 1):
                    value = row_dict.get(col_name, '')
                    if isinstance(value, datetime):
                        value = value.strftime('%Y-%m-%d %H:%M:%S')
                    elif isinstance(value, str):
                        value = value.strip()
                        if ';' in value:
                            value = value.replace(';', ',')
                    
                    # 特殊处理停线时长列，确保是数字
                    if col_name == 'StopTime':
                        cell = ws.cell(row=row_idx, column=col, value=int(value))
                        # 移除自定义数字格式中的"分钟"
                        cell.number_format = '0'
                    else:
                        ws.cell(row=row_idx, column=col, value=value)
            
            # 设置列宽
            ws.column_dimensions[get_column_letter(1)].width = 15  # 线别
            ws.column_dimensions[get_column_letter(2)].width = 10  # 班次
            ws.column_dimensions[get_column_letter(3)].width = 15  # 相关部门
            ws.column_dimensions[get_column_letter(4)].width = 20  # 开始时间
            ws.column_dimensions[get_column_letter(5)].width = 20  # 结束时间
            ws.column_dimensions[get_column_letter(6)].width = 15  # 停线时长
            ws.column_dimensions[get_column_letter(7)].width = 50  # 问题描述
            ws.column_dimensions[get_column_letter(8)].width = 15  # 责任人
            
            # 生成响应
            response = HttpResponse(
                content_type='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
            )
            response['Content-Disposition'] = f'attachment; filename=incident_details_{datetime.now().strftime("%Y%m%d_%H%M%S")}.xlsx'
            wb.save(response)
            return response
            
    except Exception as e:
        print(f"Error in incident_details_export: {str(e)}")
        import traceback
        print(traceback.format_exc())
        return HttpResponse(f"导出失败: {str(e)}", status=500)


from sqlalchemy import create_engine, text
from sqlalchemy.exc import SQLAlchemyError

from sqlalchemy import create_engine, text
from sqlalchemy.exc import SQLAlchemyError


def execute_query(query, params=None):
    """
    Execute a given SQL query or stored procedure with optional parameters.

    :param query: SQL query or stored procedure to execute.
    :param params: Dictionary of parameters to pass to the query.
    :return: Tuple containing columns and rows of the result set.
    """
    engine = create_engine(
        'mssql+pymssql://Report_Volcano:V!2017CANL@*************:1437/FF_Volcano_Report'
    )

    connection = engine.connect()
    print("Connected to the database")

    try:
        if params:
            query = text(query).params(**params)
        else:
            query = text(query)

        result = connection.execute(query)
        rows = result.fetchall()

        if not rows:
            raise ValueError("Query did not return any data")

        columns = result.keys()
        # print('Columns:', columns)
        # print('Data fetched:', rows)
        return columns, rows
    except SQLAlchemyError as e:
        print("Error executing query:", e)
        return None, None
    finally:
        connection.close()


#指定结果集的列，转成list
def extract_column_data(columns, rows, target_column):
    """
    Extracts data from the specified column in the fetched rows.

    Args:
    - columns (list or RMKeyView): List of column names.
    - rows (list of tuples): Fetched rows.
    - target_column (str): Name of the target column.

    Returns:
    - list: List containing data from the specified column.
    """
    try:
        column_index = list(columns).index(target_column)
        column_data = [row[column_index] for row in rows]
        return column_data
    except ValueError:
        print(f"Column '{target_column}' not found.")
        return []


# Your original code
#返回工位列表
def get_all_lines(partfamilytypeid):
    query = """
    DECLARE @TypeName varchar(50);
    DECLARE @ListID INT;
    SET @ListID = :ListID;

    SELECT @TypeName = name FROM luPartFamilyType WITH (NOLOCK) WHERE id = @ListID;

    SELECT Description FROM ffLine WITH (NOLOCK) WHERE location LIKE @TypeName + '%' ORDER BY location;
    """
    params = {'ListID': partfamilytypeid}  # 假设需要传递 ListID 参数

    columns, rows = execute_query(query, params)

    if columns and rows:
        # print('Columns:', columns)
        # print('Data fetched:', rows)
        description_list = extract_column_data(columns, rows, 'Description')
        #print('Description List:', description_list)
        return description_list
    else:
        print("No data returned")


# 调用示例
# linelist = get_all_lines(6)
# print(linelist)

# 调用示例








# 调用示例
#columns, rows = get_all_lines()


def get_complex_station_types(partfamilyID):
    query = "EXEC [dbo].UDPGetPartFamilyTypeStations @PartfamilyTypeID=:partfamilyID"
    params = {'partfamilyID': partfamilyID}

    columns, rows = execute_query(query, params)

    if columns and rows:
        # print('Columns:', columns)
        # print('Data fetched:', rows)
        description_list = extract_column_data(columns, rows, 'Stationtype')
        return description_list
    else:
        print("No data returned")
        return None, None


# 调用示例
# rows = get_complex_station_types(6)
# print(rows)
import time

def get_ff_lines_without_params():
    query = """
    SELECT Description FROM ffLine WITH (NOLOCK) WHERE location LIKE 'Kosmos%' ORDER BY location
    """

    columns, rows = execute_query(query)

    if columns and rows:
        print('Columns:', columns)
        print('Data fetched:', rows)
        return columns, rows
    else:
        print("No data returned")
        return None, None


# 调用示例
#columns, rows = get_ff_lines_without_params()


def execute_query_with_embedded_param(query, param):
    start_time = time.time()
    print(f"开始连接数据库: {start_time}")

    engine = create_engine(
        'mssql+pymssql://Report_Volcano:V!2017CANL@*************:1437/FF_Volcano_Report'
    )

    with engine.connect() as connection:
        connect_time = time.time()
        print(f"数据库连接耗时: {connect_time - start_time:.4f} 秒")

        try:
            # 直接在查询字符串中嵌入参数
            query = query.replace(":param_0", f"'{param}'")

            query_start = time.time()
            result = connection.execute(text(query))
            query_end = time.time()
            print(f"查询执行耗时: {query_end - query_start:.4f} 秒")

            fetch_start = time.time()
            rows = result.fetchall()
            fetch_end = time.time()
            print(f"数据获取耗时: {fetch_end - fetch_start:.4f} 秒")

            if not rows:
                print("Query did not return any data")
                return None, None

            columns = result.keys()
            return columns, rows
        except SQLAlchemyError as e:
            print("Error executing query:", e)
            return None, None



# Your original code
#返回工位列表
def get_all_lines(partfamilytypeid):
    query = """
    DECLARE @TypeName varchar(50);
    DECLARE @ListID INT;
    SET @ListID = :ListID;

    SELECT @TypeName = name FROM luPartFamilyType WITH (NOLOCK) WHERE id = @ListID;

    SELECT Description FROM ffLine WITH (NOLOCK) WHERE location LIKE @TypeName + '%' ORDER BY location;
    """
    params = {'ListID': partfamilytypeid}  # 假设需要传递 ListID 参数

    columns, rows = execute_query(query, params)

    if columns and rows:
        # print('Columns:', columns)
        # print('Data fetched:', rows)
        description_list = extract_column_data(columns, rows, 'Description')
        #print('Description List:', description_list)
        return description_list
    else:
        print("No data returned")


# 调用示例
# linelist = get_all_lines(6)
# print(linelist)

# 调用示例








# 调用示例
#columns, rows = get_all_lines()


def get_complex_station_types(partfamilyID):
    query = "EXEC [dbo].UDPGetPartFamilyTypeStations @PartfamilyTypeID=:partfamilyID"
    params = {'partfamilyID': partfamilyID}

    columns, rows = execute_query(query, params)

    if columns and rows:
        # print('Columns:', columns)
        # print('Data fetched:', rows)
        description_list = extract_column_data(columns, rows, 'Stationtype')
        return description_list
    else:
        print("No data returned")
        return None, None


# 调用示例
# rows = get_complex_station_types(6)
# print(rows)


def get_ff_lines_without_params():
    query = """
    SELECT Description FROM ffLine WITH (NOLOCK) WHERE location LIKE 'Kosmos%' ORDER BY location
    """

    columns, rows = execute_query(query)

    if columns and rows:
        print('Columns:', columns)
        print('Data fetched:', rows)
        return columns, rows
    else:
        print("No data returned")
        return None, None


# 调用示例
#columns, rows = get_ff_lines_without_params()


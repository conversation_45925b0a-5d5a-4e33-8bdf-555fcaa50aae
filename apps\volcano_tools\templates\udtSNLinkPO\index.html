{% extends 'base1.html' %}
{% load static %}

{% block title %}SN绑定工单（udtSNLinkPO）{% endblock %}

{% block extra_css %}
<style>
/* 只影响本页面的输入框，优先级更高 */
.container .form-control {
    background-color: #f4f6fa !important;
    border: 2px solid #343a40 !important;  /* 更深的灰黑色 */
    border-radius: 0.5rem !important;
    box-shadow: none !important;
    color: #22223b !important;
    font-size: 1.1rem;
    transition: border-color 0.2s, background-color 0.2s;
}
.container .form-control:focus {
    background-color: #fff !important;
    border-color: #4285f4 !important;
    box-shadow: 0 0 0 0.15rem rgba(66,133,244,.15) !important;
    outline: none !important;
}
.container textarea.form-control {
    min-height: 120px;
    resize: vertical;
}
</style>
{% endblock %}

{% block content %}
<div class="container mt-4">
    <h2 class="mb-3">SN绑定工单（udtSNLinkPO）</h2>
    <form method="post" class="row g-3 mb-4">
        {% csrf_token %}
        <!-- SN列表 (50%宽度) -->
        <div class="col-md-6">
            <label class="form-label">SN列表（每行一个）</label>
            <textarea name="sn_list" class="form-control" rows="5">{{ sn_list|default_if_none:"" }}</textarea>
        </div>
        
        <!-- 右侧字段组 (50%宽度) -->
        <div class="col-md-6">
            <div class="row g-3">
                <!-- PO号 -->
                <div class="col-12 mb-3">
                    <label class="form-label">PO号</label>
                    <input type="text" name="po" class="form-control" value="{{ po|default_if_none:"" }}">
                </div>
                <!-- SNC编号 -->
                <div class="col-12 mb-3">
                    <label class="form-label">SNC编号</label>
                    <input type="text" name="snc" class="form-control" value="{{ snc|default_if_none:"" }}">
                </div>
                <!-- 申请人 -->
                <div class="col-12 mb-3">
                    <label class="form-label">申请人</label>
                    <input type="text" name="requestBy" class="form-control" value="{{ requestBy|default_if_none:"" }}">
                </div>
                <!-- 按钮组 -->
                <div class="col-12 mb-3">
                    <div class="d-flex gap-2 justify-content-start"> {# Changed justify-content-end to start for better alignment #}
                        <button name="action" value="query" class="btn btn-info">查询</button>
                        <button name="action" value="insert" class="btn btn-success">插入</button>
                        <button name="action" value="update" class="btn btn-warning">更新</button>
                        <button name="action" value="clear" class="btn btn-secondary">清空</button>
                    </div>
                </div>
            </div>
        </div>
    </form>
    {% if messages %}
        {% for message in messages %}
            <div class="alert alert-{{ message.tags }} mt-2">{{ message }}</div>
        {% endfor %}
    {% endif %}
    {% if data %}
    <div class="table-responsive mt-3">
        <table class="table table-bordered table-striped datatable w-100">
            <thead>
                <tr>
                    <th>Name</th><th>Value</th><th>PO</th><th>RequestSNC</th>
                    <th>RequestBY</th><th>status</th><th>retcode</th><th>lastupdate</th>
                </tr>
            </thead>
            <tbody>
                {% for row in data %}
                <tr>
                    {% for col in row %}
                    <td>{{ col }}</td>
                    {% endfor %}
                </tr>
                {% endfor %}
            </tbody>
        </table>
    </div>
    {% endif %}
</div>
{% endblock %} 
from django.contrib import admin
from .models import ffLine, UdtFWCheckByLine

@admin.register(ffLine)
class ffLineAdmin(admin.ModelAdmin):
    list_display = ('ID', 'Description')
    search_fields = ('Description',)

from django.contrib import admin
from django.db.models import Q
from .models import ffLine, UdtFWCheckByLine

@admin.register(UdtFWCheckByLine)
class UdtFWCheckByLineAdmin(admin.ModelAdmin):
    list_display = ('id', 'part_family', 'fw_ver', 'line_id', 'get_line_description', 'creation_time', 'last_update', 'qty')
    list_filter = ('part_family', 'fw_ver', 'line_id')
    search_fields = ('part_family', 'fw_ver', 'reel_id')
    readonly_fields = ('creation_time', 'last_update', 'get_line_description')

    def get_line_description(self, obj):
        try:
            line = ffLine.objects.get(ID=obj.line_id)
            return line.Description
        except ffLine.DoesNotExist:
            return "N/A"
    get_line_description.short_description = 'Line'

    fieldsets = (
        (None, {
            'fields': ('part_family_id', 'part_family', 'fw_ver', 'line_id', 'get_line_description', 'looper_id', 'reel_id', 'qty')
        }),
        ('Timestamps', {
            'fields': ('creation_time', 'last_update'),
            'classes': ('collapse',)
        }),
    )

    def get_search_results(self, request, queryset, search_term):
        queryset, use_distinct = super().get_search_results(request, queryset, search_term)

        if search_term:
            line_ids = ffLine.objects.filter(Description__icontains=search_term).values_list('ID', flat=True)
            queryset |= self.model.objects.filter(line_id__in=line_ids)

        return queryset, use_distinct

{% load static %}
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta http-equiv="refresh" content="600">
    <title>{{ page_title|default:"Assembly Supermarket PCBA Inventory" }}</title>
    <script src="{% static 'js/echarts.min.js' %}"></script>
    <link rel="stylesheet" type="text/css" href="{% static 'css/fonts.css' %}">
    <style>
        body {
            background-color: #121212;
            color: #e0e0e0;
            font-family: 'Montserrat', sans-serif;
            margin: 0;
            padding: 0;
            background-image:
                radial-gradient(circle at 20% 30%, rgba(41, 98, 255, 0.03) 0%, transparent 50%),
                radial-gradient(circle at 80% 70%, rgba(255, 41, 112, 0.03) 0%, transparent 50%);
            overflow-x: hidden;
        }
        header {
            text-align: center;
            padding: 30px 0;
            background: linear-gradient(to right, rgba(30, 30, 30, 0.9), rgba(40, 40, 40, 0.9));
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
        }
        h1 {
            color: #ffffff;
            font-weight: 600;
            margin: 0;
            font-size: 32px;
            letter-spacing: 1px;
            text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
        }
        .charts-grid {
            display: flex;
            flex-wrap: wrap;
            justify-content: space-around;
            padding: 30px 10px; /* 减小左右内边距 */
            max-width: 96%;    /* 使用百分比宽度，并保留少量边缘 */
            margin: 0 auto;
        }

        .chart-container {
            width: 48%;
            min-width: 300px;
            height: 420px; /* 减小图表容器高度 */
            margin-bottom: 25px; /* 减小图表容器下外边距 */
            border-radius: 16px;
            box-sizing: border-box;
            position: relative;
            transition: all 0.3s ease;
            overflow: hidden;
            background: linear-gradient(145deg, #1e1e1e, #252525);
            border: 2px solid transparent; /* 透明边框，用于显示动画效果 */
            box-shadow:
                0 0 18px 3px rgba(74, 144, 226, 0.4), /* 外部蓝色辉光 */
                inset 0 0 10px rgba(74, 144, 226, 0.2); /* 内部蓝色辉光 */
        }

        /* 添加边框动画效果 */
        .chart-container::before {
            content: '';
            position: absolute;
            top: -2px;
            left: -2px;
            right: -2px;
            bottom: -2px;
            border: 2px solid #4a90e2;
            border-radius: 16px;
            animation: borderLight 4s linear infinite;
            z-index: 2;
            pointer-events: none;
            filter: drop-shadow(0 0 2px rgba(74, 144, 226, 0.8));
        }

        @keyframes borderLight {
            0% {
                clip-path: inset(0 0 calc(100% - 3px) 0);
                border-color: #4a90e2;
                box-shadow: 0 0 10px #4a90e2, 0 0 20px rgba(74, 144, 226, 0.5);
            }
            25% {
                clip-path: inset(0 0 0 calc(100% - 3px));
                border-color: #63b3ed;
                box-shadow: 0 0 10px #63b3ed, 0 0 20px rgba(99, 179, 237, 0.5);
            }
            50% {
                clip-path: inset(calc(100% - 3px) 0 0 0);
                border-color: #4a90e2;
                box-shadow: 0 0 10px #4a90e2, 0 0 20px rgba(74, 144, 226, 0.5);
            }
            75% {
                clip-path: inset(0 calc(100% - 3px) 0 0);
                border-color: #63b3ed;
                box-shadow: 0 0 10px #63b3ed, 0 0 20px rgba(99, 179, 237, 0.5);
            }
            100% {
                clip-path: inset(0 0 calc(100% - 3px) 0);
                border-color: #4a90e2;
                box-shadow: 0 0 10px #4a90e2, 0 0 20px rgba(74, 144, 226, 0.5);
            }
        }

        .chart-container:hover {
            transform: translateY(-5px);
            box-shadow: 0 15px 30px rgba(0, 0, 0, 0.3),
                        0 1px 5px rgba(255, 255, 255, 0.08) inset;
        }

        .chart-header {
            display: flex;
            justify-content: center; /* 修改为居中对齐标题 */
            align-items: center;
            padding: 10px 20px; /* 减小头部垂直内边距 */
            border-bottom: 1px solid rgba(255, 255, 255, 0.05);
            position: relative;
        }

        .chart-title {
            font-size: 28px; /* 增大字体 */
            font-weight: 600; /* 加粗 */
            color: #e0e0e0;
        }

        .chart-controls {
            position: absolute; /* 保持绝对定位 */
            right: 15px;
            top: 50%;
            transform: translateY(-50%);
            display: flex;
            gap: 10px;
        }

        .chart-control {
            background: none;
            border: none;
            color: #777;
            cursor: pointer;
            font-size: 20px;
            padding: 3px 5px;
            transition: color 0.2s;
        }

        .chart-control:hover {
            color: #4a90e2;
        }

        .chart-area {
            height: calc(100% - 51px); /* Approx height of chart-header */
        }
        .loading {
            display: flex;
            align-items: center;
            justify-content: center;
            height: 100%;
            width: 100%;
            position: absolute;
            top: 0;
            left: 0;
            background-color: rgba(30, 30, 30, 0.9);
            z-index: 10;
            transition: opacity 0.5s ease, visibility 0.5s ease;
        }

        .loading.hidden {
            opacity: 0;
            visibility: hidden;
        }

        .spinner {
            width: 50px;
            height: 50px;
            border: 3px solid rgba(255, 255, 255, 0.1);
            border-top-color: #4a90e2;
            border-radius: 50%;
            animation: spin 1s ease-in-out infinite;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .no-data {
            text-align: center;
            padding-top: 40%;
            color: #777;
            font-style: italic;
        }

        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(20px); }
            to { opacity: 1; transform: translateY(0); }
        }

        .chart-container {
            animation: fadeIn 0.6s ease forwards;
            opacity: 0;
        }

        .chart-container:nth-child(1) { animation-delay: 0.1s; }
        .chart-container:nth-child(2) { animation-delay: 0.2s; }
        .chart-container:nth-child(3) { animation-delay: 0.3s; }
        .chart-container:nth-child(4) { animation-delay: 0.4s; }

        @keyframes pulse {
            0% { transform: scale(1); }
            50% { transform: scale(1.05); }
            100% { transform: scale(1); }
        }

        .pulse {
            animation: pulse 1.5s ease infinite;
        }

        @media (max-width: 768px) {
            .chart-container {
                width: 100%;
            }

            h1 {
                font-size: 22px;
            }
        }
        .error-message-box {
            background-color: rgba(255, 0, 0, 0.1);
            border-left: 4px solid #ff5252;
            padding: 15px;
            margin: 20px auto;
            border-radius: 4px;
            color: #ff5252;
            max-width: 80%;
            text-align:center;
        }
    </style>
</head>
<body>
    <header>
        <h1>{{ page_title|default:"Assembly Supermarket PCBA Inventory" }}</h1>
    </header>

    {% if error_message %}
        <div style="background-color: rgba(255, 0, 0, 0.1); border-left: 4px solid #ff5252; padding: 15px; margin: 20px; border-radius: 4px;">
            <p style="color: #ff5252; margin: 0;">{{ error_message }}</p>
        </div>
    {% endif %}

    {{ pass_data_by_project|json_script:"pass-data-by-project" }}
    {{ aggregated_onhold_data|json_script:"aggregated-onhold-data" }}

    <div class="charts-grid" id="projectChartsGrid">
        <!-- Pass Status Charts will be added here by JS -->
    </div>
    
    <!-- Container for the aggregated OnHold chart -->
    <div class="charts-grid" id="onHoldChartContainer" style="margin-top: 30px;">
        <!-- Aggregated OnHold Chart will be added here by JS -->
    </div>

    <script type="text/javascript">
        document.addEventListener('DOMContentLoaded', function () {
            const passDataByProjectElement = document.getElementById('pass-data-by-project');
            const passDataByProject = passDataByProjectElement ? JSON.parse(passDataByProjectElement.textContent) : {};
            
            const aggregatedOnholdDataElement = document.getElementById('aggregated-onhold-data');
            const aggregatedOnholdData = aggregatedOnholdDataElement ? JSON.parse(aggregatedOnholdDataElement.textContent) : [];

            const projectChartsGrid = document.getElementById('projectChartsGrid');
            const onHoldChartContainer = document.getElementById('onHoldChartContainer'); // Get the new container
            let initializedCharts = [];

            const pieSliceColors = ['#5470c6', '#91cc75', '#fac858', '#ee6666', '#73c0de', '#3ba272', '#fc8452', '#9a60b4', '#ea7ccc']; // For slices within a pie
            const barColors = ['#5470c6', '#91cc75', '#fac858', '#ee6666', '#73c0de', '#3ba272', '#fc8452', '#9a60b4', '#ea7ccc']; // Using the same diverse colors for bars

            function createProjectPieOption(projectName, projectData, chartType = 'pie') {
                // 兼容 name/Name 字段，确保 tooltip 始终有部件名
                const chartData = projectData.map(item => ({
                    name: item.name || item.Name || '未知部件',
                    value: item.quantity
                }));

                const totalQuantity = projectData.reduce((sum, item) => sum + item.quantity, 0);
                let seriesConfig;

                if (chartType === 'pie') {
                    seriesConfig = {
                        type: 'pie',
                        radius: ['45%', '70%'],
                        center: ['50%', '55%'],
                        avoidLabelOverlap: true,
                        itemStyle: {
                            borderColor: '#1e1e1e',
                            borderWidth: 2
                        },
                        label: {
                            show: true,
                            formatter: '{b}: {c}\n({d}%)',
                            color: '#e0e0e0',
                            fontSize: 18,
                            fontWeight: '500',
                            textBorderColor: '#000',
                            textBorderWidth: 2,
                            textShadowColor: 'rgba(0, 0, 0, 0.5)',
                            textShadowBlur: 3
                        },
                        labelLine: {
                            lineStyle: { color: '#aaa', width: 1.5 },
                            smooth: 0.2, length: 20, length2: 30
                        },
                        emphasis: { itemStyle: { shadowBlur: 10, shadowOffsetX: 0, shadowColor: 'rgba(0, 0, 0, 0.5)' } },
                        animationType: 'scale',
                        animationEasing: 'elasticOut',
                        animationDelay: idx => Math.random() * 200,
                        universalTransition: true,
                        data: chartData
                    };
                } else {
                    seriesConfig = {
                        type: 'bar',
                        barWidth: '60%',
                        backgroundStyle: { color: 'rgba(180, 180, 180, 0.05)' },
                        label: { show: true, position: 'top', formatter: '{c}', color: '#ccc', fontSize: 16 },
                        emphasis: {
                            itemStyle: {
                                shadowBlur: 10,
                                shadowColor: 'rgba(255,255,255,0.3)'
                            }
                        },
                        animationDuration: 1000,
                        animationEasing: 'elasticOut',
                        animationDelay: idx => idx * 100,
                        universalTransition: true,
                        data: chartData.map((item, index) => ({
                            ...item,
                            itemStyle: {
                                color: barColors[index % barColors.length]
                            }
                        }))
                    };
                }

                return {
                    backgroundColor: 'transparent',
                    title: [
                        {
                            text: '',
                            left: 'center',
                            top: '5%',
                            textStyle: { color: '#ccc', fontSize: 16, fontWeight: 'normal' }
                        },
                        {
                            text: totalQuantity.toString(),
                            left: '50%',
                            top: chartType === 'pie' ? '53%' : '88%',
                            textAlign: 'center',
                            textStyle: { 
                                fontSize: 22, 
                                fontWeight: 'bold', 
                                color: '#fff', 
                                textBorderColor: '#000', 
                                textBorderWidth: 3, 
                                textShadowBlur: 2,
                                rich: { 
                                    link: { 
                                        textDecoration: 'underline', 
                                        cursor: 'pointer',
                                        color: '#fff'
                                    } 
                                }
                            },
                            // 添加链接属性，直接使用 ECharts 的链接功能
                            link: chartType === 'pie' ? `/dashboard/supermarket_pcba_detail/?project=${encodeURIComponent(projectName.replace(' - Pass', ''))}` :
                                  (projectName === 'Aggregated OnHold Status') ? `/dashboard/supermarket_pcba_detail/?project=OnHold` : undefined,
                            target: (chartType === 'pie' || projectName === 'Aggregated OnHold Status') ? '_blank' : undefined
                        }
                    ],
                    tooltip: {
                        trigger: chartType === 'pie' ? 'item' : 'axis',
                        formatter: chartType === 'pie' ? '{a} <br/>{b} : {c} ({d}%)' : '{b} : {c}',
                        backgroundColor: 'rgba(50, 50, 50, 0.9)', borderColor: '#555', borderWidth: 1,
                        textStyle: { color: '#eee', fontSize: 14 },
                        extraCssText: 'box-shadow: 0 4px 8px rgba(0, 0, 0, 0.3);'
                    },
                    legend: {
                        show: false,
                        orient: 'vertical', left: '5%', top: '15%',
                        data: chartData.map(item => item.name),
                        textStyle: { color: '#e0e0e0', fontSize: 18 },
                        itemGap: 15, itemWidth: 20, itemHeight: 14, icon: 'roundRect',
                        formatter: name => {
                            const item = chartData.find(d => d.name === name);
                            if (!item) return name;
                            const percent = totalQuantity === 0 ? 0 : ((item.value / totalQuantity) * 100).toFixed(1);
                            return `${name} (${percent}%)`;
                        }
                    },
                    grid: chartType === 'bar' ? { left: '12%', right: '5%', top: '20%', bottom: '15%', containLabel: true } : undefined,
                    xAxis: chartType === 'bar' ? { type: 'category', data: chartData.map(item => item.name), axisLine: { lineStyle: { color: '#555' } }, axisTick: { alignWithLabel: true, lineStyle: { color: '#555' } }, axisLabel: { color: '#ccc', rotate: 30, fontSize: 12 } } : undefined,
                    yAxis: chartType === 'bar' ? { type: 'value', axisLine: { show: false }, axisTick: { show: false }, splitLine: { lineStyle: { color: '#333', type: 'dashed' } }, axisLabel: { color: '#ccc', fontSize: 12 } } : undefined,
                    color: chartType === 'pie' ? pieSliceColors : barColors,
                    series: [seriesConfig]
                };
            }

            // 获取所有需要展示 Pass 状态图表的项目
            const passProjects = Object.keys(passDataByProject);

            if (passProjects.length > 0) {
                // 为每个项目创建 Pass 状态图表
                for (const project of passProjects) {
                    const projectPassData = passDataByProject[project] || [];

                    const chartContainerDiv = document.createElement('div');
                    chartContainerDiv.className = 'chart-container';

                    const loadingDiv = document.createElement('div');
                    loadingDiv.className = 'loading';
                    loadingDiv.innerHTML = '<div class="spinner"></div>';
                    chartContainerDiv.appendChild(loadingDiv);

                    const chartHeaderDiv = document.createElement('div');
                    chartHeaderDiv.className = 'chart-header';
                    
                    const chartTitleDiv = document.createElement('div');
                    chartTitleDiv.className = 'chart-title';
                    chartTitleDiv.textContent = `${project} - Pass`;
                    chartHeaderDiv.appendChild(chartTitleDiv);

                    const chartControlsDiv = document.createElement('div');
                    chartControlsDiv.className = 'chart-controls';
                    
                    const toggleViewBtn = document.createElement('button');
                    toggleViewBtn.className = 'chart-control toggle-view';
                    toggleViewBtn.title = '切换视图';
                    toggleViewBtn.textContent = '⊙'; // Pass 默认饼图
                    chartControlsDiv.appendChild(toggleViewBtn);

                    const resetZoomBtn = document.createElement('button');
                    resetZoomBtn.className = 'chart-control reset-zoom';
                    resetZoomBtn.title = '重置缩放';
                    resetZoomBtn.textContent = '↺';
                    chartControlsDiv.appendChild(resetZoomBtn);

                    chartHeaderDiv.appendChild(chartControlsDiv);
                    chartContainerDiv.appendChild(chartHeaderDiv);

                    const chartId = `project-pass-chart-${project.replace(/\s+/g, '_').replace(/[^a-zA-Z0-9-_]/g, '')}`;

                    const chartAreaDiv = document.createElement('div');
                    chartAreaDiv.id = chartId;
                    chartAreaDiv.className = 'chart-area';
                    chartContainerDiv.appendChild(chartAreaDiv);

                    projectChartsGrid.appendChild(chartContainerDiv);

                    setTimeout(() => {
                        const chartDom = document.getElementById(chartId);
                        if (chartDom) {
                            const chartInstance = echarts.init(chartDom, null, {renderer: 'svg'});
                            chartInstance._chartType = 'pie';
                            chartInstance._dataType = 'pass';
                            chartInstance._projectName = project;
                            const option = createProjectPieOption(`${project} - Pass`, projectPassData, 'pie');
                            chartInstance.setOption(option);
                            initializedCharts.push(chartInstance);
                            loadingDiv.classList.add('hidden');
                        }
                    }, 0);
                }
            } else if (aggregatedOnholdData.length === 0 && !document.querySelector('[style*="background-color: rgba(255, 0, 0, 0.1)"]')) {
                 projectChartsGrid.innerHTML = '<div class="chart-container" style="text-align:center; padding:30px; font-size:18px;">No Pass project data available to display.</div>';
            }

            // 创建聚合的 OnHold 图表
            if (aggregatedOnholdData.length > 0) {
                const chartContainerDiv = document.createElement('div');
                const containerWidth = aggregatedOnholdData.length > 5 ? "96%" : "48%";
                chartContainerDiv.className = 'chart-container';
                chartContainerDiv.style.width = containerWidth;

                const loadingDiv = document.createElement('div');
                loadingDiv.className = 'loading';
                loadingDiv.innerHTML = '<div class="spinner"></div>';
                chartContainerDiv.appendChild(loadingDiv);

                const chartHeaderDiv = document.createElement('div');
                chartHeaderDiv.className = 'chart-header';
                
                const chartTitleDiv = document.createElement('div');
                chartTitleDiv.className = 'chart-title';
                chartTitleDiv.textContent = 'Aggregated OnHold Status';
                chartHeaderDiv.appendChild(chartTitleDiv);

                const chartControlsDiv = document.createElement('div');
                chartControlsDiv.className = 'chart-controls';
                
                const toggleViewBtn = document.createElement('button');
                toggleViewBtn.className = 'chart-control toggle-view';
                toggleViewBtn.title = '切换视图';
                toggleViewBtn.textContent = '▭';
                chartControlsDiv.appendChild(toggleViewBtn);

                const resetZoomBtn = document.createElement('button');
                resetZoomBtn.className = 'chart-control reset-zoom';
                resetZoomBtn.title = '重置缩放';
                resetZoomBtn.textContent = '↺';
                chartControlsDiv.appendChild(resetZoomBtn);

                chartHeaderDiv.appendChild(chartControlsDiv);
                chartContainerDiv.appendChild(chartHeaderDiv);

                const chartId = 'aggregated-onhold-chart';
                const chartAreaDiv = document.createElement('div');
                chartAreaDiv.id = chartId;
                chartAreaDiv.className = 'chart-area';
                chartContainerDiv.appendChild(chartAreaDiv);

                onHoldChartContainer.appendChild(chartContainerDiv);

                setTimeout(() => {
                    const chartDom = document.getElementById(chartId);
                    if (chartDom) {
                        const chartInstance = echarts.init(chartDom, null, {renderer: 'svg'});
                        chartInstance._chartType = 'bar';
                        chartInstance._dataType = 'aggregated_onhold';
                        const option = createProjectPieOption('Aggregated OnHold Status', aggregatedOnholdData, 'bar');
                        chartInstance.setOption(option);
                        
                        // 为 OnHold 图表添加标题点击事件
                        chartInstance.getZr().on('click', function(params) {
                            const pointInPixel = [params.offsetX, params.offsetY];
                            if (chartInstance.containPixel('title', pointInPixel)) {
                                window.open('/dashboard/supermarket_pcba_detail/?project=OnHold', '_blank');
                            }
                        });
                        
                        initializedCharts.push(chartInstance);
                        loadingDiv.classList.add('hidden');
                    }
                }, 0);
            } else if (passProjects.length === 0 && !document.querySelector('[style*="background-color: rgba(255, 0, 0, 0.1)"]')) {
                onHoldChartContainer.innerHTML = '<div class="chart-container" style="text-align:center; padding:30px; font-size:18px;">No OnHold data available to display.</div>';
            }
            
            if (passProjects.length === 0 && aggregatedOnholdData.length === 0 && !document.querySelector('[style*="background-color: rgba(255, 0, 0, 0.1)"]')) {
                 projectChartsGrid.innerHTML = '';
                 onHoldChartContainer.innerHTML = '<div class="chart-container" style="width: 96%; text-align:center; padding:30px; font-size:18px;">No project data available to display.</div>';
            }

            // 更新控制按钮事件监听器
            document.querySelectorAll('.toggle-view').forEach(button => {
                button.addEventListener('click', function() {
                    const container = this.closest('.chart-container');
                    const chartId = container.querySelector('.chart-area').id;
                    let chartInstance = initializedCharts.find(chart => chart && chart.getDom() && chart.getDom().id === chartId);

                    if (chartInstance) {
                        let chartData;
                        const chartTitleText = container.querySelector('.chart-title').textContent;

                        if (chartInstance._dataType === 'pass') {
                            chartData = passDataByProject[chartInstance._projectName] || [];
                        } else if (chartInstance._dataType === 'aggregated_onhold') {
                            chartData = aggregatedOnholdData || [];
                        }

                        if (chartData) {
                            const newType = chartInstance._chartType === 'pie' ? 'bar' : 'pie';
                            chartInstance._chartType = newType;
                            const option = createProjectPieOption(chartTitleText, chartData, newType);
                            chartInstance.setOption(option, true);
                            this.textContent = newType === 'pie' ? '⊙' : '▭';
                            container.classList.add('pulse');
                            setTimeout(() => container.classList.remove('pulse'), 1500);
                        }
                    }
                });
            });

            document.querySelectorAll('.reset-zoom').forEach(button => {
                button.addEventListener('click', function() {
                    const container = this.closest('.chart-container');
                    const chartId = container.querySelector('.chart-area').id;
                    
                    const chartInstance = initializedCharts.find(chart => {
                        return chart && chart.getDom() && chart.getDom().id === chartId;
                    });
                    
                    if (chartInstance) {
                        chartInstance.dispatchAction({ type: 'restore' });
                        container.classList.add('pulse');
                        setTimeout(() => container.classList.remove('pulse'), 1500);
                    }
                });
            });

            window.addEventListener('resize', function () {
                initializedCharts.forEach(chart => {
                    if (chart && !chart.isDisposed()) {
                        chart.resize();
                    }
                });
            });
            
            window.addEventListener('beforeunload', () => {
                initializedCharts.forEach(chart => {
                    if (chart && !chart.isDisposed()) {
                        chart.dispose();
                    }
                });
            });

        });
    </script>
</body>
</html>
import pandas as pd
import numpy as np
from django.db import connections
from django.shortcuts import render

def get_daily_packing_production_summary_chart(request):
    Y_AXIS_MAX = 2000  # 默认值，如果没有获取到 ShiftTarget 时使用
    error_message = None

    # 1. Packing SP: 合并 Hermes/Alpha 所有数据
    packing_raw_data = []
    try:
        with connections['VolcanoReporDB'].cursor() as cursor:
            cursor.execute("EXEC usp_GetDailyPackingLineProductionSummary_rw")
            columns = [col[0] for col in cursor.description]
            for row in cursor.fetchall():
                packing_raw_data.append(dict(zip(columns, row)))
    except Exception as e:
        error_message = f'Packing SP 数据库错误：{str(e)}'

    packing_context = {
        'categories': [],
        'planned_data': [],
        'actual_data': [],
        'gap_abs_data': [],
        'unmet_target_data': [],
        'gap_sign_data': [],
        'y_axis_max': Y_AXIS_MAX,
        'page_title': 'Packing Plan VS Actual By Line',
        'data_exists': False
    }
    if packing_raw_data and not error_message:
        df = pd.DataFrame(packing_raw_data)
        desc_col = columns[1]  # 描述列
        plan_col = columns[4]  # 计划数量列
        input_col = columns[5]  # 实际输入列
        shift_target_col = columns[6] if len(columns) > 6 else None  # ShiftTarget 列，如果存在

        df[plan_col] = pd.to_numeric(df[plan_col], errors='coerce').fillna(0)
        df[input_col] = pd.to_numeric(df[input_col], errors='coerce').fillna(0)
        
        # 确保 ShiftTarget 列存在且可以转换为数字
        shift_targets = {}
        if shift_target_col and shift_target_col in df.columns:
            df[shift_target_col] = pd.to_numeric(df[shift_target_col], errors='coerce').fillna(Y_AXIS_MAX)
            # 为每个描述值计算 ShiftTarget
            shift_targets = df.groupby(desc_col)[shift_target_col].max().to_dict()
        
        summary_df = df.groupby(desc_col, as_index=False).agg(
            PlannedQuantity_Total=(plan_col, 'sum'),
            Input_Total=(input_col, 'sum')
        )
        
        # 添加 ShiftTarget 列
        summary_df['ShiftTarget'] = summary_df[desc_col].map(lambda x: shift_targets.get(x, Y_AXIS_MAX))
        
        summary_df['Actual_Production'] = summary_df['Input_Total']
        summary_df['Gap_Value'] = summary_df['Input_Total'] - summary_df['PlannedQuantity_Total']
        summary_df['Absolute_Gap'] = summary_df['Gap_Value'].abs()
        
        # 使用 ShiftTarget 计算未满目标
        summary_df['Unmet_Target'] = summary_df.apply(
            lambda row: max(0, row['ShiftTarget'] - row['Actual_Production']), axis=1
        )
        
        summary_df['Gap_Sign'] = summary_df['Gap_Value'].apply(lambda x: 'positive' if x >= 0 else 'negative')
        
        packing_context['categories'] = summary_df[desc_col].tolist()
        packing_context['planned_data'] = [int(x) for x in summary_df['PlannedQuantity_Total'].tolist()]
        packing_context['actual_data'] = [int(x) for x in summary_df['Actual_Production'].tolist()]
        packing_context['gap_abs_data'] = [int(x) for x in summary_df['Absolute_Gap'].tolist()]
        packing_context['unmet_target_data'] = [int(x) for x in summary_df['Unmet_Target'].tolist()]
        packing_context['gap_sign_data'] = summary_df['Gap_Sign'].tolist()
        # 将可能的 numpy.int64 转换为原生 int
        max_target = max(summary_df['ShiftTarget'].max(), Y_AXIS_MAX) if not summary_df.empty else Y_AXIS_MAX
        packing_context['y_axis_max'] = int(max_target)
        packing_context['data_exists'] = True

    # 2. BB SP: 三类数据
    bb_raw_data = []
    try:
        with connections['VolcanoReporDB'].cursor() as cursor:
            cursor.execute("EXEC usp_GetDailyBBLineProductionSummary_rw")
            columns_bb = [col[0] for col in cursor.description]
            for row in cursor.fetchall():
                bb_raw_data.append(dict(zip(columns_bb, row)))
    except Exception as e:
        error_message = f'BB SP 数据库错误：{str(e)}'

    bb_types = ['Hermes Charger', 'Hermes Holder', 'Alpha BB']
    bb_contexts = {}
    for bb_type in bb_types:
        context = {
            'categories': [],
            'planned_data': [],
            'actual_data': [],
            'gap_abs_data': [],
            'unmet_target_data': [],
            'gap_sign_data': [],
            'y_axis_max': Y_AXIS_MAX,
            'page_title': bb_type + ' Plan VS Actual By Line',
            'data_exists': False
        }
        if bb_raw_data and not error_message:
            df_bb = pd.DataFrame(bb_raw_data)
            type_col = columns_bb[0]  # 类型列
            desc_col = columns_bb[1]  # 描述列
            plan_col = columns_bb[4]  # 计划数量列
            input_col = columns_bb[5]  # 实际输入列
            shift_target_col = columns_bb[6] if len(columns_bb) > 6 else None  # ShiftTarget 列，如果存在
            
            df_bb[plan_col] = pd.to_numeric(df_bb[plan_col], errors='coerce').fillna(0)
            df_bb[input_col] = pd.to_numeric(df_bb[input_col], errors='coerce').fillna(0)
            
            # 确保 ShiftTarget 列存在且可以转换为数字
            shift_targets_bb = {}
            if shift_target_col and shift_target_col in df_bb.columns:
                df_bb[shift_target_col] = pd.to_numeric(df_bb[shift_target_col], errors='coerce').fillna(Y_AXIS_MAX)
                # 为每个描述值计算 ShiftTarget（按 type_col 和 desc_col 分组）
                df_filtered = df_bb[df_bb[type_col] == bb_type]
                if not df_filtered.empty:
                    shift_targets_bb = df_filtered.groupby(desc_col)[shift_target_col].max().to_dict()
            
            df_filtered = df_bb[df_bb[type_col] == bb_type]
            if not df_filtered.empty:
                summary_df = df_filtered.groupby(desc_col, as_index=False).agg(
                    PlannedQuantity_Total=(plan_col, 'sum'),
                    Input_Total=(input_col, 'sum')
                )
                
                # 添加 ShiftTarget 列
                summary_df['ShiftTarget'] = summary_df[desc_col].map(lambda x: shift_targets_bb.get(x, Y_AXIS_MAX))
                
                summary_df['Actual_Production'] = summary_df['Input_Total']
                summary_df['Gap_Value'] = summary_df['Input_Total'] - summary_df['PlannedQuantity_Total']
                summary_df['Absolute_Gap'] = summary_df['Gap_Value'].abs()
                
                # 使用 ShiftTarget 计算未满目标
                summary_df['Unmet_Target'] = summary_df.apply(
                    lambda row: max(0, row['ShiftTarget'] - row['Actual_Production']), axis=1
                )
                
                summary_df['Gap_Sign'] = summary_df['Gap_Value'].apply(lambda x: 'positive' if x >= 0 else 'negative')
                
                context['categories'] = summary_df[desc_col].tolist()
                context['planned_data'] = [int(x) for x in summary_df['PlannedQuantity_Total'].tolist()]
                context['actual_data'] = [int(x) for x in summary_df['Actual_Production'].tolist()]
                context['gap_abs_data'] = [int(x) for x in summary_df['Absolute_Gap'].tolist()]
                context['unmet_target_data'] = [int(x) for x in summary_df['Unmet_Target'].tolist()]
                context['gap_sign_data'] = summary_df['Gap_Sign'].tolist()
                # 将可能的 numpy.int64 转换为原生 int
                max_target = max(summary_df['ShiftTarget'].max(), Y_AXIS_MAX) if not summary_df.empty else Y_AXIS_MAX
                context['y_axis_max'] = int(max_target)
                context['data_exists'] = True
        bb_contexts[bb_type] = context

    final_context = {
        'error_message': error_message,
        'packing_chart': packing_context,
        'bb_charger_chart': bb_contexts['Hermes Charger'],
        'bb_holder_chart': bb_contexts['Hermes Holder'],
        'bb_alpha_chart': bb_contexts['Alpha BB'],
    }
    return render(request, 'dashboard/volcano_production_summary_chart.html', final_context)
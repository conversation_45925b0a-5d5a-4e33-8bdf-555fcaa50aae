import pandas as pd
import numpy as np
import json
from django.db import connections
from django.shortcuts import render
from django.http import JsonResponse

def ort_dashboard_view(request):
    # print('aaa')
    try:
        # Initialize empty dictionaries for chart data
        qty_chart_data = {}
        cost_chart_data = {}

        with connections['VolcanoReporDB'].cursor() as cursor:
            print("Executing stored procedure: rudpGetORTInDashBoard")
            # Try with SET NOCOUNT ON to ensure we get a result set
            cursor.execute("SET NOCOUNT ON; EXEC rudpGetORTInDashBoard")
            # print('11111')

            # Check if cursor.description is None (which would happen if no results)
            if cursor.description is None:
                print("No results returned from database (cursor.description is None)")

                # Try to execute a different query to get the data
                try:
                    print("Trying alternative query approach...")
                    # This query should match what the stored procedure is doing
                    cursor.execute("""
                    SELECT [Date], Project, [ORTDV] as 'ORT/DV', COUNT(1) as QTY, SUM([Unit Cost]) as [Unit Cost]
                    FROM (
                        SELECT
                            CONVERT(VARCHAR(7), ort.CreateTime, 23) as [Date],
                            CASE WHEN ort.Station IN ('ORTIn', 'ReliabilityIn') THEN 'ORT' ELSE 'DV' END as [ORTDV],
                            pft.Name as Project,
                            pfd.Content as [Unit Cost]
                        FROM udtORTInOutDetail ort WITH(NOLOCK)
                        INNER JOIN ffUnit u ON u.ID = ort.UnitID
                        INNER JOIN ffPart p ON p.ID = u.PartID
                        INNER JOIN luPartFamily pf ON pf.ID = p.PartFamilyID
                        INNER JOIN luPartFamilyType pft ON pft.ID = pf.PartFamilyTypeID
                        INNER JOIN ffPartFamilyDetail pfd ON pfd.PartFamilyID = pf.ID AND PartFamilyDetailDefID = 58
                        WHERE ort.CreateTime >= DATEADD(MONTH, -5, GETDATE())
                        AND ort.Station IN ('ORTIn', 'ReliabilityIn', 'DVDOEIn')
                    ) as subquery
                    WHERE [Unit Cost] IS NOT NULL
                    GROUP BY [Date], Project, [ORTDV]
                    ORDER BY [Date]
                    """)

                    # Check if we got results this time
                    if cursor.description is None:
                        print("Alternative query also returned no results")

                        # Try one more simple query as a last resort
                        try:
                            print("Trying simple test query...")
                            cursor.execute("""
                            SELECT
                                '2025-05' as [Date],
                                'Test Project' as Project,
                                'ORT' as [ORT/DV],
                                10 as QTY,
                                100.00 as [Unit Cost]
                            UNION ALL
                            SELECT
                                '2025-05' as [Date],
                                'Test Project' as Project,
                                'DV' as [ORT/DV],
                                5 as QTY,
                                50.00 as [Unit Cost]
                            """)

                            if cursor.description is None:
                                print("Even simple test query returned no results - likely a database connection issue")
                                return render(request, 'dashboard/ort_dashboard.html', {
                                    'page_title': 'ORT Dashboard',
                                    'error_message': "Database connection issue. Unable to retrieve any data.",
                                    'qty_chart_data': json.dumps({}),
                                    'cost_chart_data': json.dumps({})
                                })
                        except Exception as test_error:
                            print(f"Simple test query failed: {test_error}")

                        return render(request, 'dashboard/ort_dashboard.html', {
                            'page_title': 'ORT Dashboard',
                            'error_message': "The stored procedure 'rudpGetORTInDashBoard' is not returning a result set. This may be due to a SQL Server compatibility issue with the stored procedure. Please check the stored procedure to ensure it returns a result set properly.",
                            'qty_chart_data': json.dumps({}),
                            'cost_chart_data': json.dumps({})
                        })
                except Exception as query_error:
                    print(f"Alternative query failed: {query_error}")
                    return render(request, 'dashboard/ort_dashboard.html', {
                        'page_title': 'ORT Dashboard',
                        'error_message': f"Database query failed: {query_error}",
                        'qty_chart_data': json.dumps({}),
                        'cost_chart_data': json.dumps({})
                    })

            # print(f"Cursor description: {cursor.description}")
            columns = [col[0] for col in cursor.description]
            # print(f"Columns: {columns}")
            rows = cursor.fetchall()
            # print(f"Number of rows returned: {len(rows) if rows else 0}")

            # Check if no rows were returned
            if not rows:
                print("No rows returned from database")
                return render(request, 'dashboard/ort_dashboard.html', {
                    'page_title': 'ORT Dashboard',
                    'error_message': "No data available",
                    'qty_chart_data': json.dumps({}),
                    'cost_chart_data': json.dumps({})
                })

            data = [dict(zip(columns, row)) for row in rows]
            # print(f"First row data sample: {data[0] if data else 'No data'}")

        df = pd.DataFrame(data)
        # print("DataFrame info:")
        # print(df.info())
        # print("DataFrame head:")
        # print(df.head())

        # Check if dataframe is empty
        if df.empty:
            print("DataFrame is empty - no data returned from database")
            return render(request, 'dashboard/ort_dashboard.html', {
                'page_title': 'ORT Dashboard',
                'error_message': "No data available",
                'qty_chart_data': json.dumps({}),
                'cost_chart_data': json.dumps({})
            })

        # Print all column names for debugging
        print(f"Actual DataFrame columns: {df.columns.tolist()}")

        # Verify required columns exist
        required_columns = ['Date', 'Project', 'ORT/DV', 'QTY', 'Unit Cost']

        # Check for alternative column names (e.g., 'ORT_DV' instead of 'ORT/DV')
        column_alternatives = {
            'ORT/DV': ['ORT/DV', 'ORT_DV', 'ORTDV', 'Type'],
            'QTY': ['QTY', 'Quantity', 'Qty'],
            'Unit Cost': ['Unit Cost', 'UnitCost', 'Cost']
        }

        # Map actual column names to required column names
        column_mapping = {}
        for req_col in required_columns:
            if req_col in df.columns:
                column_mapping[req_col] = req_col
            elif req_col in column_alternatives:
                for alt_col in column_alternatives[req_col]:
                    if alt_col in df.columns:
                        column_mapping[req_col] = alt_col
                        break

        # Rename columns if needed
        if len(column_mapping) < len(required_columns):
            print(f"Column mapping incomplete: {column_mapping}")
        else:
            print(f"Using column mapping: {column_mapping}")
            # Rename columns only if the mapping is different
            rename_dict = {v: k for k, v in column_mapping.items() if k != v}
            if rename_dict:
                df = df.rename(columns=rename_dict)
                print(f"Renamed columns: {rename_dict}")
                print(f"New DataFrame columns: {df.columns.tolist()}")

        # Ensure ORT/DV column is string type
        if 'ORT/DV' in df.columns:
            # Check data types
            print(f"Data types before conversion: {df.dtypes}")

            # Convert ORT/DV to string
            df['ORT/DV'] = df['ORT/DV'].astype(str)

            # Check unique values in ORT/DV column
            print(f"Unique values in ORT/DV column: {df['ORT/DV'].unique()}")

            print(f"Data types after conversion: {df.dtypes}")

        # Check for missing columns after mapping
        missing_columns = [col for col in required_columns if col not in df.columns]
        if missing_columns:
            print(f"Missing required columns: {missing_columns}")
            return render(request, 'dashboard/ort_dashboard.html', {
                'page_title': 'ORT Dashboard',
                'error_message': f"Missing required data columns: {', '.join(missing_columns)}",
                'qty_chart_data': json.dumps({}),
                'cost_chart_data': json.dumps({})
            })

        # 将 Date 列转换为月份格式，例如 "2024-12" -> "Dec 2024"
        try:
            df['Date'] = pd.to_datetime(df['Date'], format='%Y-%m').dt.strftime('%b %Y')
        except Exception as e:
            print(f"Error converting date format: {e}")
            return render(request, 'dashboard/ort_dashboard.html', {
                'page_title': 'ORT Dashboard',
                'error_message': f"Error processing date format: {e}",
                'qty_chart_data': json.dumps({}),
                'cost_chart_data': json.dumps({})
            })

        # 聚合数据用于图表
        try:
            # Check for actual ORT/DV values in the database
            if 'ORT/DV' in df.columns:
                ort_dv_values = df['ORT/DV'].unique()
                print(f"Actual ORT/DV values in database: {ort_dv_values}")

                # Determine which values to use for ORT and DV
                ort_values = [val for val in ort_dv_values if 'ORT' in val.upper()]
                dv_values = [val for val in ort_dv_values if 'DV' in val.upper()]

                print(f"Using these values for ORT: {ort_values}")
                print(f"Using these values for DV: {dv_values}")

                if not ort_values:
                    print("WARNING: No ORT values found in the data")
                if not dv_values:
                    print("WARNING: No DV values found in the data")

            # 第一个图表：ORT/DV QTY
            for date in df['Date'].unique():
                qty_chart_data[date] = {}
                for project in df['Project'].unique():
                    try:
                        # Create masks for ORT and DV values
                        ort_mask = (df['Date'] == date) & (df['Project'] == project) & df['ORT/DV'].apply(lambda x: any(ort_val in x.upper() for ort_val in ['ORT']))
                        dv_mask = (df['Date'] == date) & (df['Project'] == project) & df['ORT/DV'].apply(lambda x: any(dv_val in x.upper() for dv_val in ['DV']))

                        ort_qty = df[ort_mask]['QTY'].sum()
                        dv_qty = df[dv_mask]['QTY'].sum()

                        print(f"Date: {date}, Project: {project}, ORT QTY: {ort_qty}, DV QTY: {dv_qty}")

                        # Convert to float, handling NaN and None values
                        ort_qty = float(ort_qty) if pd.notna(ort_qty) else 0
                        dv_qty = float(dv_qty) if pd.notna(dv_qty) else 0

                        qty_chart_data[date][project] = {
                            'ORT': ort_qty,
                            'DV': dv_qty
                        }
                    except Exception as e:
                        print(f"Error processing QTY data for date={date}, project={project}: {e}")
                        qty_chart_data[date][project] = {'ORT': 0, 'DV': 0}

            # 第二个图表：ORT/DV Unit Cost
            for date in df['Date'].unique():
                cost_chart_data[date] = {}
                for project in df['Project'].unique():
                    try:
                        # Create masks for ORT and DV values
                        ort_mask = (df['Date'] == date) & (df['Project'] == project) & df['ORT/DV'].apply(lambda x: any(ort_val in x.upper() for ort_val in ['ORT']))
                        dv_mask = (df['Date'] == date) & (df['Project'] == project) & df['ORT/DV'].apply(lambda x: any(dv_val in x.upper() for dv_val in ['DV']))

                        ort_cost = df[ort_mask]['Unit Cost'].sum()
                        dv_cost = df[dv_mask]['Unit Cost'].sum()

                        print(f"Date: {date}, Project: {project}, ORT Cost: {ort_cost}, DV Cost: {dv_cost}")

                        # Convert to float, handling NaN and None values
                        ort_cost = float(ort_cost) if pd.notna(ort_cost) else 0
                        dv_cost = float(dv_cost) if pd.notna(dv_cost) else 0

                        cost_chart_data[date][project] = {
                            'ORT': ort_cost,
                            'DV': dv_cost
                        }
                    except Exception as e:
                        print(f"Error processing Cost data for date={date}, project={project}: {e}")
                        cost_chart_data[date][project] = {'ORT': 0, 'DV': 0}
        except Exception as e:
            print(f"Error aggregating data: {e}")
            return render(request, 'dashboard/ort_dashboard.html', {
                'page_title': 'ORT Dashboard',
                'error_message': f"Error processing data: {e}",
                'qty_chart_data': json.dumps({}),
                'cost_chart_data': json.dumps({})
            })

        # Check if chart data is empty
        if not qty_chart_data or not any(qty_chart_data.values()):
            print("WARNING: qty_chart_data is empty after processing")

        if not cost_chart_data or not any(cost_chart_data.values()):
            print("WARNING: cost_chart_data is empty after processing")

        # Print final chart data structure
        # print(f"Final qty_chart_data structure: {qty_chart_data}")
        # print(f"Final cost_chart_data structure: {cost_chart_data}")

        context = {
            'page_title': 'ORT Dashboard',
            'qty_chart_data': json.dumps(qty_chart_data),
            'cost_chart_data': json.dumps(cost_chart_data),
        }
        return render(request, 'dashboard/ort_chart.html', context) # 注意这里模板名称已更改

    except Exception as e:
        print(f"Error in ort_dashboard_view: {e}")
        context = {
            'page_title': 'ORT Dashboard',
            'error_message': f"Error fetching data: {e}",
            'qty_chart_data': json.dumps({}),
            'cost_chart_data': json.dumps({})
        }
        return render(request, 'dashboard/ort_chart.html', context) # 注意这里模板名称已更改
